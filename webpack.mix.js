let mix = require('laravel-mix');

/*
 |--------------------------------------------------------------------------
 | Mix Asset Management
 |--------------------------------------------------------------------------
 |
 | Mix provides a clean, fluent API for defining some Webpack build steps
 | for your Laravel application. By default, we are compiling the Sass
 | file for the application as well as bundling up all the JS files.
 |
 */

mix
    .js('resources/assets/js/app.js', 'public/js')

    // editor js files
    .js('resources/assets/js/editor/parties.js', 'public/js/editor')
    .js('resources/assets/js/editor/title.js', 'public/js/editor')
    .js('resources/assets/js/editor/body.js', 'public/js/editor')
    .js('resources/assets/js/editor/article_header.js', 'public/js/editor')
    .js('resources/assets/js/editor/article_body.js', 'public/js/editor')
    .js('resources/assets/js/editor/signees.js', 'public/js/editor')
    .js('resources/assets/js/editor/attachments.js', 'public/js/editor')
    .js('resources/assets/js/editor/legal_remedy.js', 'public/js/editor')
    .js('resources/assets/js/editor/recipients.js', 'public/js/editor')
    .js('resources/assets/js/editor/editor.js', 'public/js/editor')
    .js('resources/assets/js/editor/spellcheck.js', 'public/js/editor')

    // document js files
    .js('resources/assets/js/documents/common.js', 'public/js/documents/')
    .js('resources/assets/js/documents/bozovic/WorkContract/parties.js', 'public/js/documents/bozovic/WorkContract/')
    .js('resources/assets/js/documents/bozovic/WorkContract/type.js', 'public/js/documents/bozovic/WorkContract/')
    .js('resources/assets/js/documents/bozovic/WorkContract/general_provisions.js', 'public/js/documents/bozovic/WorkContract/')
    .js('resources/assets/js/documents/bozovic/WorkContract/additional_provisions.js', 'public/js/documents/bozovic/WorkContract/')
    .js('resources/assets/js/documents/bozovic/WorkContract/final_provisions.js', 'public/js/documents/bozovic/WorkContract/')
	//{addEntryPlaceholder}

    .css('resources/assets/css/app.css', 'public/css')
    .css('resources/assets/css/preview.css', 'public/css')
    .css('resources/assets/css/editor.css', 'public/css')
    .version();

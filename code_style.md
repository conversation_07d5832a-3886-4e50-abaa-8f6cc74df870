When writing code you should strictly adhere to these rules, this is VERY IMPORTANT:

- the user facing language of this app is Croatian, but the code itself is in English language
- in PHP, use snake_case for variable names. in JS, you can use camelCase
- don't leave instructional code comments such as "THIS IS THE NEW PART", "The changes begin here" etc.
- code comments should always look like a developer wrote them for himself (not <PERSON><PERSON> for developer)
- avoid redundant comments, don’t add comments that just repeat what the code already says clearly
- inline code comments should generally not be capitalized // this is a comment
- block comments and doc blocks can be capitalized /* This is a comment */
- avoid using "numbering" inside code comments for code blocks, for example (// 1. something, // 2. something else, etc.)
- when replying with code, don't use the "git diff" format, just write full code that i can copy-paste into a file
<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines contain the default error messages used by
    | the validator class. Some of these rules have multiple versions such
    | as the size rules. Feel free to tweak each of these messages here.
    |
    */

    'accepted'             => 'Polje ":attribute" mora biti prihvaćeno.',
    'active_url'           => 'Polje ":attribute" nije dobar URL.',
    'after'                => 'Polje ":attribute" mora biti datum nakon :date.',
    'after_or_equal'       => 'Polje ":attribute" mora biti datum nakon ili jednak :date.',
    'alpha'                => 'Polje ":attribute" može samo sadržavati slova.',
    'alpha_dash'           => 'Polje ":attribute" može samo sadržavati slova, brojeve, crtice i podvlake.',
    'alpha_num'            => 'Polje ":attribute" može samo sadržavati slova i brojeve.',
    'array'                => 'Polje ":attribute" mora biti skup.',
    'before'               => 'Polje ":attribute" mora biti datum prije :date.',
    'before_or_equal'      => 'Polje ":attribute" mora biti datum prije ili jednak to :date.',
    'between'              => [
        'numeric' => 'Polje ":attribute" mora biti između :min i :max.',
        'file'    => 'Polje ":attribute" mora biti između :min i :max kilobajta.',
        'string'  => 'Polje ":attribute" mora biti između :min i :max znakova.',
        'array'   => 'Polje ":attribute" mora imati između :min i :max članova.',
    ],
    'boolean'              => 'Polje ":attribute" mora biti točno ili netočno.',
    'confirmed'            => 'Polje ":attribute" potvrda ne odgovara.',
    'date'                 => 'Polje ":attribute" nije dobro definiran datum.',
    'date_format'          => 'Polje ":attribute" ne odgovara formatu :format.',
    'different'            => 'Poljei ":attribute" i :other moraju biti različite.',
    'digits'               => 'Polje ":attribute" mora biti :digits znamenki.',
    'digits_between'       => 'Polje ":attribute" mora biti između :min i :max znamenki.',
    'dimensions'           => 'Polje ":attribute" nema prihvatljive dimenzije za sliku.',
    'distinct'             => 'Polje ":attribute" ima dupliciranu vrijednost.',
    'email'                => 'Polje ":attribute" mora biti ispravna adresa e-pošte.',
    'exists'               => 'Odabrana vrijednost ":attribute" nije dobra.',
    'file'                 => 'Polje ":attribute" mora biti datoteka.',
    'filled'               => 'Polje ":attribute" mora imati vrijednost.',
    'gt'                   => [
        'numeric' => 'Polje ":attribute" mora biti veća od :value.',
        'file'    => 'Polje ":attribute" mora biti veća od :value kilobajta.',
        'string'  => 'Polje ":attribute" mora biti veća od :value znakova.',
        'array'   => 'Polje ":attribute" mora imati više od :value članova.',
    ],
    'gte'                  => [
        'numeric' => 'Polje ":attribute" mora biti najmanje :value.',
        'file'    => 'Polje ":attribute" mora imati najmanje :value kilobajta.',
        'string'  => 'Polje ":attribute" mora imati najmanje :value znakova.',
        'array'   => 'Polje ":attribute" mora imati najmanje :value članova.',
    ],
    'image'                => 'Polje ":attribute" mora biti slika.',
    'in'                   => 'Označena vrijednost ":attribute" nije dobra.',
    'in_array'             => 'Polje ":attribute" ne postoji u :other.',
    'integer'              => 'Polje ":attribute" mora biti brojčano.',
    'ip'                   => 'Polje ":attribute" mora biti IP adresa.',
    'ipv4'                 => 'Polje ":attribute" mora biti IPv4 adresa.',
    'ipv6'                 => 'Polje ":attribute" mora biti IPv6 adresa.',
    'json'                 => 'Polje ":attribute" mora biti JSON string.',
    'lt'                   => [
        'numeric' => 'Polje ":attribute" mora biti manje od :value.',
        'file'    => 'Polje ":attribute" mora imati manje od :value kilobajta.',
        'string'  => 'Polje ":attribute" mora imati manje od :value znakova.',
        'array'   => 'Polje ":attribute" mora imati manje od :value članova.',
    ],
    'lte'                  => [
        'numeric' => 'Polje ":attribute" mora biti manje ili jednako :value.',
        'file'    => 'Polje ":attribute" mora biti do :value kilobajta.',
        'string'  => 'Polje ":attribute" mora imati do :value znakova.',
        'array'   => 'Polje ":attribute" ne smije imati više od :value članova.',
    ],
    'max'                  => [
        'numeric' => 'Polje ":attribute" ne smije biti veće od :max.',
        'file'    => 'Polje ":attribute" ne smije imati više od :max kilobajta.',
        'string'  => 'Polje ":attribute" ne smije imati više od :max znakova.',
        'array'   => 'Polje ":attribute" ne smije imati više od :max članova.',
    ],
    'mimes'                => 'Polje ":attribute" mora biti tipa: :values.',
    'mimetypes'            => 'Polje ":attribute" mora biti tipa: :values.',
    'min'                  => [
        'numeric' => 'Polje ":attribute" mora biti barem :min.',
        'file'    => 'Polje ":attribute" mora imati barem :min kilobajta.',
        'string'  => 'Polje ":attribute" mora sadržavati barem :min znakova.',
        'array'   => 'Polje ":attribute" mora sadržavati barem :min članova.',
    ],
    'not_in'               => 'Označena vrijednost ":attribute" nije dobra.',
    'not_regex'            => 'Polje formata ":attribute" nije dobro.',
    'numeric'              => 'Polje ":attribute" mora biti brojčana.',
    'present'              => 'Polje ":attribute" mora postojati.',
    'regex'                => 'Polje formata ":attribute" nije dobra.',
    'required'             => 'Polje ":attribute" je obavezno.',
    'required_if'          => 'Polje ":attribute" je obavezno kad :other je :value.',
    'required_unless'      => 'Polje ":attribute" je obavezno osim ako :other se nalazi u :values.',
    'required_with'        => 'Polje ":attribute" je obavezno kad :values postoji.',
    'required_with_all'    => 'Polje ":attribute" je obavezno kad :values postoji.',
    'required_without'     => 'Polje ":attribute" je obavezno kad :values ne postoji.',
    'required_without_all' => 'Polje ":attribute" je obavezno kad nijedna od vrijednosti :values ne postoji.',
    'same'                 => 'Polje ":attribute" i :other se moraju podudarati.',
    'size'                 => [
        'numeric' => 'Polje ":attribute" mora biti :size.',
        'file'    => 'Polje ":attribute" mora biti :size kilobajta.',
        'string'  => 'Polje ":attribute" mora biti :size znakova.',
        'array'   => 'Polje ":attribute" se mora sastojati od :size članova.',
    ],
    'string'               => 'Polje ":attribute" mora biti tekst.',
    'timezone'             => 'Polje ":attribute" mora biti dobra vremenska zona.',
    'unique'               => 'Polje ":attribute" već postoji u bazi.',
    'uploaded'             => 'Prijenos ":attribute" nije bio uspješan.',
    'url'                  => 'Polje formata ":attribute" nije dobra.',
    'indisposable'         => 'Nažalost, ne možete izraditi račun koristeći privremenu adresu e-pošte.',

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | Here you may specify custom validation messages for attributes using the
    | convention "attribute.rule" to name the lines. This makes it quick to
    | specify a specific custom language line for a given attribute rule.
    |
    */

    'custom' => [
        'attribute-name' => [
            'rule-name' => 'custom-message',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Attributes
    |--------------------------------------------------------------------------
    |
    | The following language lines are used to swap attribute place-holders
    | with something more reader friendly such as E-Mail Address instead
    | of "email". This simply helps us make messages a little cleaner.
    |
    */

    'attributes' => [
    	'password' => 'lozinka',
	    'email' => 'adresa e-pošte'
    ],

];

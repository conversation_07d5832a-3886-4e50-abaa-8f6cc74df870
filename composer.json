{"name": "laravel/laravel", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "type": "project", "require": {"php": "^8.1", "ext-curl": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-json": "*", "ext-libxml": "*", "ext-zip": "*", "apavliukov/laravel-email-smtp-validation": "^1.0", "doctrine/dbal": "^3.0.0", "elasticsearch/elasticsearch": "^7.12", "genealabs/laravel-model-caching": "^0.13", "guzzlehttp/guzzle": "^7.0.1", "intervention/image": "^2.5", "jaybizzle/crawler-detect": "*", "laravel/framework": "^10.0", "laravel/telescope": "^5.1", "laravel/tinker": "^2.5", "laravel/ui": "^4.0", "misterspelik/laravel-pdf": "^2.1", "mobiledetect/mobiledetectlib": "^3.74", "mtvs/eloquent-hashids": "^3.1.0", "predis/predis": "^1.1", "propaganistas/laravel-disposable-email": "^2.1", "smalot/pdfparser": "^2.10", "soundasleep/html2text": "^2.1", "spatie/browsershot": "^3.58", "spatie/laravel-activitylog": "^4.7", "spatie/laravel-honeypot": "^4.0.0", "spatie/laravel-html": "^3.5", "spatie/laravel-responsecache": "^7.4", "spatie/laravel-sitemap": "^6.1", "spatie/pdf-to-text": "^1.54", "stancl/tenancy": "^3.9", "tijsverkoyen/css-to-inline-styles": "^2.3", "timehunter/laravel-google-recaptcha-v3": "~2.5", "voku/portable-utf8": "^6.0", "voku/simple_html_dom": "^4.8", "yajra/laravel-datatables-oracle": "^10.3.0"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.5", "barryvdh/laravel-ide-helper": "^2.9.0", "brianium/paratest": "^6.11", "fakerphp/faker": "^1.9.1", "filp/whoops": "^2.9.2", "kitloong/laravel-migrations-generator": "^6.8", "laravel/dusk": "^7.0", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.2", "nunomaduro/collision": "^6.1", "phpunit/phpunit": "^9.3.3", "spatie/laravel-ignition": "^2.0", "staudenmeir/dusk-updater": "^1.1"}, "autoload": {"classmap": ["database/seeds", "database/factories"], "psr-4": {"App\\": "app/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "extra": {"laravel": {"dont-discover": []}}, "scripts": {"post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate"], "post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover"]}, "config": {"preferred-install": "dist", "sort-packages": true, "optimize-autoloader": true}, "minimum-stability": "stable", "prefer-stable": true}
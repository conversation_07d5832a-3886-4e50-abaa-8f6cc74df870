$(document).ready(function () {

    function bindRemoveAttachment() {
        $('.remove-attachment').off('click').on('click', function () {
            $(this).closest('tr').remove();
            updateAttachmentsCount();
        });
    }

    function bindAddAttachment() {
        $('#add-attachment').off('click').on('click', function () {
            $('#attachments-table tbody').append(`
                <tr class="attachment-data">
                    <td>
                        <input type="text" placeholder="Upiši naziv priloženog dokumenta..." class="form-control attachment-label"></input>
                    </td>
                  
                    <td class="text-center">
                        <a class="btn btn-danger btn-sm remove-attachment"><i class="fa fa-trash"></i></a>
                    </td>
                </tr>`);

            bindAttachmentLabelChange();
            updateAttachmentsCount();
            bindRemoveAttachment();
        });
    }

    function updateAttachmentsCount() {
        // count the number of non-empty attachment-label inputs
        let count = $('.attachment-label').filter(function() {
            return $(this).val().length > 0
        }).length;

        if(count > 0) {
            $('#attachments-count').text('(' + count + ')');
        } else {
            $('#attachments-count').text('');
        }
    }

    // on attachment-label typing, update attachments-count
    function bindAttachmentLabelChange() {
        $('.attachment-label').off('input').on('input', function() {
            updateAttachmentsCount();
        })
    }

    bindAttachmentLabelChange();
    bindRemoveAttachment();
    bindAddAttachment();
});

$(document).ready(function () {

    let config = {
        language: 'hr',
        placeholder: "Ovdje upiši tekst dokumenta...",
        selector: '.editable-segment[data-type=body]',
        fixed_toolbar_container: '#editor-toolbar',
        menubar: false,
        setup: (editor) => {

            editor.on('NodeChange', (e) => {

                let elementBody = $(editor.getElement());

                // wrap all tables that are not wrapped in a div with the class .overflow-x-auto
                elementBody.find('table').each(function() {
                    if (!$(this).parent().hasClass('overflow-x-auto')) {
                        $(this).wrap('<div class="overflow-x-auto"></div>');
                    }
                });

                // Remove empty mce_caret spans
                $.each(editor.dom.select('span[id="_mce_caret"]'), function () {
                    const $span = $(this);

                    // Check if the span is empty or contains only invisible characters and remove it if so
                    if (!$span.text().trim()) {
                        $span.remove();
                    }
                });

                // Handle tutorial tooltips (only on desktop)
                if (window.matchMedia('(min-width: 1024px)').matches) {
                    // Remove any orphaned tooltip elements
                    $('.tooltip').remove();

                    $.each(editor.dom.select('span[data-toggle="tooltip"]'), function () {
                        const $span = $(this);

                        // Ensure the tooltip is always disposed of before being re-initialized
                        $span.tooltip('dispose').tooltip({ html: true });

                        // Check if the span is empty or contains only invisible characters and remove it if so
                        if (!$span.text().trim()) {
                            $span.remove();
                            return; // Skip the rest of the logic since this span is removed
                        }

                        // Get the original content if not already set
                        if (!$span.data('original-content')) {
                            $span.data('original-content', $span.html());
                        }

                        // Check if the content has changed
                        if ($span.data('original-content') !== $span.html()) {
                            // Save the current selection range
                            const selection = window.getSelection();
                            const range = selection.getRangeAt(0);
                            const startContainer = range.startContainer;
                            const startOffset = range.startOffset;

                            // Unwrap the span but keep the inner content intact
                            $span.contents().unwrap();

                            // Restore the caret position
                            range.setStart(startContainer, startOffset);
                            range.collapse(true);
                            selection.removeAllRanges();
                            selection.addRange(range);
                        }

                        // Enable tooltip on hover or when the caret is inside the span
                        $span.on('mouseenter focus click', function () {
                            $(this).tooltip('show');
                        }).on('mouseleave blur', function () {
                            const selection = window.getSelection();
                            const range = selection.rangeCount > 0 ? selection.getRangeAt(0) : null;

                            // Only hide the tooltip if the caret is not inside the span
                            if (!range || !$(this).is($(range.commonAncestorContainer).closest('span[data-toggle="tooltip"]'))) {
                                $(this).tooltip('hide');
                            }
                        }).on('click', function (e) {    // Select the entire tooltip span on click
                            const selection = window.getSelection();
                            if (selection.isCollapsed) {  // Check if there is no selection (collapsed selection means no text is selected)
                                const range = document.createRange();
                                range.selectNodeContents(this);
                                selection.removeAllRanges();
                                selection.addRange(range);
                            }
                        });
                    });
                }
            });

            // Observer to dispose tooltips on DOM mutations (span removal, etc.)
            if (window.matchMedia('(min-width: 1024px)').matches) {
                const observer = new MutationObserver(function (mutationsList) {
                    for (let mutation of mutationsList) {
                        if (mutation.removedNodes.length > 0) {
                            // Remove any orphaned tooltip elements
                            $('.tooltip').remove();
                        }
                    }
                });

                observer.observe(document.getElementById('editor'), { childList: true, subtree: true });
            }

            // Add keydown event handler for backspace and delete keys
            editor.on('keydown', function(e) {
                // Only on desktop (where tooltips are enabled)
                if (window.matchMedia('(min-width: 1024px)').matches) {
                    if (e.which === 8 || e.which === 46) {
                        const selection = editor.selection;
                        const node = selection.getNode();
                        if (node.nodeName === 'SPAN' && node.hasAttribute('data-toggle')) {
                            setTimeout(() => {
                                if (!document.body.contains(node)) {
                                    $(node).tooltip('dispose');

                                    // Remove formatting after span is deleted
                                    editor.execCommand('RemoveFormat');
                                }
                            }, 0);
                        }
                    }
                }
            });
        },
        plugins: ['lists', 'save', 'table'],
        visual: false,
        inline: true,
        paste_as_text: true,
        paste_block_drop: true,
        smart_paste: false,
        paste_preprocess : function(editor, args) {
            let content = args.content;

            content = content.replaceAll(/&nbsp;/g, '');
            content = content.replaceAll('\u00A0', '');

            args.content = content;
        },
        toolbar: 'navigationLabel bold italic underline backcolor removeformat alignleft aligncenter alignright numlist bullist indent outdent undo redo cancel',
        extended_valid_elements: 'span[class|style]',
        object_resizing : false,
        color_default_background: '#FBEEB8',
    };

    tinymce.init(config);

    // Add initialization to window function
    window.initBody = function() {
        tinymce.init(config);
    };
});

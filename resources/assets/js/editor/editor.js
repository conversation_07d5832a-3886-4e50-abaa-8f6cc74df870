// Remember which segment the add article button was clicked on
let addArticleSegmentContainer = null;

// Undo delete-article function
let lastDeletedArticle = null;  // holds segmentHtml, as well as its precedingSegment

// Track if example has article templates
let articleTemplatesExist = false;

$(document).ready(function () {
    // disable global onError listener
    window.onerror = () => {};

    // Editor tutorial
    const shouldShowTutorial = !isMobile();
    if (shouldShowTutorial && $('#tutorialModal').length) {
        $('#tutorialModal').modal('show');
        $.ajax({
            url: '/user/editor/tutorial',
            type: 'POST',
            data: {
                _token: $("[name='_token']").val(),
            }
        });
    }

    // Show navigation confirmation dialog
    bindNavigationConfirmationDialog();

    // Event handlers
    $('#insert-empty-article').on('click', () => insertArticle(generateNewArticle()));

    $('#save-and-exit').click(saveAndExitHandler);
    $('#save-and-continue').click(saveAndContinueHandler);

    // CTRL+S is pressed
    $(document).on('keydown', function(e) {
        if(e.ctrlKey && e.keyCode === 83) {
            saveAndContinueHandler(e);
        }
    });

    $('#restore-article').on('click', restoreLastDeletedArticle);
    $('#close-alert-widget, #close-alert-widget span').on('click', () => toggleArticleDeletedAlert('hide'));
    $('#search-articles-table tbody').on('click', 'i.details-control', toggleArticleDetails).on('click', 'button', insertArticleTemplate);

    $('#addArticleModal').on('shown.bs.modal', function () {
        articleTemplatesExist = true;
        $('#addArticleModal input[type=search]').focus();
    });

    // Initial function calls
    bindSegmentToolbar();
    bindArticleReferences();
    bindOnClickArticleListItemFocusEditor();
    toggleDeleteArticleVisibility();
    disableMoveUpOnFirstArticle();

    // Add event listener for disabling copy and cut clipboard buffer
    ['copy', 'cut'].forEach((event) => {
        document.addEventListener(event, (e) => {
            e.clipboardData.setData('text/plain', '');
            e.preventDefault();
        });
    });
});

// Helper functions
function isMobile() {
    return $(window).width() < 480;
}

function generateRandomString(length = 7, characterSet = "ABCDEFGHIJKLMNOPQRSTUVWXTZabcdefghiklmnopqrstuvwxyz") {
    return Array.from({ length }, () => characterSet[Math.floor(Math.random() * characterSet.length)]).join('');
}

function toggleDeleteArticleVisibility() {
    if ($('.delete-article').length === 1) {
        $('.delete-article').hide();
    } else {
        $('.delete-article').show();
    }
}

function isArticleSegmentEmpty(content) {
    if (!content || content === "&nbsp;") {
        return true;
    }

    const contentHtml = $.parseHTML(content);
    const firstElement = $(contentHtml).first();

    return firstElement.length === 1 && firstElement.children().length === 1 && firstElement.children().first().text().trim() === '';
}

function generateNewArticle(title, body) {

    let newArticleClass = generateRandomString(),
        newArticle = (
            '<div class="segment-container mb-2"> ' +
            '<div class="segment-toolbar text-right mb-1"> ' +
            '<div class="segment-toolbar-options"> <div style="display: inline-block"> <ul> <li><a style="margin-right:4px;" class="btn btn-xs move-down" title="Pomakni dolje"><i class="fa fa-arrow-down"></i></a></li><li><a class="btn btn-xs move-up" title="Pomakni gore"><i class="fa fa-arrow-up"></i></a></li>  <li><a class="btn btn-xs btn-danger delete-article" title="Ukloni ovaj članak"><i class="fa fa-trash"></i></a></li> <li><a class="btn btn-xs btn-primary add-article"  title="Dodaj novi članak ispod"><i class="fa fa-plus"></i></a></li> </ul> </div> </div> ' +
            '</div> ' +
            '<div class="editable-segment" data-type="article"> <ol class="article-list"><li><p class="article-header '+newArticleClass+'">' + (title ? title : '<br>') + '</p></li></ol> <div class="article-body '+newArticleClass+'">' + (body ? body : '<ol class="paragraph-list"><li><br></li></ol>') + '</div> </div> ' +
            '</div>'
        );

    return {
        selector: newArticleClass,
        article: newArticle
    };
}

function insertArticle(articleData) {

    let newArticleContainer = $(articleData.article).insertAfter(addArticleSegmentContainer);

    tinymce.init({
        ...window.articleHeaderConfig,
        selector: '.article-header.'+articleData.selector
    });

    tinymce.init({
        ...window.articleBodyConfig,
        selector: '.article-body.'+articleData.selector
    });

    bindSegmentToolbar();
    updateSegmentNavigationLabels();
    updateArticleReferences();
    bindOnClickArticleListItemFocusEditor();
    toggleDeleteArticleVisibility();
    disableMoveUpOnFirstArticle();
    updateArticleCounters();

    // scroll to new article
    $([document.documentElement, document.body]).animate({
        scrollTop: newArticleContainer.offset().top - 100
    }, 500);

    // flash
    newArticleContainer.css({opacity: 0}).animate({opacity: 1}, 700 );
}

function bindSegmentToolbarToggle() {
    $('.segment-container').on('mouseover', function(){
        $(this).find('.segment-toolbar-options').show();
    }).on('mouseleave', function(){
        $(this).find('.segment-toolbar-options').hide();
    });
}


function bindAddArticleAction() {
    $('.add-article').off('click').on('click', function(e){
        e.preventDefault();
        addArticleSegmentContainer = $(this).closest('.segment-container');

        if(articleTemplatesExist || $('#search-articles-table').DataTable().data().count()) {
            // reset table state
            $('#search-articles-table').DataTable().search('').draw();
            $('#addArticleModal').modal('show');
        } else {
            insertArticle(generateNewArticle());
        }
    });
}


function bindDeleteArticleAction() {
    $('.delete-article').off('click').on('click', function(e) {
        const $segment = $(this).closest('.segment-container');
        const articleHeader = tinymce.get($segment.find('.article-header').attr('id'));
        const articleBody = tinymce.get($segment.find('.article-body').attr('id'));
        const articleIndex = parseInt($('.editable-segment[data-type="article"]').index($segment.find('.editable-segment'))) + 1;

        // Decide if warning is needed
        const shouldShowWarning = articleHeader.isDirty() || articleBody.isDirty() ||
            !isArticleSegmentEmpty(articleHeader.getContent()) ||
            !isArticleSegmentEmpty(articleBody.getContent());

        const deleteArticle = () => {
            articleHeader.destroy();
            articleBody.destroy();

            $segment.fadeOut(300, function() {
                const precedingSegment = $segment.prev('.segment-container');
                $segment.remove();
                updateArticleReferences();
                disableMoveUpOnFirstArticle();
                updateSegmentNavigationLabels();
                toggleDeleteArticleVisibility();
                setLastDeletedArticle({
                    precedingSegment: precedingSegment,
                    segmentHtml: $segment.prop("outerHTML")
                });
                updateArticleCounters();
            });
        };

        if (shouldShowWarning) {
            const articleReferences = $.distinct($('.article-reference').map((i, el) => parseInt($(el).html())));
            const warning = articleReferences.includes(articleIndex) ?
                "Ovaj članak referencira se u drugim člancima u ugovoru. Jeste li sigurni da želite ukloniti ovaj članak iz ugovora?" :
                "Jeste li sigurni da želite ukloniti ovaj članak iz ugovora?";

            window.pleaseConfirm(warning).then(result => {
                if (result) deleteArticle();
            });
        } else {
            deleteArticle();
        }
    });
}



function bindMoveDownArticleAction() {
    $('.move-down').off('click').on('click', function(e) {
        let elementToBeMoved = $(this).closest('.segment-container');
        elementToBeMoved.insertAfter(elementToBeMoved.next()).css({opacity: 0}).animate({opacity: 1}, 700 );
        updateArticleReferences();
        updateSegmentNavigationLabels();
        disableMoveUpOnFirstArticle();
        updateArticleCounters();

        $([document.documentElement, document.body]).animate({
            scrollTop: elementToBeMoved.offset().top - 100
        }, 500);
    });
}

function bindMoveUpArticleAction() {
    $('.move-up').off('click').on('click', function(e) {
        let elementToBeMoved = $(this).closest('.segment-container');
        elementToBeMoved.insertBefore(elementToBeMoved.prev()).css({opacity: 0}).animate({opacity: 1}, 700 );
        updateArticleReferences();
        updateSegmentNavigationLabels();
        disableMoveUpOnFirstArticle();
        updateArticleCounters();

        $([document.documentElement, document.body]).animate({
            scrollTop: elementToBeMoved.offset().top - 100
        }, 500);
    });
}

function bindSegmentToolbarActions() {
    bindAddArticleAction();
    bindDeleteArticleAction();
    bindMoveDownArticleAction();
    bindMoveUpArticleAction();
}

function bindSegmentToolbar() {
    bindSegmentToolbarToggle();
    bindSegmentToolbarActions();
}

// Function to update segment navigation labels
function updateSegmentNavigationLabels() {
    let articleIndex = 0;
     // Iterate through each editable segment
    $('.editable-segment').each((i, el) => {
        // Check if the segment is an article
        if ($(el).attr('data-type') === 'article') {
            articleIndex++;
             // Get the article header and body
            let articleHeader = tinymce.get($(el).find('.article-header').attr('id'));
            let articleBody = tinymce.get($(el).find('.article-body').attr('id'));
             // Update the label for the article header and body
            $(articleHeader.getContainer()).find('.tox-tbtn:not([title="Numeriraj stavak"]) .tox-tbtn__select-label').html('Članak ' + articleIndex + '.');
            $(articleBody.getContainer()).find('.tox-tbtn:not([title="Numeriraj stavak"]) .tox-tbtn__select-label').html('Članak ' + articleIndex + '.');
        }
    });
}

// This function updates the references of articles
function updateArticleReferences() {
    // Loop through each element with class 'article-reference'
    $('.article-reference').each((i, el) => {
        // Get the index of the article element with the same data-id as the current 'article-reference' element
        let articleIndex = parseInt($('.editable-segment[data-type="article"]').index($('.editable-segment[data-id="'+$(el).attr('data-article-id')+'"]')));
         // Check if the article index is valid
        if(articleIndex >= 0) {
            // Update the content of the 'article-reference' element with the article index + 1
            $(el).html(articleIndex + 1);
        }
    });
}

function bindArticleReferences() {
    // assign ids to articles
    $('.editable-segment[data-type="article"]').each(
        (i, article) => $(article).attr('data-id', generateRandomString())
    );

    // attach article ids to article reference elements
    $('.article-reference').each((i, el) => {
        let articleId = $($('.editable-segment[data-type="article"]').get(parseInt($(el).html())-1)).data('id');
        $(el).attr('data-article-id', articleId);
    });
}

function saveAndExitHandler(e) {

    e.preventDefault();

    // remove tinymce DOM elements
    tinymce.remove();

    let editor = $('#editor').clone();
    let segments = editor.find('.editable-segment');

    let output = prepareOutput(segments);

    if(output.length) {
        $('#save-and-exit').addClass('disabled');
        $('#html').val(output);
        $('#save-document-form').submit();
    } else {
        alert('Ups, došlo je do pogreške. Probajte ponovo ili nam se obratite za pomoć.')
    }
}

function saveAndContinueHandler(e) {

    e.preventDefault();

    // remove tinymce DOM elements
    tinymce.remove();

    let editor = $('#editor').clone();
    let segments = editor.find('.editable-segment');

    let output = prepareOutput(segments);

    if(output.length) {
        // save by ajax
        $.post($('#save-document-form').attr('action'), {
            html: output,
            solemnization: $('input[name="solemnization"]').is(':checked') ? 'on' : 'off',
            _token: $("[name='_token']").val()
        }).fail(() => alert('Ups, došlo je do pogreške prilikom spremanja promjena. Probajte ponovo ili nam se obratite za pomoć.'));
    }

    // reinstantiate tinymce
    window.initParties();
    window.initTitle();
    window.initBody();
    window.initArticleHeader();
    window.initArticleBody();

    // close modal
    $('#saveChangesModal').modal('hide');
}


function prepareOutput(segments) {

    let articleIndex = 0;

    return segments.map(function(segment_i, segment) {

        if($(segment).attr('data-type') === 'article') {

            articleIndex++;

            // numerate article headers
            let articleHeader = $(segment).find('.article-header');
            let articleHeaderHtml = articleHeader.html().length ? articleHeader.html() : "<br>";
            articleHeader.replaceWith("<p class='article-header'>Članak "+articleIndex+". "+articleHeaderHtml+"</p>");
            $(segment).find('.article-list li').contents().unwrap();   // remove parent list element
            $(segment).find('.article-list').contents().unwrap();   // remove parent list

            // numerate article paragraphs
            let articleParagraphs = $(segment).find('.paragraph-list li');

            articleParagraphs.each(function(paragraph_i, paragraph){
                // remove text decoration styling from paragraph
                $(paragraph).css('text-decoration', 'none');
                $(paragraph).css('font-style', 'normal');
                $(paragraph).css('font-weight', 'normal');

                // other styling may apply (floats)
                let pStyle = typeof($(paragraph).attr('style')) !== 'undefined' ? $(paragraph).attr('style') : '';
                $(paragraph).replaceWith("<p style='"+pStyle+"'>"+articleIndex+"."+(paragraph_i+1)+". " + $(paragraph).html()+"</p>")
            });

            $(segment).find('.paragraph-list').contents().unwrap();   // remove parent list element

            // remove tinymce id
            $(segment).find('.article-header').removeAttr('id');
            $(segment).find('.article-body').removeAttr('id');

        } else {
            // remove tinymce id
            $(segment).removeAttr('id');

            // for parties/title add dirty class to segment
            if($(segment).parent('.segment-dirty').length) {
                $(segment).addClass('segment-dirty');
            }
        }

        // remove extra trailing lines at the end of the segment
        let paragraphs = $(segment).find('p');
        for (let i = paragraphs.length - 1; i >= 0; i--) {
            let paragraph = $(paragraphs[i]);
            let isEmpty = $.trim(paragraph.html().replace(/&nbsp;/g, '')) === '' || paragraph.html() === '<br>';

            // Stop removing when a non-empty paragraph is found
            if (!isEmpty) {
                break;
            }

            // Remove empty paragraph
            paragraph.remove();
        }

        // if last segment, append signees and attachments
        if(segment_i === (segments.length - 1)) {
            segment = appendSignatureLines(segment);
        }

        return $(segment).get(0).outerHTML;

    }).get().join('') + getSupplementariesContainer();
}

function getSignees() {
    let signees = [];

    $(".signee-data").each(function() {
        let signee = $(this).find(".signee-label").val();
        let side = $(this).find(".signee-side option:selected").val();

        if(signee.length) {
            signees.push({ signee: signee, side: side });
        }
    });

    if(signees.length) {
        return adjustSignees(signees);
    }

    return [];
}

function getAttachments() {
    let attachments = $(".attachment-label").map(function() {
        return $(this).val();
    }).get();

    // filter out non-empty
    return attachments.filter(attachment => attachment.length);
}

function getRecipients() {
    let recipients = $(".recipient-label").map(function() {
        return $(this).val();
    }).get();

    // filter out non-empty
    return recipients.filter(recipient => recipient.length);
}

function getLegalRemedy() {
    if(!$('#legal-remedy').length) return '';
    let legalRemedy = $('#legal-remedy').val();

    // append <br> to new lines (for PDF rendering)
    legalRemedy = legalRemedy.replace(/(\r?\n)/g, '$1<br>');

    return legalRemedy;
}

// makes both left and right side arrays the same size
function adjustSignees(signees) {
    const left_signees = signees.filter(s => s.side === 'left').map(s => s.signee);
    const right_signees = signees.filter(s => s.side === 'right').map(s => s.signee);
    const max_length = Math.max(left_signees.length, right_signees.length);

    left_signees.length = right_signees.length = max_length;
    left_signees.fill(null, left_signees.length);
    right_signees.fill(null, right_signees.length);

    return Array.from({ length: max_length }, (_, i) => ({
        left: left_signees[i],
        right: right_signees[i]
    }));
}

function appendSignatureLines(segment) {
    const signees = getSignees();
    if (!signees.length) return segment;

    const createCell = (content) => {
        return content ? `<div style="margin:0; padding: 0;"><br/><br/><br/><br/><br/><br/><br/><br/></div><span class="signature-line">___________________________________________</span><br><small>${content.replace(/\n/g, '<br>')}</small><br/>` : '';
    };

    const createTable = (leftContent, rightContent) => {
        const leftCell = `<td style="vertical-align:top; width: 50%;">${createCell(leftContent)}</td>`;
        const rightCell = `<td style="vertical-align:top; padding-left: 5%; width: 50%;">${createCell(rightContent)}</td>`;
        return `<table class="signature-table" style="table-layout: fixed; width: 100%; overflow: scroll;"><tr class="avoid-page-break">${leftCell + rightCell}</tr></table>`;
    };

    if ($(segment).attr('data-type') === 'article') {
        const articleBody = $(segment).find('.article-body');
        // wrap content in "stick-to-header" div
        $(articleBody).wrapInner('<div class="stick-to-header"></div>');
        const stickToHeader = $(articleBody).find('.stick-to-header');

        $.each(signees, (index, _signee) => {
            const table = createTable(_signee.left, _signee.right);
            if (index === 0) {
                $(stickToHeader).append(table);
            } else {
                $(articleBody).append(table);
            }
        });
    } else {
        $.each(signees, (index, _signee) => {
            const table = createTable(_signee.left, _signee.right);
            $(segment).append(table);
        });
    }

    return segment;
}

function getSupplementariesContainer() {
    const attachments = getAttachments();
    const recipients = getRecipients();
    const legalRemedy = getLegalRemedy();

    if (!attachments.length && !recipients.length && !legalRemedy.length) return '';

    return '<div id="supplementaries-container" class="mt-5">' + getAttachmentsContainer() + getRecipientsContainer() + getLegalRemedyContainer() + '</div>';
}

function getAttachmentsContainer() {

    const attachments = getAttachments();
    if (!attachments.length) return '';

    const recipients = getRecipients();
    const legalRemedy = getLegalRemedy();

    let marginClass = recipients.length || legalRemedy.length ? 'mb-3' : '';


    return '<div id="attachments-container" class="avoid-page-break">\n' +
        '        <div class="' + marginClass + '">\n' +
        '            <p><strong>U privitku:</strong></p>\n' +
        '            <ul>\n' +
        '                ' + attachments.map(attachment => `<li><span class="attachment">${attachment}</span></li>`).join('') +
        '            </ul>\n' +
        '        </div>\n' +
        '    </div>';
}

function getRecipientsContainer() {

    const recipients = getRecipients();
    if (!recipients.length) return '';

    const legalRemedy = getLegalRemedy();

    let marginClass = legalRemedy.length ? 'mb-3' : '';

    return '<div id="recipients-container" class="avoid-page-break">\n' +
        '        <div class="' + marginClass + '">\n' +
        '            <p><strong>Dostaviti:</strong></p>\n' +
        '            <ul>\n' +
        '                ' + recipients.map(recipient => `<li><span class="recipient">${recipient}</span></li>`).join('') +
        '            </ul>\n' +
        '        </div>\n' +
        '    </div>';
}

function getLegalRemedyContainer() {

    const legalRemedy = getLegalRemedy();

    if (!legalRemedy.length) return '';

    return '<div id="legal-remedy-container" class="avoid-page-break">\n' +
        '        <div>\n' +
        '            <p><strong>Uputa o pravnom lijeku:</strong></p>\n' +
        '            <div id="legal-remedy">' + legalRemedy + '</div>\n' +
        '        </div>\n' +
        '    </div>';
}

function bindNavigationConfirmationDialog() {
    let confirmMessage = "Imate nespremljene promjene. Jeste li sigurni da želite nastaviti?";

    // back button
    let initialReferrer = document.referrer;
    history.pushState(null, document.title, location.href);
    window.addEventListener('popstate', function(event) {
        // Stop the browser from immediately navigating back.
        event.preventDefault();

        if (!isAnyEditorDirty()) {
            history.back();
        } else {
            window.pleaseConfirm(confirmMessage).then(result => {
                if (result) {
                    window.location.href = initialReferrer;
                }
            });

            history.pushState(null, document.title, location.href);
        }
    });

    // navigation links
    $('.navbar a, .footer-links a').on('click', function(e) {
        let $clickedLink = $(this);

        if ($clickedLink.attr('href').length && isAnyEditorDirty()) {
            e.preventDefault();
            window.pleaseConfirm(confirmMessage).then(result => {
                if(result){
                    window.location.href = $clickedLink.attr('href');
                }
            });
        }
    });

}

function isAnyEditorDirty() {
    let isDirty = [];
    tinymce.get().forEach(function(editor){
        if(editor.isDirty()) {
            isDirty.push(editor.id);
        }
    });

    return isDirty.length;
}

function bindOnClickArticleListItemFocusEditor() {
    $('.article-list li').on('click', function(e){
        let id = $(this).find('p').attr('id');
        tinymce.get(id).focus();
    })
}

function setLastDeletedArticle(article) {
    lastDeletedArticle = article;
    toggleArticleDeletedAlert('show');
}

function restoreLastDeletedArticle(e) {
    e.preventDefault();

    if(lastDeletedArticle !== null) {
        let { precedingSegment, segmentHtml } = lastDeletedArticle;
        let restoredArticle = $(segmentHtml).insertAfter(precedingSegment);

        tinymce.init({
            ...window.articleHeaderConfig,
            selector: '#'+restoredArticle.find('.article-header').attr('id')
        });

        tinymce.init({
            ...window.articleBodyConfig,
            selector: '#'+restoredArticle.find('.article-body').attr('id')
        });

        bindSegmentToolbar();
        updateSegmentNavigationLabels();
        updateArticleReferences();
        bindOnClickArticleListItemFocusEditor();
        updateArticleCounters();

        restoredArticle.fadeIn();

        restoredArticle.find('[data-toggle="tooltip"]').tooltip({html: true})

        // scroll to new article
        $([document.documentElement, document.body]).animate({
            scrollTop: restoredArticle.offset().top - 100
        }, 500);
    }

    toggleArticleDeletedAlert('hide');

    $('[data-toggle="tooltip"]').tooltip({html: true})

    lastDeletedArticle = null;
}

function toggleArticleDeletedAlert(action) {
    if(action === 'show') {
        $('#alert-widget .alert').removeClass('hide').addClass('show');
    } else if(action === 'hide') {
        $('#alert-widget .alert').removeClass('show').addClass('hide');
    }
}

function toggleArticleDetails() {
    const tr = $(this).closest('tr');
    const row = $('#search-articles-table').DataTable().row(tr);

    if (row.child.isShown()) {
        // This row is already open - close it
        row.child.hide();
        tr.removeClass('shown');
        $(this).removeClass('fa-chevron-circle-up').addClass('fa-chevron-circle-down');
    } else {
        // Close all other rows
        $('#search-articles-table').DataTable().rows().every(function () {
            if (this.child.isShown()) {
                this.child.hide();
                $(this.node()).removeClass('shown');
                $(this.node()).find('i.details-control').removeClass('fa-chevron-circle-up').addClass('fa-chevron-circle-down');
            }
        });

        // Open this row
        row.child(row.data().body).show();
        tr.addClass('shown');
        $(this).removeClass('fa-chevron-circle-down').addClass('fa-chevron-circle-up');
    }
}


function insertArticleTemplate() {
    // fetch row data
    let data = $('#search-articles-table').DataTable().row($(this).parents('tr')).data();

    // generate article data
    let articleData = generateNewArticle(data['title'], data['content']);

    // insert article as a new segment
    insertArticle(articleData);

    // hide new article modal
    $('#addArticleModal').modal('hide');
}

function disableMoveUpOnFirstArticle() {

    // Remove the 'disabled-move-up' class from all move-up buttons
    $(".segment-container .move-up").removeClass('disabled-move-up');

    // Add the 'disabled-move-up' class to the first move-up button in the first segment-container that contains it
    let firstMoveUpButton = $(".segment-container:has(.move-up):first .move-up");

    if (firstMoveUpButton.length) {
        firstMoveUpButton.addClass('disabled-move-up');
    }
}

function updateArticleCounters() {
    const articles = document.querySelectorAll('.article-body');
    articles.forEach((article, index) => {
        // Set articleIndex for each article
        article.style.counterReset = `articleIndex ${index + 1}`;
    });
}

$(document).ready(function () {

    function bindRemoveRecipient() {
        $('.remove-recipient').off('click').on('click', function () {
            $(this).closest('tr').remove();
            updateRecipientsCount();
        });
    }

    function bindAddRecipient() {
        $('#add-recipient').off('click').on('click', function () {
            $('#recipients-table tbody').append(`
                <tr class="recipient-data">
                    <td>
                        <input type="text" placeholder="Upiši naziv primatelja..." class="form-control recipient-label"></input>
                    </td>
                  
                    <td class="text-center">
                        <a class="btn btn-danger btn-sm remove-recipient"><i class="fa fa-trash"></i></a>
                    </td>
                </tr>`);

            bindRecipientLabelChange();
            updateRecipientsCount();
            bindRemoveRecipient();
        });
    }

    function updateRecipientsCount() {
        // count the number of non-empty recipient-label inputs
        let count = $('.recipient-label').filter(function() {
            return $(this).val().length > 0
        }).length;

        if(count > 0) {
            $('#recipients-count').text('(' + count + ')');
        } else {
            $('#recipients-count').text('');
        }
    }

    // on recipient-label typing, update recipients-count
    function bindRecipientLabelChange() {
        $('.recipient-label').off('input').on('input', function() {
            updateRecipientsCount();
        })
    }

    bindRecipientLabelChange();
    bindRemoveRecipient();
    bindAddRecipient();
});

$(document).ready(function () {

    // global scope, so it can be accessed from editor.js
    window.articleHeaderConfig = {
        language: 'hr',
        selector: '.article-header',
        fixed_toolbar_container: '#editor-toolbar',
        menubar: false,
        setup: (editor) => {

            editor.on('init', () => {
                $('#editor-toolbar').hide(); // hide toolbar container
                editor.ui.show(); // load toolbar in DOM

                // strip link tags
                let content = editor.getContent();
                content = content.replace(/<a\s[^>]*>(.*?)<\/a>/g, "$1");
                editor.setContent(content);
            });

            editor.on('focus', function(){
                $('.tox-tinymce-inline').css('display', 'none'); // hide any remaining open toolbars
                $('#editor-toolbar').show(); // reveal toolbar container
                $(editor.getElement()).closest('.segment-container').addClass('active');    // make segment container active
            })

            editor.on('blur', function(e) {
                // if content is different from starting content, wrap in dirty div
                if(editor.getContent({ format: 'raw' }).replace(/\n/g, "") !== editor.startContent) {
                    if(!$(editor.getElement()).parent('.segment-dirty').length) {
                        $(editor.getElement()).wrap('<div class="segment-dirty"></div>');
                    }
                }

                $(editor.getElement()).closest('.segment-container').removeClass('active');     // remove segment container active
            })

            let articleIndex = parseInt($('.editable-segment[data-type="article"]').index($(editor.getElement()).closest('.editable-segment'))) + 1;

            editor.ui.registry.addButton('navigationLabel', {
                text: 'Članak ' + articleIndex + '.',
                onAction: function (_) {},
            });

            editor.ui.registry.addButton('boldCustom', {
                icon: 'bold',
                tooltip: 'Podebljano',
                onAction: function (_) {},
                onSetup: function (buttonApi) {buttonApi.setEnabled(false);}
            });

            editor.ui.registry.addButton('insertUnorderedListCustom', {
                icon: 'unordered-list',
                tooltip: 'Popis s oznakama',
                onAction: function (_) {},
                onSetup: function (buttonApi) {buttonApi.setEnabled(false);}
            });

            editor.ui.registry.addButton('insertOrderedListCustom', {
                icon: 'ordered-list',
                tooltip: 'Numerirani popis',
                onAction: function (_) {},
                onSetup: function (buttonApi) {buttonApi.setEnabled(false);}
            });

            editor.ui.registry.addButton('indentCustom', {
                icon: 'indent',
                tooltip: 'Povećaj uvlačenje',
                onAction: function (_) {},
                onSetup: function (buttonApi) {buttonApi.setEnabled(false);}
            });

            editor.ui.registry.addButton('outdentCustom', {
                icon: 'outdent',
                tooltip: 'Smanji uvlačenje',
                onAction: function (_) {},
                onSetup: function (buttonApi) {buttonApi.setEnabled(false);}
            });

            editor.ui.registry.addButton('alignLeftCustom', {
                icon: 'align-left',
                tooltip: 'Poravnaj lijevo',
                onAction: function (_) {},
                onSetup: function (buttonApi) {buttonApi.setEnabled(false);}
            });

            editor.ui.registry.addButton('alignRightCustom', {
                icon: 'align-right',
                tooltip: 'Poravnaj desno',
                onAction: function (_) {},
                onSetup: function (buttonApi) {buttonApi.setEnabled(false);}
            });

            editor.ui.registry.addButton('alignCenterCustom', {
                icon: 'align-center',
                tooltip: 'Poravnaj po sredini',
                onAction: function (_) {},
                onSetup: function (buttonApi) {buttonApi.setEnabled(false);}
            });

            editor.on('change', function(e){

            })

            editor.on('keydown', function(e) {

                if(e.which === 9 || e.which === 13) {   // disable TAB and ENTER
                    e.preventDefault();
                    return false;
                }
            });
        },
        plugins: ['lists', 'save'],
        save_oncancelcallback: function (editor) {
            editor.setContent(editor.startContent);
            // place cursor at end of input
            editor.selection.select(editor.getBody(), true);
            editor.selection.collapse(false);

        },
        inline: true,
        paste_as_text: true,
        paste_block_drop: true,
        smart_paste: false,
        toolbar: 'navigationLabel boldCustom italic underline backcolor removeformat alignLeftCustom alignCenterCustom alignRightCustom insertOrderedListCustom insertUnorderedListCustom indentCustom outdentCustom undo redo cancel',
        color_default_background: '#FBEEB8',
    };

    tinymce.init(window.articleHeaderConfig);

    // add initialization to window function
    window.initArticleHeader = function() {tinymce.init(window.articleHeaderConfig)}
});

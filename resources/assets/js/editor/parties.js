$(document).ready(function () {

    let config = {
        placeholder: "Ovdje upiši podatke o strankama...",
        language: 'hr',
        selector: '.editable-segment[data-type="parties"]',
        fixed_toolbar_container: '#editor-toolbar',
        menubar: false,
        plugins: ['lists', 'save'],
        save_oncancelcallback: function (editor) {
            editor.setContent(editor.startContent);
            // place cursor at end of input
            editor.selection.select(editor.getBody(), true);
            editor.selection.collapse(false);

        },
        setup: (editor) => {

            editor.on("init", function () {
                // strip link tags
                let content = editor.getContent();
                content = content.replace(/<a\s[^>]*>(.*?)<\/a>/g, "$1");
                editor.setContent(content);
            });

            editor.on('focus', function(){
                $('.tox-tinymce-inline').css('display', 'none'); // hide any remaining open toolbars
                $('#editor-toolbar').show(); // reveal toolbar container
                $(editor.getElement()).closest('.segment-container').addClass('active');    // make segment container active
            })

            editor.on('blur', function(e) {
                // if content is different from starting content, wrap in dirty div
                if(editor.getContent({ format: 'raw' }).replace(/\n/g, "") !== editor.startContent) {
                    if(!$(editor.getElement()).parent('.segment-dirty').length) {
                        $(editor.getElement()).wrap('<div class="segment-dirty"></div>');
                    }
                }

                $(editor.getElement()).closest('.segment-container').removeClass('active');     // remove segment container active
            })

            editor.ui.registry.addButton('navigation', {
                text: 'Stranke ',
                onAction: function (_) {},
            });
        },
        inline: true,
        content_style: "body { margin: 1rem auto }",
        paste_as_text: true,
        paste_block_drop: true,
        smart_paste: false,
        toolbar: 'navigation bold italic underline backcolor removeformat alignleft aligncenter alignright numlist bullist indent outdent undo redo cancel',
        color_default_background: '#FBEEB8',
    };

    tinymce.init(config);

    // add initialization to window function
    window.initParties = function() {tinymce.init(config)}
});

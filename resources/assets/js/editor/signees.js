$(document).ready(function () {

    function bindRemoveSignee() {
        $('.remove-signee').off('click').on('click', function () {
            if($(this).closest('tr').find('.signee-label').val()) {
                window.pleaseConfirm('Jeste li sigurni da želite ukloniti ovog potpisnika?').then(result => {
                    if(result){
                        $(this).closest('tr').remove();
                    }
                });
            } else {
                $(this).closest('tr').remove();
            }
        });
    }

    function bindAddSignee() {
        $('#add-signee').off('click').on('click', function () {
            $('#signees-table tbody').append(`
                <tr class="signee-data">
                    <td>
                        <textarea placeholder="Upiši ime potpisnika..." class="form-control signee-label"></textarea>
                    </td>
                    <td>
                        <select class="form-control signee-side">
                            <option value="left">Lijeva</option>
                            <option value="right">Desna</option>
                        </select>
                    </td>
                    <td class="text-center">
                        <a class="btn btn-danger btn-sm remove-signee"><i class="fa fa-trash"></i></a>
                    </td>
                </tr>`);

            bindRemoveSignee();
        });
    }

    bindRemoveSignee();
    bindAddSignee();
});

$(document).ready(function () {

    // indicate if probation period type has already been saved
    let probationPeriodTypeSet = $('#probation_period_type_set').length;

    // definite or indefinite document switch
    $('#definite_contract').click(function () {
        $('#definite_contract_container').removeClass("sliding-up").slideDown();
    });

    $('#indefinite_contract').click(function () {
        $('#definite_contract_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
    });

    // probation period
    $('#is_probation_period').click(function () {
        $('#probation_period_container').removeClass("sliding-up").slideDown();
    });

    $('#is_not_probation_period').click(function () {
        $('#probation_period_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
    });

    // work start date custom clear
    $('#work_start_date_non_custom_radio').on('focus', function () {
        $('#work_start_date_custom').val("");
    });

    // special case, two radio labels
    $("label[for='expected_duration_until_date']").on('click', function(e) {
        $(this).closest('.form-check-label').trigger({
                        type: 'click',
                        programmatic: true
                    });
    });


    // has collective agreement switch
    $('#has_collective_agreement').click(function () {
        $('#has_collective_agreement_container').removeClass("sliding-up").slideDown();
        $('#probation_period_collective_agreement_container').removeClass("sliding-up").slideDown();

        // if probation period type has not already been set, set it to collective agreement
        if(!probationPeriodTypeSet && $('#probation_period_custom_cb').is(':checked')) {
            $('#probation_period_defined_in_collective_agreement_cb').trigger({
                        type: 'click',
                        programmatic: true
                    });
        }
    });

    $('#has_not_collective_agreement').click(function () {
        $('#has_collective_agreement_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
        $('#probation_period_collective_agreement_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");

        // if probation period is set to collective agreement, reset to default
        if($('#probation_period_defined_in_collective_agreement_cb').is(':checked')) {
            if($('#has_work_regulations').is(':checked')) {
                $('#probation_period_defined_in_work_regulations_cb').trigger({
                        type: 'click',
                        programmatic: true
                    });
            } else {
                $('#probation_period_custom_cb').trigger({
                        type: 'click',
                        programmatic: true
                    });
            }

        }
    });

    // has work regulations switch
    $('#has_work_regulations').click(function () {
        $('#has_work_regulations_container').removeClass("sliding-up").slideDown();
        $('#probation_period_work_regulations_container').removeClass("sliding-up").slideDown();

        // if probation period type has not already been set, set it to work regulations
        if(!probationPeriodTypeSet && $('#probation_period_custom_cb').is(':checked')) {
            $('#probation_period_defined_in_work_regulations_cb').trigger({
                        type: 'click',
                        programmatic: true
                    });
        }
    });

    $('#has_not_work_regulations').click(function () {
        $('#has_work_regulations_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
        $('#probation_period_work_regulations_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");

        // if probation period is set to work regulations, reset to default
        if($('#probation_period_defined_in_work_regulations_cb').is(':checked')) {
            if($('#has_collective_agreement').is(':checked')) {
                $('#probation_period_defined_in_collective_agreement_cb').trigger({
                        type: 'click',
                        programmatic: true
                    });
            } else {
                $('#probation_period_custom_cb').trigger({
                        type: 'click',
                        programmatic: true
                    });
            }
        }
    });
});
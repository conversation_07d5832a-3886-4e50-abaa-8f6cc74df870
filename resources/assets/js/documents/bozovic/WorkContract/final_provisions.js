$(document).ready(function () {
    // custom provisions
    $('#add_custom_provision').click(function () {
        let custom_provision_index = Date.now();  // we just care that it is unique
        let new_custom_provision = $('#custom_provision_template').html().replace(new RegExp('{INDEX}', 'g'), custom_provision_index);
        $('#custom_provisions_container').append(new_custom_provision);
        bindRemoveCustomProvision();
        bindTextAreaKeyPress();
    });

    bindTextAreaKeyPress();

    // on enter, add double line break (to be converted to paragraph)
    function bindTextAreaKeyPress() {
        $('textarea').unbind().keypress(function(e) {
            if (e.which == 13) {
                e.preventDefault();
                let s = $(this).val();
                $(this).val(s+"\n\n");
            }
        }).keydown(function(e){
            if(e.which == 8) {
                let s = $(this).val();
                let charToBeDeletedUnicode = s.charCodeAt(s.length-1);

                // if new line, remove one extra line break
                if(charToBeDeletedUnicode === 10){
                    $(this).val(s.substring(0, s.length - 1));
                }
            }
        });
    }

    // remove custom provision
    function bindRemoveCustomProvision() {
        $('.remove_custom_provision').unbind('click').click(function () {
            let target = $(this).closest('.custom_provision_content');

            target.hide('slow', function () {
                target.remove();
            }).addClass("sliding-up");

        });
    }

    bindRemoveCustomProvision();
});

$(document).ready(function () {

    // flexible or fixed work location switch
    $('#fixed_work_location').click(function () {
        $('#flexible_work_location_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
        $('#fixed_work_location_container').removeClass("sliding-up").slideDown();
    });

    $('#flexible_work_location').click(function () {
        $('#fixed_work_location_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
        $('#flexible_work_location_container').removeClass("sliding-up").slideDown();
    });

    // custom notice switch
    $('.notice_management_type_non_custom_radio').click(function () {
        $('#custom_notice_management_type_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
    });
    $('#notice_management_type_custom_radio').click(function () {
        $('#custom_notice_management_type_container').removeClass("sliding-up").slideDown();
    });

    // custom compensation switch
    $('.compensation_management_type_non_custom_radio').click(function () {
        $('#custom_compensation_management_type_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
    });
    $('#compensation_management_type_custom_radio').click(function () {
        $('#custom_compensation_management_type_container').removeClass("sliding-up").slideDown();
    });

    // custom salary bonuses

    // add custom salary bonus
    $('#add_custom_salary_bonus').click(function () {
        let new_salary_bonus = $('#custom_salary_bonus_template').html().replace(new RegExp('{INDEX}', 'g'), Date.now());
        $('#custom_salary_bonuses').append(new_salary_bonus);

        bindRemoveCustomSalaryBonus();
    });

    function bindRemoveCustomSalaryBonus() {
        $('.remove_custom_salary_bonus').unbind('click').click(function () {
            let target = $(this).closest('.custom_salary_bonus_content');

            target.hide('slow', function () {
                target.remove();
            }).addClass("sliding-up");

        });
    }

    bindRemoveCustomSalaryBonus();

    // auto check/uncheck custom material perks checkboxes

    $('#material_perk_christmas_value').on('change paste keyup', function (e) {
        if ($(this).val().length) {
            $('#material_perk_christmas_cb').prop('checked', true);
        } else {
            $('#material_perk_christmas_cb').prop('checked', false);
        }
    });

    $('#material_perk_easter_value').on('change paste keyup', function (e) {
        if ($(this).val().length) {
            $('#material_perk_easter_cb').prop('checked', true);
        } else {
            $('#material_perk_easter_cb').prop('checked', false);
        }
    });

    $('#material_perk_child_gift_value').on('change paste keyup', function (e) {
        if ($(this).val().length) {
            $('#material_perk_child_gift_cb').prop('checked', true);
        } else {
            $('#material_perk_child_gift_cb').prop('checked', false);
        }
    });

    $('#material_perk_vacation_subvention_value').on('change paste keyup', function (e) {
        if ($(this).val().length) {
            $('#material_perk_vacation_subvention_cb').prop('checked', true);
        } else {
            $('#material_perk_vacation_subvention_cb').prop('checked', false);
        }
    });

    $('#material_perk_meal_subvention_value').on('change paste keyup', function (e) {
        if ($(this).val().length) {
            $('#material_perk_meal_subvention_cb').prop('checked', true);
        } else {
            $('#material_perk_meal_subvention_cb').prop('checked', false);
        }
    });

    $('#material_perk_transport_subvention_value').on('change paste keyup', function (e) {
        console.log("HERE");
        if ($(this).val().length) {
            $('#material_perk_transport_subvention_cb').prop('checked', true);
        } else {
            $('#material_perk_transport_subvention_cb').prop('checked', false);
        }
    });

    $('#material_perk_transport_subvention_intercity_value').on('change paste keyup', function (e) {
        if ($(this).val().length) {
            $('#material_perk_transport_subvention_intercity_cb').prop('checked', true);
        } else {
            $('#material_perk_transport_subvention_intercity_cb').prop('checked', false);
        }
    });

    function setTransportSubventionPlaceholder(selectElement) {
        let selectedValue = selectElement.val();
        let placeholderText;

        if (selectedValue === 'description') {
            placeholderText = 'Npr: u visini stvarnih izdataka prema cijeni mjesečne odnosno pojedinačne prijevozne karte';
        } else {
            placeholderText = 'Npr: 150,00';
        }

        selectElement.closest('.input-group').find('input').attr('placeholder', placeholderText);
    }

    let transportSubventionTypeLocal = $('select[name="material_perk_transport_subvention[type]"]');
    let transportSubventionTypeIntercity = $('select[name="material_perk_transport_subvention_intercity[type]"]');

    setTransportSubventionPlaceholder(transportSubventionTypeLocal, 'local');
    setTransportSubventionPlaceholder(transportSubventionTypeIntercity, 'intercity');

    transportSubventionTypeLocal.change(function() {
        setTransportSubventionPlaceholder($(this));
    });

    transportSubventionTypeIntercity.change(function() {
        setTransportSubventionPlaceholder($(this));
    });

    // custom salary perks

    // add custom salary perk
    $('#add_custom_salary_perk').click(function () {
        let new_salary_perk = $('#custom_salary_perk_template').html().replace(new RegExp('{INDEX}', 'g'), Date.now());
        $('#custom_salary_perks').append(new_salary_perk);
        bindRemoveCustomSalaryPerk();
    });

    function bindRemoveCustomSalaryPerk() {
        $('.remove_custom_salary_perk').unbind('click').click(function () {
            let target = $(this).closest('.custom_salary_perk_content');

            target.hide('slow', function () {
                target.remove();
            }).addClass("sliding-up");

        });
    }

    bindRemoveCustomSalaryPerk();


    // custom material perks

    // add custom material perk
    $('#add_custom_material_perk').click(function () {
        let new_material_perk = $('#custom_material_perk_template').html().replace(new RegExp('{INDEX}', 'g'), Date.now());
        $('#custom_material_perks').append(new_material_perk);
        bindRemoveCustomMaterialperk();
    });

    function bindRemoveCustomMaterialperk() {
        $('.remove_custom_material_perk').unbind('click').click(function () {
            let target = $(this).closest('.custom_material_perk_content');

            target.hide('slow', function () {
                target.remove();
            }).addClass("sliding-up");

        });
    }

    bindRemoveCustomMaterialperk();


    // work responsibilities

    // add work responsibility
    $('#add_work_responsibility').click(function () {
        let new_work_responsibility = $('#work_responsibility_template').html().replace(new RegExp('{INDEX}', 'g'), Date.now());
        $('#work_responsibilities').append(new_work_responsibility);
        bindRemoveWorkResponsibility();
    });

    function bindRemoveWorkResponsibility() {
        $('.remove_work_responsibility').unbind('click').click(function () {
            let target = $(this).closest('.work_responsibility_content');

            if ($('.remove_work_responsibility:visible').length > 1) {
                target.remove();
                $(document).trigger('updatePreviewEvent');
            }

        });
    }

    bindRemoveWorkResponsibility();


    // toggle visibility of week hours defined in work regulations/collective agreement
    $('#is_full_time_0').on('click', function (e) {
        $('#week_hours_defined_in_work_regulations').addClass('d-none');
        $('#week_hours_defined_in_collective_agreement').addClass('d-none');
        $('#week_hours_custom').attr('max', 39).attr('placeholder', 'Npr: 30');
        $("label[for='week_hours'] span[data-toggle='tooltip']").attr('data-original-title', 'Prema Zakonu o radu, nepuno radno vrijeme radnika ne može biti duže od 39 sati tjedno.');
        $('#full_time_working_hours').addClass('d-none');
        $('input[name="week_hours"]').trigger({
                        type: 'click',
                        programmatic: true
                    });
    });

    $('#is_full_time_1').on('click', function (e) {
        $('#week_hours_defined_in_work_regulations').removeClass('d-none');
        $('#week_hours_defined_in_collective_agreement').removeClass('d-none');
        $('#week_hours_custom').attr('max', 40).attr('placeholder', 'Npr: 40');
        $("label[for='week_hours'] span[data-toggle='tooltip']").attr('data-original-title', 'Prema Zakonu o radu, puno radno vrijeme radnika ne može biti duže od 40 sati tjedno.');
        $('#full_time_working_hours').removeClass('d-none');
    });

    // on change custom working hours, check checkbox
    $('.custom_working_hours').on('change', function (e) {
        if (!$('#working_hours_custom_radio').is(':checked')) {
            $('#working_hours_custom_radio').trigger({
                        type: 'click',
                        programmatic: true
                    });
        }

    });

    // on tick one option in working hours, untick others (simulate radio behaviour)
    $('.working_hours').on('change', function (e) {
        $('.working_hours').not(this).prop('checked', false);
    });

    //
    $('#salary_perk_car').on('change', function(){
        if($(this).prop('checked')) {
            $('#salary_perk_car_container').removeClass("sliding-up").slideDown();
        } else {
            $('#salary_perk_car_container').slideUp()
        }
    });


});

// validation has to be declared outside document ready function and assigned to window object
function validate() {

    // remove all existing has-error classes
    $('div').removeClass('has-error');

    // set variables
    let errors = [];


    // **** VALIDATION RULES ****** //

    // 1. week hours is max 40
    let weekHoursCustomCb = $('#week_hours_custom_cb');
    let weekHoursCustomInput = $('#week_hours_custom');

    if(weekHoursCustomCb.is(':checked')) {
        if(weekHoursCustomInput.val() && parseFloat(weekHoursCustomInput.val()) > parseFloat(weekHoursCustomInput.attr('max'))) {

            if($('#is_full_time_1').is(':checked')) {
                errors.push("Puno radno vrijeme radnika ne može biti duže od " + weekHoursCustomInput.attr('max') + " sati tjedno.");
            } else {
                errors.push("Nepuno radno vrijeme radnika ne može biti duže od " + weekHoursCustomInput.attr('max') + " sati tjedno.");
            }

            weekHoursCustomInput.closest('.radio-input').addClass('has-error');
        }
    }

    // 2. sunday salary bonus is min 50
    let sundaySalaryBonus = $('#sunday_salary_bonus');
    if(sundaySalaryBonus.val() && parseFloat(sundaySalaryBonus.val()) < parseFloat(sundaySalaryBonus.attr('min'))) {
        errors.push("Postotak povećanja plaće za rad nedjeljom ne može biti manji od " + sundaySalaryBonus.attr('min') + "%.");
        sundaySalaryBonus.closest('.input-group').addClass('has-error');
    }

    // 3. salary day between 1 and 15
    let salaryDayCustomCb = $('#salary_day_custom_cb');
    let salaryDayCustomInput = $('#salary_day_custom');

    if(salaryDayCustomCb.is(':checked') && salaryDayCustomInput.val()) {
        if(parseFloat(salaryDayCustomInput.val()) > parseFloat(salaryDayCustomInput.attr('max'))) {
            errors.push("Plaća za prethodni mjesec ne može dospijevati kasnije od " + salaryDayCustomInput.attr('max') + ". dana tekućeg mjeseca.");
            salaryDayCustomInput.closest('.radio-input').addClass('has-error');
        }
    }

    // **** END VALIDATION RULES ****** //


    // display errors
    if (errors.length) {

        let errors_html = "<strong>Molimo ispravite sljedeće greške:</strong>";
        errors_html += "<ol>";

        $.each(errors, function (i, error) {
            errors_html += "<li>" + error + "</li>";
        });

        errors_html += "</ol>";

        $('#javascript-error').html(errors_html);
        $('#javascript-error-container').removeClass('d-none');

        window.scrollTo(0, 0);

        return false;
    } else {
        $('#javascript-error-container').addClass('d-none');
        return true;
    }

}

window.validate = validate;

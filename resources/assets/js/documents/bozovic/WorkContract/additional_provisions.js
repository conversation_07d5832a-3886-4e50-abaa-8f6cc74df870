$(document).ready(function () {

    // containers
    $('#regulate_competition_1').click(function () {
        $('#regulate_competition_container').removeClass("sliding-up").slideDown();
    });

    $('#regulate_competition_0').click(function () {
        $('#regulate_competition_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
    });

    $('#regulate_competition_has_compensation').click(function () {
        $('#regulate_competition_compensation_container').removeClass("sliding-up").slideDown();
        $('#regulate_competition_has_not_penalty').attr('disabled', false);
    });

    $('#regulate_competition_has_not_compensation').click(function () {
        $('#regulate_competition_compensation_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");

        $('#regulate_competition_has_penalty').trigger({
                        type: 'click',
                        programmatic: true
                    });
        $('#regulate_competition_has_not_penalty').attr('disabled', true);
    });

    $('#regulate_competition_has_penalty').click(function () {
        $('#regulate_competition_penalty_container').removeClass("sliding-up").slideDown();
    });

    $('#regulate_competition_has_not_penalty').click(function () {
        $('#regulate_competition_penalty_container').slideUp(400, function() { $(this).removeClass("sliding-up");  }).addClass("sliding-up");
    });


});

$(document).ready(function () {

    let max_authorized_employer_persons = 10;


    // toggle authorized employer persons
    $('#authorized_employer_persons_exist').click(function (e) {

        // if tooltip was clicked, ignore and exit
        if($('.tooltip-inner:visible').length){
            $(this).prop("checked", !$(this).is(':checked'));
            return null;
        }

        // if no additional employer persons exist yet (only template div is present), add one
        if ($('.remove_authorized_employer_person').length == 1) {
            $('#add_authorized_employer_person').trigger({
                        type: 'click',
                        programmatic: true
                    });
        }
        if ($(this).is(':checked')) {
            $('#authorized_employer_persons_container').removeClass('d-none');
        } else {
            $('#authorized_employer_persons_container').addClass('d-none');
        }
    });

    // add additional authorized employer person
    $('#add_authorized_employer_person').click(function () {
        if ($('.remove_authorized_employer_person:visible').length <= max_authorized_employer_persons) {
            let new_authorized_employer_person = $('#authorized_employer_person_template').html().replace(new RegExp('{INDEX}', 'g'), Date.now());
            $('#authorized_employer_persons').append(new_authorized_employer_person);
            bindRemoveAuthorizedEmployerPerson();

            $(function () {
                $('[data-toggle="tooltip"]').tooltip()
            });

            window.bindMapsAutofillInputs($('.maps-autofill-input'));
        }
    });

    // remove additional authorized employer person
    function bindRemoveAuthorizedEmployerPerson() {
        $('.remove_authorized_employer_person').unbind('click').click(function () {
            let target = $(this).closest('.authorized_employer_person_content');

            target.hide('slow', function () {
                target.remove();

                // if no additional authorized employer persons exist (only template div is present), un-select checkbox
                if ($('.remove_authorized_employer_person').length == 1) {
                    $('#authorized_employer_persons_exist').trigger({
                        type: 'click',
                        programmatic: true
                    });
                }
            }).addClass("sliding-up");

        });
    }

    bindRemoveAuthorizedEmployerPerson();

    // generate parties
    function appendParties(e) {
        // generate and save party fields
        let parties = [];

        // if empty authorized employer persons
        if (!$('#employer_content').find('.authorized_employer_person_content:visible:not(.sliding-up)').length) {
            // add employer
            parties.push({
                label: "Poslodavac (" + $('input[name="employer_name"]').val().trim() + ")",
                name: $('input[name="employer_name"]').val().trim(),
                side: "left"
            })
        } else {
            // else, add authorized persons
            $('#employer_content').find('.authorized_employer_person_content:visible:not(.sliding-up)').each(function () {
                parties.push({
                    label: "Za Poslodavca (" + $('input[name="employer_name"]').val().trim() + ") <br> " + $(this).find('input')[0].value.trim() + " - " + $(this).find('input')[1].value.trim(),
                    name: $(this).find('input')[1].value.trim(),
                    side: "left"
                })
            })
        }

        parties.push({
            label: "Radnik (" + $('#employee_content').find('input')[0].value.trim() + ")",
            name: $('#employee_content').find('input')[0].value.trim(),
            side: "right"
        });

        // create parties element, populate it and append to form
        if (!$('#parties').length) {
            $('#form-container').find('form').append("<input type='hidden' name='parties' id='parties'>");
        }

        $('#parties').val(JSON.stringify(parties));
    }


    window.appendParties = appendParties;
});
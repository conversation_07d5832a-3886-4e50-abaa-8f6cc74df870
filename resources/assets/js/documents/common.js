$(document).ready(function () {

    let sectionContent = $('#section-content');
    let documentPreview = $('#document-preview');
    let formContainer = $('#form-container');
    let errorContainer = $('#javascript-error-container');
    let form = formContainer.find('form');

    let isMobileDevice = window.innerWidth < 992;

    // on document form submit
    $('.submit-link').on('click', function (e) {

        // if validation exists and fails, stop execution
        if(!form[0].reportValidity()) {
            e.preventDefault();
            return;
        }
        else if (typeof window.validate == 'function' && !window.validate()) {
            e.preventDefault();
            return;
        }

        processSectionFormSubmit();
        form.submit();
    });

    function processSectionFormSubmit(ajax = false, isLivePreview = false) {
        if (!ajax) {
            // remove all hidden inputs (except token and parties)
            form.find(":hidden").not('option').not($("input[name='_token']")).not($("input[name='parties']")).remove();
        }

        // append document parties information
        if (window["appendParties"]) {
            window["appendParties"]();
        }

        // append missing data field
        if (!isLivePreview) {
            let missingData = getMissingRequiredData();
            if (missingData.length) {
                let missingDataNames = missingData.map(function () {
                    return $(this).attr('name');
                }).get();
                let missingDataJson = JSON.stringify(missingDataNames);
                form.append('<input type="hidden" name="missing_data" value=\'' + missingDataJson + '\'/>');
            }
        }

    }

    // on focus radio input text, select radio
    $('.radio_input_text').on('focus', function () {
        $(this).closest('.form-check').find('.form-check-input').click();
    });

    // on focus radio input text, select radio
    $('.checkbox_input_text').on('focus', function () {
        $(this).closest('.form-check').find('.form-check-input').prop('checked', true);
    });

    function getDataForAjax(isLivePreview = false) {
        processSectionFormSubmit(true, isLivePreview);

        let data = []; // Initialize an array to hold our name=value pairs

        let addedRadios = {};

        form.find('input, textarea, select').filter(function() {
            if ($(this).attr('name') === '_token' || $(this).attr('name') === 'parties' || $(this).attr('name') === 'missing_data') {
                return true;
            } else if (!$(this).is(':visible')) {
                return false;
            } else {
                // if visible, make sure it's not inside a 'sliding-up' container
                return $(this).closest('.sliding-up').length === 0;
            }
        }).each(function() {
            let name = $(this).attr('name');
            let value = $(this).val();
            let isCheckbox = $(this).is(':checkbox');
            let isChecked = $(this).is(':checked');
            let isRadio = $(this).is(':radio');

            // Check for live preview and data-currency attribute
            if (isLivePreview && $(this).data('currency')) {
                value = $(this).attr('data-value'); // Use data-value for elements with data-currency
                if(!value) {
                    value = $(this).val();
                }
            } else if (isCheckbox && !isChecked) {
                // Skip unchecked checkboxes
                return;
            } else if (isRadio) {
                if (!isChecked || addedRadios[name]) {
                    // Skip if the radio is not checked or if we've already added a radio with this name
                    return;
                }
                // Mark this radio name as added
                addedRadios[name] = true;
            }

            if(name) {
                // Only encode the value part, names with brackets are left as is
                data.push(name + "=" + encodeURIComponent(value));
            }

        });

        // Join the name=value pairs with '&' to create the query string format
        return data.join('&');
    }

    // on preview click, submit the form first and then proceed
    $('.preview-link').click(function (e) {

        e.preventDefault();

        // if validation exists and fails, stop execution
        if('reportValidity' in window && !form[0].reportValidity()) {
            return;
        }
        else if (typeof validate == 'function' && !validate()) {
            return;
        }

        let data = getDataForAjax();
        let url = form.attr('action');
        let href = $(this).attr('data-route');

        // prevent browser from blocking the window
        let windowReference = window.open();

        $.ajax({
            type: "POST",
            url: url,
            data: data, // serializes the form's elements.
            success: function (data, status) {

                if(windowReference) {
                    windowReference.location = href;
                }
                // some browsers do not allow popups
                else {
                    window.location.href = href;
                }

            },
            error: function (data) {
                if(windowReference) {
                    windowReference.close();
                }
                alert("Došlo je do pogreške. Molimo pokušajte ponovo.");
            },
        });
    });

    // dynamic datepicker
    $(document).on('focus', 'input[data-datepicker="1"]', function () {
        $(this).datepicker({
            dateFormat: "d. m. yy.",
            yearRange: "-100:+100",
            regional: 'hr',
            changeYear: true,
            changeMonth: true
        });

        $(this).keypress(function (evt) {
            evt.preventDefault();
        });
    });

    // disable scrolling on number field focus
    $(document).on('focus', 'input[type=number]', function(e) {
        $(document).on('wheel.disableScroll', 'input[type=number]', function(e) {
            e.preventDefault();
        });
    }).on('blur', 'input[type=number]', function(e) {
        $(document).off('wheel.disableScroll', 'input[type=number]');
    });


    // remind users of input capitalization errors
    function validateCapitalization(event) {
        let input = $(this).val();
        let forceCase = $(this).data('force-start-case');

        // Safely check for getModifierState
        let isCapsLock = false;
        if (event && typeof event.getModifierState === 'function') {
            isCapsLock = event.getModifierState("CapsLock");
        } else if (
            event.originalEvent &&
            typeof event.originalEvent.getModifierState === 'function'
        ) {
            isCapsLock = event.originalEvent.getModifierState("CapsLock");
        }

        let errorMessage = "";

        if (input.length && isLetter(input.charAt(0))) {
            if (forceCase === "lower" && startsWithCapital(input)) {
                errorMessage = "Ovo polje mora započinjati malim početnim slovom";

                if (isCapsLock) {
                    errorMessage += ". Isključite CAPS LOCK";
                }
            } else if (forceCase === "upper" && !startsWithCapital(input)) {
                errorMessage = "Ovo polje mora započinjati velikim početnim slovom";
            }
        }

        if (errorMessage.length) {
            $(this).closest(".text-input").addClass("has-error");
        } else {
            $(this).closest(".text-input").removeClass("has-error");
        }

        $(this).closest(".text-input").find(".text-input-error").html(errorMessage);
    }


    function startsWithCapital(word){
        return (word.charAt(0) === word.charAt(0).toUpperCase())
    }

    function isLetter(char) {
        return char.toUpperCase() !== char.toLowerCase();
    }

    // validate capitalization
    $(document).on("keyup", 'input[data-force-start-case], textarea[data-force-start-case]', validateCapitalization);

    // in radio-input divs, on focus text/dropdown input, check radio box
    $(document).on('focus', '.radio-input input, .radio-input select', function () {
        $(this).closest('.radio-input').find('input[type=radio]').prop('checked', true).trigger('change');
    });

    // on currency inputs, format currency
    $(document).on('change keyup', 'input[data-currency]', function(event) {
        const $this = $(this);
        let shouldFormat = true;

        if ($this.closest('.dropdown-text-input').length) {
            shouldFormat = $this.closest('.dropdown-text-input').find('select').val() === 'eur';
        }

        if (shouldFormat) {
            const formattedValue = formatCurrencyInput($this);

            if (event.type === 'change') {
                $this.val(formattedValue);
            } else if (event.type === 'keyup') {
                $this.attr('data-value', formattedValue || null);
            }
        } else {
            $this.removeAttr('data-value');
        }
    });


    function formatCurrencyInput(el) {
        // check if attached to a dropdown
        let dropdownConditional = el.data('currency-dropdown-conditional');

        // if dropdown is not currency, return without formatting
        if(dropdownConditional) {
            if(el.closest('.input-group').find('select').val() !== dropdownConditional) {
                return;
            }
        }

        let number = el.val();

        if(number.length) {

            let currency = "EUR";

            number = number.replace(/[^\d.,]/g, "");	// remove non digit, comma or dot characters
            number = number.replace(',', '.');	// replace all commas with dots
            number = number.replace(/,/g,'.').replace(/[.](?!\d*$)/g,''); // replace all dots except last one

            if(number.length) {

                number = parseFloat(number);

                const formatter = new Intl.NumberFormat('hr-HR', {
                    style: 'currency',
                    currency: currency,
                });

                return formatter.format(number).replace('€', '').trim();
            } else {
                // invalid input
                return '';
            }
        }
    }


    // when input is attached to a dropdown where currency can be selected, when currency is selected, format text input to currency
    $(document).on('change', '.dropdown-text-input .custom-select', function(e) {
        if($(this).val() === 'eur') {
            let input = $(this).closest('.dropdown-text-input').find('input');
            input.val(formatCurrencyInput(input));
        }
    });

    // on form change - update document preview
    function onFormChangeUpdateDocumentPreview() {

        let debouncedUpdatePreview = debounce(function(e) {
            updatePreview();
        }, 300);

        // debounce function to limit how often a function can fire
        function debounce(func, wait, immediate) {
            let timeout;
            return function() {
                let context = this, args = arguments;
                let later = function() {
                    timeout = null;
                    if (!immediate) func.apply(context, args);
                };
                let callNow = immediate && !timeout;
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
                if (callNow) func.apply(context, args);
            };
        }

        // bind events for updating document preview
        formContainer
            .on('click', 'a', updatePreview)
            .on('keyup', 'input[type="text"], input[type="number"], textarea', debouncedUpdatePreview)
            .on('change', 'input[type="text"], input[type="number"], textarea', updatePreview)
            .on('change', 'input[type="checkbox"], input[type="radio"], select', updatePreview);

        $(document).on('updatePreviewEvent', updatePreview)
            .on('mapsAutoFill', updatePreview);

        function updatePreview(e) {

            // if programmaticly triggered, do not update preview
            if(e && e.programmatic) {
                return true;
            }

            // do not trigger on submit link or preview link
            if (e && e.target && ($(e.target).hasClass('submit-link') || $(e.target).hasClass('preview-link') || $(e.target).closest('.fancybox').length)) {
                return true;
            }

            let url = documentPreview.attr('data-url');
            let data = getDataForAjax(true);

            let previousHtml = documentPreview.html();

            $.ajax({
                type: 'POST',
                url: url,
                data: data,
                success: function (data) {
                    let $newContent = $('<div>').html(data);
                    let $previousContent = $('<div>').html(previousHtml);

                    // do nothing if contents are identical
                    if ($newContent.text() === $previousContent.text()) {
                        return;
                    }

                    // replace html
                    documentPreview.html($newContent.html());

                    highlightChanges($previousContent);

                    scrollPreview(
                        $('#document-preview .highlighted').first(),
                        200
                    );
                }
            })

        }

        function highlightChanges($previousContent) {

            function stripParagraphEnumeration(str) {
                let noLeadingNumbers = str.replace(/^\d+(\.\d+)*\.\s*/, '');
                return noLeadingNumbers.replace(/^Članak\s+\d+\.\s*/, '');
            }

            function compareAndHighlight(elementType) {

                documentPreview.find(elementType).each(function() {
                    let $currentElement = $(this);
                    let currentText = $currentElement.text().trim();

                    if(elementType === 'p') {
                        currentText = stripParagraphEnumeration($currentElement.text().trim());
                    }

                    let isFound = false;

                    $previousContent.find(elementType).each(function() {
                        let previousText = $(this).text().trim();

                        if(elementType === 'p') {
                            previousText = stripParagraphEnumeration(previousText);
                        }

                        if(elementType === 'li') {
                            // rtrim dots (last li often has a dot)
                            previousText = previousText.replace(/\s*\.\s*$/, '');
                            currentText = currentText.replace(/\s*\.\s*$/, '');
                        }

                        if (currentText === previousText) {
                            isFound = true;
                            return false;
                        }
                    });

                    if (!isFound) {
                        // if it's not inside a table inside #signatures-segment, or a table with id 'signatures-segment',
                        if(!$currentElement.closest('table').is('#signatures-segment') && !$currentElement.closest('#signatures-segment table').length) {
                            $currentElement.addClass('highlighted');
                        }
                    }
                });
            }

            compareAndHighlight('p');
            compareAndHighlight('td');
            compareAndHighlight('li');
        }
    }

    // if lg screen, setup document preview
    if (!isMobileDevice) {
        // bind form change event
        onFormChangeUpdateDocumentPreview();

        let sectionDocumentLocation = documentPreview.find('[data-section="' + documentPreview.attr('data-section') + '"]');
        sectionDocumentLocation.find('p:not(.article-header)').first().addClass('highlighted');

        // show section content after grid has been adjusted
        sectionContent.fadeIn(100);

        // scroll to the appropriate section
        scrollPreview(
            sectionDocumentLocation.find('.article-body').first()
        );
    } else {
        // show section content after grid has been adjusted
        sectionContent.fadeIn(100);
    }

    function scrollPreview(element, speed = 0) {
        if(element.length) {
            let cardBody = documentPreview.closest('.card-body');

            let targetScrollTop = element.offset().top - cardBody.offset().top + cardBody.scrollTop() - 25;
            let cardBodyPaddingTop = parseInt(cardBody.css('padding-top'), 10);
            targetScrollTop -= cardBodyPaddingTop;

            cardBody.animate({
                scrollTop: targetScrollTop
            }, speed);
        }
    }

    let sectionFormChanged = false;

    // if any of the fields has been changed or any link clicked, section form was changed
    formContainer.on('change', 'input, textarea, select', function(e) {
        if(e && !e.programmatic) {
            sectionFormChanged = true;
            // remove class has-warning from target
            $(e.target).removeClass('has-warning');

            // if select2, remove class has-warning from the container
            let select2 = $(e.target).siblings('.select2-container');
            if(select2.length) {
                select2.removeClass('has-warning');
            }

        }
    }).on('click', 'a', function(e) {
        if(e && !e.programmatic) {
            sectionFormChanged = true;
        }
    });

    // on unsaved changes and .nav-link click, ask user to confirm
    $(document).on('click', '.nav-link', function(e) {
        if(sectionFormChanged) {
            e.preventDefault();
            // save intended page
            $('#intended-exit-page').val($(this).attr('href'));
            $('#saveChangesModal').modal('show');
        }
    })

    // if user wants to continue without saving, redirect to intended page
    $('#continue-without-saving').click(function (e) {
        e.preventDefault();
        window.location = $('#intended-exit-page').val();
    });

    // if user wants to save and continue, save the form and redirect to intended page
    $('#save-and-continue').click(function (e) {

        e.preventDefault();

        // if validation exists and fails, stop execution
        if('reportValidity' in window && !form[0].reportValidity()) {
            $('#saveChangesModal').modal('hide');
            return;
        }
        else if (typeof validate == 'function' && !validate()) {
            $('#saveChangesModal').modal('hide');
            return;
        }

        // get data
        let data = getDataForAjax();
        let url = form.attr('action');

        $.ajax({
            type: "POST",
            url: url,
            data: data, // serializes the form's elements.
            success: function (data, status) {
                // redirect to intended page
                window.location = $('#intended-exit-page').val();
            },
            error: function () {
                alert("Došlo je do pogreške. Molimo pokušajte ponovo.");
            },
        });
    });


    // toggle preview
    let previewToggle = $('.preview-toggle');
    let previewSidebar = $('#preview-sidebar');
    let actionsSidebar = $('#actions-sidebar');
    let navContainer = $('#nav-container');
    let columns = formContainer.find('[class*="col-"]');

    // Store the original column classes for each column
    columns.each(function () {
        let currentClass = $(this).attr('class');
        let gridClass = currentClass.match(/\bcol-(?:xs|sm|md|lg|xl)-\d+\b/g) || ['col'];
        $(this).attr('data-original-class', gridClass.join(' '));

        if(sectionContent.attr('data-preview')) {
            $(this).attr('class',  currentClass.replace(/\bcol-(?:xs|sm|md|lg|xl)-\d+\b/g, 'col-lg-12'));
        }
    });

    previewToggle.click(function (e) {

        e.preventDefault();

        // remember setting on the backend
        let action = $(this).attr('data-action');

        sectionContent.attr('data-preview', action === 'on' ? '1' : '0');

        $.ajax({
            type: "POST",
            url: '/documentPreviewSetting',
            data: {action: action, _token: $("input[name='_token']").val()}
        });

        // get columns again
        let columns = formContainer.find('[class*="col-"]');

        // When hiding the sidebar, revert to the original grid classes
        if (previewSidebar.is(':visible')) {
            columns.each(function () {
                let originalClass = $(this).attr('data-original-class');
                let currentClass = $(this).attr('class');
                let newClass = currentClass.replace(/\bcol-lg-12\b/g, originalClass);
                $(this).attr('class', newClass);

            });
            previewSidebar.removeClass('d-lg-block');
            actionsSidebar.addClass('d-lg-block');
            formContainer.attr('class', 'col-lg-9');
            navContainer.attr('class', 'col-lg-9 pt-3 pb-2');
            errorContainer.attr('class', errorContainer.attr('class').replace(/\bcol-lg-12\b/g, 'col-lg-9'));
            $('.select2-container').width('100%');
        }
        else // When showing the sidebar, set all columns to 'col-lg-12', keeping other classes
        {
            columns.each(function () {
                let currentClass = $(this).attr('class');
                let newClass = currentClass.replace(/\bcol-(?:xs|sm|md|lg|xl)-\d+\b/g, 'col-lg-12');
                $(this).attr('class', newClass);
            });
            previewSidebar.addClass('d-lg-block');
            actionsSidebar.removeClass('d-lg-block');
            formContainer.attr('class', 'col-lg-4');
            navContainer.attr('class', 'col-lg-12 pt-3 pb-2 section-navbar-sticky');
            errorContainer.attr('class', errorContainer.attr('class').replace(/\bcol-lg-9\b/g, 'col-lg-12'));
            $('.select2-container').width('100%');
            scrollPreview($('#document-preview .highlighted').first());
        }
    });

    // get missing required data
    function getMissingRequiredData() {
        return formContainer.find('input:visible, select:visible, textarea:visible').filter(function () {
            if ($(this).val().length === 0) {

                let label = $(this).closest('.form-group').find('label');
                if (label.length === 0) {
                    label = $(this).closest('label');
                }

                if (label.text().indexOf('(opcionalno)') !== -1) {
                    return false;
                }

                // radio input (special case) - check if the belonging radio input is checked
                let parentRadioInput = $(this).closest('.radio-input');
                if (parentRadioInput.length > 0 && parentRadioInput.find('input[type="radio"]:checked').length === 0) {
                    return false;
                }

                // form check (special case) - check if the belonging radio input is checked
                let parentFormCheck = $(this).closest('.form-check');
                if (parentFormCheck.length > 0 && (parentFormCheck.find('input[type="radio"]').length && parentFormCheck.find('input[type="radio"]:checked').length === 0)) {
                    return false;
                }

                if (parentFormCheck.length > 0 && (parentFormCheck.find('input[type="checkbox"]').length && parentFormCheck.find('input[type="checkbox"]:checked').length === 0)) {
                    return false;
                }

                if($(this).hasClass('radio_input_text')) {
                    let sublingRadioInput = $(this).siblings('.form-check-input');
                    if(sublingRadioInput.length > 0 && !sublingRadioInput.is(':checked')) {
                        return false;
                    } else {
                        // maybe it's inside input group container
                        let inputGroupContainer = $(this).parent('.input-group');

                        if(inputGroupContainer.length) {
                            let sublingRadioInput = inputGroupContainer.siblings('.form-check-input');
                            if(sublingRadioInput.length > 0 && !sublingRadioInput.is(':checked')) {
                                return false;
                            }
                        }
                    }
                }

                return true;
            }

            return false;
        });
    }

    // highlight any missing data
    let missingData = formContainer.attr('data-missing');
    if(missingData.length) {
        if(missingData === '*') {
            // add has_warning class to all required visible inputs
            let inputs = getMissingRequiredData();
            $.each(inputs, function() {
                $(this).addClass('has-warning');
            })
        } else {
            // loop through missing data
            missingData = JSON.parse(missingData);
            for(let i = 0; i < missingData.length; i++) {
                let selector = 'input[name="' + missingData[i] + '"], select[name="' + missingData[i] + '"], textarea[name="' + missingData[i] + '"]';
                $(selector).addClass('has-warning');
            }
        }

        // highlight select2 containers (delay for intialization)
        setTimeout(function () {
            // for all selects that have has-warning class, add has-warning class to their sibling 'select2' element and remove it from themselves
            formContainer.find('select.has-warning').each(function () {
                let select2 = $(this).siblings('.select2-container');
                if(select2.length) {
                    select2.addClass('has-warning');
                    $(this).removeClass('has-warning');
                }
            })
        }, 20)
    }

    // make it work with different screens and element heights
    function setDocumentPreviewTopAndHeight() {
        let topNavbarHeight = $('nav').first().outerHeight();
        let sectionNavbarHeight = $('#sectionNavbar').outerHeight();
        let progressHeight = $('.progress').first().outerHeight();

        // set preview top attribute
        $('#preview-sidebar .sticky-top').css('top', topNavbarHeight + sectionNavbarHeight + progressHeight + 10);
    }

    setDocumentPreviewTopAndHeight();

    function showWizardTutorialModal() {

        let introModal = $('#wizardTutorialModal');

        if(!isMobileDevice && introModal.length) {
            introModal.modal('show');

            $.ajax({
                url: '/user/wizard/tutorial',
                type: 'POST',
                data: {
                    _token: $("[name='_token']").val(),
                }
            });
        }
    }

    showWizardTutorialModal();

});

/*
    // binds all address related fields to google maps api for autofill
    // must include google places api script

    1. container element must have a class of "maps_autofill_container"
    2. elements which trigger autofilling must be passed as input and have correct "data-maps-autofill-type" attribute (address or (cities))
    3. all elements participating in being autofilled must have "data-maps-autofill" set to either address, city, zip or country

    Note: this function must be defined outside document.ready() scope!
*/
function bindMapsAutofillInputs(autoFillInputs) {

    if(typeof google === 'undefined' || typeof google.maps === 'undefined' || typeof google.maps.places === 'undefined') {
        return;
    }

    if (autoFillInputs.length) {

        for (let i = 0; i < autoFillInputs.length; i++) {

            let autocomplete = new google.maps.places.Autocomplete(
                autoFillInputs[i],
                {
                    types: [
                        $(autoFillInputs[i]).data('maps-autofill-type')
                    ]
                }
            );

            autocomplete.inputId = i;

            google.maps.event.addListener(autocomplete, 'place_changed', function () {

                let place = autocomplete.getPlace();

                if (place.hasOwnProperty('types')) {

                    let div = document.createElement('div');
                    div.innerHTML = place.adr_address;

                    let triggered_input = $(autoFillInputs[$(this)[0].inputId]);

                    triggered_input.closest('.maps_autofill_container').find('[data-maps-autofill]').each(function () {

                        switch ($(this).data('maps-autofill')) {
                            case 'address':
                                $(this).val($(div).find('span[class="street-address"]').html()).removeClass('has-warning');
                                break;
                            case 'city':
                                $(this).val($(div).find('span[class="locality"]').html()).removeClass('has-warning');
                                break;
                            case 'zip':
                                $(this).val($(div).find('span[class="postal-code"]').html()).removeClass('has-warning');
                                break;
                            case 'country':
                                $(this).val($(div).find('span[class="country-name"]').html()).removeClass('has-warning');
                                break;
                        }

                    });

                    // dispatch event
                    $(document).trigger('mapsAutoFill');

                }
            });
        }
    }
}

// bind address fields to google maps API
function bindAddressFields() {

    if(typeof google === 'undefined' || typeof google.maps === 'undefined' || typeof google.maps.places === 'undefined') {
        return;
    }

    $('input[data-address]').each((function(i, input){
        let autocomplete = new google.maps.places.Autocomplete(input);

        google.maps.event.addListener(autocomplete, 'place_changed', function () {
            // dispatch event
            $(document).trigger('mapsAutoFill');
        });
    }))
}

window.bindAddressFields = bindAddressFields;

// temporarily disable google maps autofill
window.bindMapsAutofillInputs = function (autoFillInputs) {
    return false;
};

window.initGoogleMaps = function () {

    window.bindMapsAutofillInputs = bindMapsAutofillInputs;

    // bind all existing maps autofill inputs
    window.bindMapsAutofillInputs($('.maps-autofill-input'));

    bindAddressFields();

}

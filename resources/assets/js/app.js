$(document).ready(function () {

    // bootstrap popovers
    $('[data-toggle="tooltip"]').tooltip({html: true})

    // bind confirmation dialogs
    bindPleaseConfirm();

    function bindPleaseConfirm() {
        $(document).on('click', '.please-confirm', function (e) {
            let element = $(this);

            // If not confirmed yet, show the confirmation modal
            if (!element.data('confirmed')) {
                e.preventDefault();
                let message = element.data('message');

                pleaseConfirm(message).then(result => {
                    if (result) {
                        if (element.attr('href')) { // Check if href attribute exists
                            window.location.href = element.attr('href');
                        } else {
                            // If no href attribute exists, trigger the click but ensure the modal doesn't pop up again
                            element.data('confirmed', true).trigger('click');
                        }
                    }
                });

            } else {
                // Reset confirmation
                element.data('confirmed', false);
            }
        });
    }

    // window.pleaseConfirm(message).then(result => { });
    window.pleaseConfirm = pleaseConfirm;

    function pleaseConfirm(message) {
        return new Promise((resolve) => {
            // Set the message
            $('#pleaseConfirmModal .modal-body').text(message);

            // Show the modal
            $('#pleaseConfirmModal').modal('show');

            // Handle the buttons
            $('#pleaseConfirmOK').off('click').on('click', function() {
                $('#pleaseConfirmModal').modal('hide');
                resolve(true);
            });

            $('#pleaseConfirmCancel, .close').off('click').on('click', function() {
                $('#pleaseConfirmModal').modal('hide');
                resolve(false);
            });
        });
    }

    if (!Cookies.get('terms_accepted')) {
        $('#termsModal').modal();
    }

    $('.cookie-settings').on('click', function(e) {
        e.preventDefault();
        $('#termsModal').modal('hide');

        $('#cookieSettingsModal').modal();
    });

    function addGoogleAnalyticsTag() {

        // disable on local, staging environments
        if(window.location.hostname.includes('local') || window.location.hostname.includes('staging')) {
            return false;
        }

        removeGoogleAnalyticsTag();
        // append Google Analytics tags to head
        $('head').prepend('<script class="ga-script" async src="https://www.googletagmanager.com/gtag/js?id=G-DJ9FX7J5K2"></script>\n' +
            '    <script class="ga-script">\n' +
            '        window.dataLayer = window.dataLayer || [];\n' +
            '        function gtag(){dataLayer.push(arguments);}\n' +
            '        gtag(\'js\', new Date());\n' +
            '\n' +
            '        gtag(\'config\', \'G-DJ9FX7J5K2\');\n' +
            '    </script>');
    }

    function removeGoogleAnalyticsTag() {
        if($('.ga-script').length) {
            $('.ga-script').remove();
        }
    }

    $('#agree-terms').on('submit', function (e) {
        e.preventDefault();

        Cookies.set('terms_accepted', 1, {expires: 365});
        Cookies.set('ga_accepted', 1, {expires: 365});

        $.ajax({
            url: '/cookiePreferences',
            type: 'POST',
            data: {
                _token: $("[name='_token']").val(),
                ga_accepted: 1,
            }
        });

        addGoogleAnalyticsTag();

        $('#analyticsCookiesSwitch').prop('checked', true);

        $('#termsModal').modal('hide');
    });

    $('#cookie-settings-form').on('submit', function (e) {

        e.preventDefault();

        let ga_accepted = $('#analyticsCookiesSwitch').is(':checked') ? 1 : 0;

        $.ajax({
            url: '/cookiePreferences',
            type: 'POST',
            data: {
                _token: $("[name='_token']").val(),
                ga_accepted: ga_accepted,
            }
        });

        if(ga_accepted) {
            addGoogleAnalyticsTag();
        }
        else{
            removeGoogleAnalyticsTag();
        }

        Cookies.set('terms_accepted', 1, {expires: 365});
        $('#cookieSettingsModal').modal('hide');
    });

    if ($('#select-document').length) {

        $('#select-document').select2({
            minimumResultsForSearch: 5,
            placeholder: 'Odaberi dokument...',
            search: true,
            escapeMarkup: function (markup) {
                return markup; // Allows rendering of HTML markup
            },
            templateResult: function (data) {
                // if newest template, add badge
                if (data.text.includes('[NOVO]')) {
                    data.text = data.text.replace('[NOVO]', '').trim();
                    let $option = $('<div></div>');
                    $option.append('<span class="badge badge-success badge-pill">NOVO</span>' + ' ' + data.text);
                    return $option;
                } else if (data.text.includes('[OSVJEŽENO]')) {
                    data.text = data.text.replace('[OSVJEŽENO]', '').trim();
                    let $option = $('<div></div>');
                    $option.append('<span class="badge badge-orange badge-pill">OSVJEŽENO</span>' + ' ' + data.text);
                    return $option;
                } else {
                    return data.text;
                }
            },
            language: {
                errorLoading: function () {
                    return 'Nije pronađen nijedan dokument.';
                },
                loadingMore: function () {
                    return 'Pretražujem dokumente...';
                },
                maximumSelected: function (args) {
                    return 'Maksimalan broj odabranih stavki je ' + args.maximum;
                },
                noResults: function () {
                    return 'Nema rezultata';
                },
                searching: function () {
                    return 'Pretražujem dokumente...';
                }
            },
            ajax: {
                url: '/template/search',
                type: 'post',
                dataType: 'json',
                delay: 150,
                data: function (params) {
                    return {
                        _token: $("[name='_token']").val(), // csrf token
                        query: params.term, // search term
                    };
                },
                processResults: function (data) {
                    let results = [];

                    if(data['query']){
                        $.each(data['results'], ($_i, _data) => {
                            results.push({
                                'id': _data['id'],
                                'text': _data['title'],
                            });
                        });
                    }
                    else{
                        $.each(data['results'], (_category, _data) => {

                            let templates = [];

                            $.each(_data['templates'], (__order_index, __template_data) => {
                                templates.push({
                                    'id': __template_data['id'],
                                    'text': __template_data['title'],
                                });
                            });

                            results.push({
                                'text': _data['category'],
                                'children': templates
                            });
                        });
                    }

                    return {
                        results: results
                    };
                }
            },

        });
    }

    if ($('#search-document').length) {

        $('#search-document').select2({
            minimumResultsForSearch: 5,
            placeholder: $('#search-document-placeholder').attr('placeholder'),
            search: true,
            escapeMarkup: function (markup) {
                return markup; // Allows rendering of HTML markup
            },
            templateResult: function (data) {
                // if newest template, add badge
                if (data.text.includes('[NOVO]')) {
                    data.text = data.text.replace('[NOVO]', '').trim();
                    let $option = $('<div></div>');
                    $option.append('<span class="badge badge-success badge-pill">NOVO</span>' + ' ' + data.text);
                    return $option;
                } else if (data.text.includes('[OSVJEŽENO]')) {
                    data.text = data.text.replace('[OSVJEŽENO]', '').trim();
                    let $option = $('<div></div>');
                    $option.append('<span class="badge badge-orange badge-pill">OSVJEŽENO</span>' + ' ' + data.text);
                    return $option;
                } else {
                    return data.text;
                }
            },
            language: {
                errorLoading: function () {
                    return 'Nije pronađen nijedan dokument.';
                },
                loadingMore: function () {
                    return 'Pretražujem dokumente...';
                },
                maximumSelected: function (args) {
                    return 'Maksimalan broj odabranih stavki je ' + args.maximum;
                },
                noResults: function () {
                    return 'Nema rezultata';
                },
                searching: function () {
                    return 'Pretražujem dokumente...';
                }
            },
            ajax: {
                url: '/template/search',
                type: 'post',
                dataType: 'json',
                delay: 150,
                data: function (params) {
                    return {
                        _token: $("[name='_token']").val(), // csrf token
                        query: params.term, // search term
                    };
                },
                processResults: function (data) {
                    let results = [];

                    if(data['query']){
                        $.each(data['results'], ($_i, _data) => {
                            results.push({
                                'id': _data['id'],
                                'text': _data['title'],
                            });
                        });
                    }
                    else{
                        $.each(data['results'], (_category, _data) => {

                            let templates = [];

                            $.each(_data['templates'], (__order_index, __template_data) => {
                                templates.push({
                                    'id': __template_data['id'],
                                    'text': __template_data['title'],
                                });
                            });

                            results.push({
                                'text': _data['category'],
                                'children': templates
                            });
                        });
                    }

                    return {
                        results: results
                    };
                },
            },

        }).on('select2:select', function (e) {
            // on select, redirect to landing page
            window.location.href = "/document/show?template_id="+e.params.data.id;
        });
    }

    $('#search-document-placeholder').on('focus', function(e){
        $(this).hide();
        $('#search-document-container').show();
        $('#search-document').select2('open');
    })

    if ($('#documentCounter').length) {
        let counter = new CountUp(
            'documentCounter',
            0,
            $('#documentCounter').data('count'),
            0,
            2,
            {
                separator: '.',
                decimal: ','
            }
        );

        if (!counter.error) {
            counter.start();
        } else {
            console.error(counter.error);
        }
    }

    $('.scrollTop').click(function () {
        $("html, body").animate({scrollTop: 0}, "slow");
        return false;
    });

    // javascript error reporting
    try {
        window.onerror = function (err, url, line, col, errorObj) {

            $.ajax({
                url: '/jserror',
                type: 'POST',
                data: {
                    errorMsg: err,
                    errorLine: line,
                    errorColumn: col || 'N/A',
                    errorObj: errorObj ? errorObj.stack : 'N/A',
                    scriptURL: url,
                    queryString: document.location.search,
                    url: document.location.pathname,
                    referrer: document.referrer,
                    userAgent: navigator.userAgent
                }
            });

            if (!shouldHideErrorAlert(err, line)) {
                //alert("Došlo je do pogreške. Molimo osvježite stranicu i pokušajte ponovo. Error: " + err);
            }

            // true == suppress browser error messages
            return true;
        };

    } catch (e) {

    }

    // prevent scrolling on number inputs
    document.addEventListener('wheel', function(event) {
        if (document.activeElement.type === 'number') {
            event.preventDefault();
            document.activeElement.blur();
        }
    }, { passive: false });


    // helper functions
    let shouldHideErrorAlert = function (err, line) {
        let allowedErrors = [
            'Uncaught TypeError: K.charCodeAt is not a function',
            'Uncaught TypeError: U.charCodeAt is not a function',
        ]

        let allowedLines = [0, 1];

        return allowedErrors.includes(err) || allowedLines.includes(line);
    }

    $.extend({
        distinct : function(anArray) {
            var result = [];
            $.each(anArray, function(i,v){
                if ($.inArray(v, result) === -1) result.push(v);
            });
            return result;
        }
    });

});

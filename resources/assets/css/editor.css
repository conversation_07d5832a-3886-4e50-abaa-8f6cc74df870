@media (min-width: 768px) {
    .editable-segment {
        text-align: justify;
        text-justify: inter-ideograph;
    }
}

.editable-segment table{
    text-align: left;
}

h2 {
    padding-top: 50px;
    padding-bottom: 50px;
}

td.strong{
    font-weight: bold;
}

.article-title {
    font-weight: bold;
}

#signees-table{
    border: 1px solid #e9e9e9;
}

#signees-table th, #signees-table td{
    vertical-align: middle;
}

#attachments-table, #recipients-table, #remedies-table{
    border: 1px solid #e9e9e9;
}

#attachments-table th, #attachments-table td,
#recipients-table th, #recipients-table td,
#remedies-table th, #remedies-table td {
    vertical-align: middle;
}

.editable-segment .table {
    max-width: 99%!important;
    border-collapse: collapse;
}

.editable-segment .table, .editable-segment .table th, .editable-segment .table td {
    border: 1px solid #bfbfbf;
}

.editable-segment .table th, .editable-segment .table td{
    vertical-align: middle;
    padding: 10px;
}

.segment-container {
    border: 1px solid transparent;
    padding: 20px;
}
.segment-container:hover{
    border: 1px solid rgba(186, 186, 186, 0.32);
}

.segment-container.active{
    border: 1px solid rgba(186, 186, 186, 0.32);
}

.segment-toolbar ul{
    list-style-type: none;
    margin:0;
    padding:0;
}

.segment-toolbar ul li{
    display:inline;
}

.segment-toolbar {
    min-height: 50px;
}

.segment-toolbar-options {
    display: none;
}

.segment-container.active .segment-toolbar-options {
    display: block!important;
}

.mce-edit-focus{
    outline: none;
}

.tox.tox-tinymce-inline .tox-editor-header{
    border: none!important;
}

.tox .tox-toolbar__group {
    margin-left: auto !important;
    margin-right: auto !important;
}

.tox.tox-tinymce-inline {
    border-bottom: 2px solid #eee!important;
    border-radius: 0!important;
}

#editor-toolbar{
    top: -8px;
    z-index: 1000; /* Make sure this is lower than the navbar dropdown menu z-index */
}

@media (max-width: 992px) {
    #editor-toolbar{
        top: -10px;
    }
}

.mce-content-body:not([dir=rtl])[data-mce-placeholder]:not(.mce-visualblocks)::before {
    left: 30px!important;
}

body {
    counter-reset: articleIndex
}

.article-body:before{
    content: '';
    counter-reset: paragraphIndex;
}

.paragraph-list > li:before {
    display: inline-block;
    margin-right: 5px;
    content: counter(articleIndex)'.' counter(paragraphIndex, decimal) '.';
    counter-increment: paragraphIndex;
}

.paragraph-list {
    list-style-type: none;
    padding-left: 0;
}

.paragraph-list > li{
    margin-bottom: 1rem;
    font-weight: normal!important;
    text-decoration: none!important;
    font-style: normal!important;
    word-break: break-word;
}

.paragraph-list > li > ol {
    padding-top: 10px;
}

.paragraph-list > li > ul {
    padding-top: 10px;
    list-style-type: disc;
}



.mce-content-body audio[data-mce-selected], .mce-content-body embed[data-mce-selected], .mce-content-body img[data-mce-selected], .mce-content-body object[data-mce-selected], .mce-content-body table[data-mce-selected], .mce-content-body video[data-mce-selected] {
    outline: none!important;
}

.mce-content-body{
    outline: none!important;
}

.article-list{
    list-style-type: none;
    padding-left: 0;
}

.article-list li{
    font-weight: bold;
    text-decoration: none!important;
    font-style: normal!important;
}

.article-list li:before {
    display: inline-block;
    margin-right: 5px;
    content: '\010c' 'lanak ' counter(articleIndex)'.';
    counter-increment: articleIndex;
}


.tox-tbtn:not([title="Numeriraj stavak"]) .tox-tbtn__select-label {
    font-size: 1.1em;
    background-color: rgb(241 241 241 / 58%);
    padding: 0.375rem 0.75rem;
    line-height: 1.5;
    border-radius: 0.25rem;
    user-select: none;
    font-weight: 500!important;
}

.tox-tbtn[title="Numeriraj stavak"] .tox-tbtn__select-label
{
    font-weight: 600!important;
}

.tox-toolbar__group button:first-child:hover, .tox-toolbar__group button:first-child:focus, .tox-toolbar__group button:first-child:active{
    background: transparent;
}

.underline{
    text-decoration: underline;
}

.editable-segment[data-type="article"] .segment-dirty{
    display: inline;
}

.article-header{
    display: inline;
}


@media (max-width: 768px) {
    .signature-line {
        font-size: 0.3em!important;
    }
}

.signature-line {
    font-size: 0.9em;
}

.disabled-move-up {
    color: #d6d6d6!important;
    pointer-events: none!important;
}

.segment-container:last-child .move-down{
    color: #d6d6d6;
    pointer-events: none;
}

.tox-notifications-container{
    display: none!important;
}

#editor-tabs a {
    color: #000;
}

.modal{
    z-index: 16000003; /* has to be above sticky sidebar widget and alert-widget */
}

.popover, .tooltip {
    z-index: 16000004;
}

.spelling-error {
    background-color: #f8d7da;
    cursor: pointer;
}

.spelling-error:hover {
    text-decoration: underline;
}

.spelling-fixed {
    background-color: #ddf8d7;
    cursor: inherit;
}

.spellcheck-spinner {
    margin-right: -10px;
}

.spellcheck:focus {
    text-decoration: none!important;
}

/* For desktop browsers */
@media only screen and (min-width: 1024px) {
    #editor span[data-toggle="tooltip"] {
        background-color: rgb(251, 238, 184);
    }
}

.overflow-x-auto {
    overflow-x: auto;
}

#editor-tabs {
    overflow-x: auto;
}

@media only screen and (max-width: 768px) {
    #editor-tabs {
        font-size: .9em;
    }
}

#editor-tabs .nav {
    flex-wrap: nowrap;
}

#template-body .text-center {
    text-align: center;
}

#template-body .font-weight-bold {
    font-weight: bold;
}

#template-body h2 {
    padding-top: 50px;
    padding-bottom: 50px;
}

#template-body .article-header{
    font-weight: bold;
    margin-bottom: 10px!important;
    margin-top: 10px!important;
    page-break-after: avoid;
    page-break-inside: avoid;
}

#template-body .mt-0 {
    margin-top:0!important;
}

#template-body .mb-0 {
    margin-bottom:0!important;
}

#template-body .pb-0 {
    padding-bottom:0!important;
}

#template-body .avoid-page-break{
    page-break-inside: avoid;
}

#template-body{
    text-align: justify;
    text-justify: inter-ideograph;
    font-family: Arial, serif;
}

.signature-line {
    white-space: nowrap;
    font-size: .8em;
    color: transparent;
    border-bottom: 1px solid #a9a9a9
}

#template-body .document_placeholder{
    background: #f0f6ff;
    font-weight: normal;
    color:#f0f6ff;
    border-bottom: 0.01em solid darkgrey;
}

td .document_placeholder {
    border-bottom: none!important;
}

#template-body .signatures_table{
    width:100%;
    border-collapse:separate;
    border-spacing:3em;
}

#template-body .signatures_table td{
    vertical-align:top;
}

#template-body .table {
    width: 100%;
    border-collapse: collapse;
}

#template-body .table, .table th, .table td {
    border: 1px solid #bfbfbf;
}

#template-body .table th, .table td{
    padding: 10px;
}

#template-body .strong{
    font-weight: bold;
}

#template-body .page-break {
    page-break-after: always;
}

#template-body h2.editable-segment{
    margin-top:0;
}

#document-preview .document_placeholder {
    background: none!important;
    color: transparent!important;
}

#document-preview .table td {
    text-align: left;
}

.sticky-top {
    z-index: 0;
}

.highlighted:not(.article-header) {
    background: #ffeeb9;
}

p.highlighted:not(.article-header) {
    padding: 20px;
    border-radius: 20px;
}

li.highlighted{
    padding: 10px 20px;
    border-radius: 20px;
    margin: 10px 0;
}

#template-body{
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

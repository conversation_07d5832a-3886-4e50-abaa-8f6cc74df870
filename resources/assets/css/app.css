/*
 * ===================================================================
 * LEGALDESK CSS - Professional, Minimalist, Modern
 * ===================================================================
 */

/* --- CSS Variables --- */
:root {
    --primary-color: #1a3b5d;
    --primary-hover: #142f4b;
    --primary-light: #eef2f7;
    --text-color: #333;
    --text-muted: #6c757d;
    --border-color: #ced4da;
    --border-light: #e9ecef;
    --white: #fff;
    --black: #000;
    --font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif;
}

/* --- General & Typography --- */
body {
    font-family: var(--font-family);
    color: var(--text-color);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    margin: 0;
    padding: 0;
    height: 100%;
}

html {
    margin: 0;
    padding: 0;
    height: 100%;
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-family);
    font-weight: 700;
}

.section-title {
    font-weight: 700;
    color: var(--primary-color);
}

/* --- Hero Section --- */
.hero-section {
    background-color: var(--primary-color);
    padding: 6rem 0;
    position: relative;
}

.hero-section h1 {
    font-size: 3.2rem;
    color: var(--white);
}

.hero-section .lead {
    font-size: 1.25rem;
    color: rgba(255, 255, 255, 0.85);
    max-width: 700px;
    margin: 0 auto;
}

.hero-section .btn {
    padding: 0.8rem 2rem;
    font-weight: 600;
    border-radius: 50px;
    transition: all 0.3s ease;
    border-width: 2px;
}

.hero-section .btn-light:hover {
    background-color: #f8f9fa;
    color: var(--primary-color) !important;
}

.hero-section .btn-outline-light:hover {
    background-color: var(--white);
    color: var(--primary-color) !important;
}

/* --- Features Section --- */
.features-section {
    background-color: var(--white);
}

.feature-item {
    margin-bottom: 2rem;
}

.feature-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    background-color: var(--primary-light);
    color: var(--primary-color);
    border-radius: 50%;
    font-size: 2.5rem;
    transition: all 0.3s ease;
}

.feature-item:hover .feature-icon {
    transform: translateY(-5px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.feature-item h3 {
    color: #343a40;
    font-weight: 600;
}

/* --- Value Proposition Section --- */
.value-prop-section {
    border-top: 1px solid var(--border-light);
    border-bottom: 1px solid var(--border-light);
}

.value-item {
    margin-bottom: 2rem;
}

.value-icon {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

/* --- Call to Action (CTA) Section --- */
.cta-section .btn {
    padding: 0.8rem 2.5rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 50px;
    transition: all 0.3s ease;
}

.cta-section .btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 15px rgba(26, 59, 93, 0.25);
}


/* --- Primary Color System --- */
.bg-primary,
.btn-primary {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    color: var(--white) !important;
}

.btn-primary:hover,
.btn-primary:focus {
    background-color: var(--primary-hover) !important;
    border-color: var(--primary-hover) !important;
}

.text-primary {
    color: var(--primary-color) !important;
}

.home-intro {
    font-size: 3rem !important;
}

.bg-white-5 {
    background-color: rgb(255 255 255 / 98%) !important;
}

/* --- Responsive Design --- */
@media (min-width: 768px) {
    .value-icon {
        font-size: 3rem;
        margin-bottom: 0;
    }
}

@media (max-width: 767px) {
    .hero-section {
        padding: 4rem 0;
    }

    .hero-section h1 {
        font-size: 2.2rem;
    }

    .hero-section .lead {
        font-size: 1.1rem;
    }
}

/* --- Bootstrap Theme Overrides --- */
.btn-success,
.btn-danger,
.btn-info {
    color: var(--white) !important;
}

/* --- Navigation --- */
@media (min-width: 992px) {
    .stretch-nav-full-lg {
        width: 102%;
    }
}

/* --- Tooltips --- */
.tooltip-inner {
    max-width: 400px;
    text-align: left;
}

/* --- Layout & Footer --- */
#layout-container {
    min-height: 100%;
    position: relative;
}

#layout-content {
    padding: 70px 10px 150px;
}

footer.footer {
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 150px;
    line-height: 50px;
}

footer.footer a {
    color: rgba(0, 0, 0, .9);
}

.margin-top {
    margin-top: 30px;
}

/* --- Button & Link Styles --- */
.btn-link {
    color: var(--black) !important;
}

.dropdown-item:active {
    background-color: var(--white) !important;
    color: #212529 !important;
}

/* --- DataTables Customizations --- */
table.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control,
table.dataTable.dtr-inline.collapsed > tbody > tr > th.dtr-control {
    vertical-align: middle;
    padding-left: 35px;
}

table.dataTable.dtr-inline.collapsed > tbody > tr > td:first-child:before,
table.dataTable.dtr-inline.collapsed > tbody > tr > th:first-child:before {
    background-color: var(--primary-color) !important;
}

table.dataTable > tbody > tr.child ul li {
    border-bottom: none !important;
}

table.dataTable tbody tr.selected > *,
table.dataTable.table-striped > tbody > tr.odd.selected > * {
    box-shadow: inset 0 0 0 9999px var(--primary-color);
    color: var(--white);
}

table.dataTable thead > tr > th.sorting::after,
table.dataTable thead > tr > th.sorting_asc::after,
table.dataTable thead > tr > th.sorting_desc::after,
table.dataTable thead > tr > th.sorting_asc_disabled::after,
table.dataTable thead > tr > th.sorting_desc_disabled::after,
table.dataTable thead > tr > td.sorting::after,
table.dataTable thead > tr > td.sorting_asc::after,
table.dataTable thead > tr > td.sorting_desc::after,
table.dataTable thead > tr > td.sorting_asc_disabled::after,
table.dataTable thead > tr > td.sorting_desc_disabled::after {
    content: "\25BC" !important;
}

table.dataTable thead > tr > th.sorting:before,
table.dataTable thead > tr > th.sorting_asc:before,
table.dataTable thead > tr > th.sorting_desc:before,
table.dataTable thead > tr > th.sorting_asc_disabled:before,
table.dataTable thead > tr > th.sorting_desc_disabled:before,
table.dataTable thead > tr > td.sorting:before,
table.dataTable thead > tr > td.sorting_asc:before,
table.dataTable thead > tr > td.sorting_desc:before,
table.dataTable thead > tr > td.sorting_asc_disabled:before,
table.dataTable thead > tr > td.sorting_desc_disabled:before {
    content: "\25B2" !important;
}

table.dataTable > tbody > tr.selected > td.select-checkbox:after,
table.dataTable > tbody > tr.selected > th.select-checkbox:after {
    content: "\2713 " !important;
}

table.table-bordered.dataTable {
    border-top-width: 0;
}

.table-bordered thead td,
.table-bordered thead th {
    border-bottom-width: 1px;
}

/* --- Pagination --- */
.page-item.active .page-link {
    background-color: #f2f2f2;
    border-color: #e3e3e3;
    color: var(--black);
}

.page-link {
    color: var(--black);
    border: none;
}

.page-link:focus {
    outline: none;
    box-shadow: none;
    border-color: transparent;
}

.page-link:hover {
    color: #525252;
    background-color: #f2f2f2;
    border-color: #e3e3e3;
}

/* --- Form Components --- */
.radio_input_text {
    border-radius: 5px;
    border: 1px solid #ccc;
    height: 38px;
    padding-left: 10px;
}

.checkbox_input_text {
    border-radius: 5px;
    border: 1px solid #ccc;
}

/* --- Links --- */
a,
a:hover {
    color: var(--black);
}

/* --- Utility Classes --- */
.sticky-offset {
    top: 75px;
}

.sticky-offset-100 {
    top: 100px;
}

.sticky-offset-125 {
    top: 125px;
}

.lead {
    font-size: 1.2rem;
    letter-spacing: 0.2px;
}

/* --- Custom Fonts --- */
@font-face {
    font-family: MyAndroid;
    src: url('/fonts/android.ttf');
}

/* --- Form Placeholders --- */
.form-control::-webkit-input-placeholder,
.form-control::-moz-placeholder,
.form-control:-ms-input-placeholder {
    color: #9c9c9c;
}

/* --- Select2 Customizations --- */
.select2-selection__rendered {
    line-height: 40px !important;
    text-align: left;
}

.select2-selection,
.select2-selection__arrow {
    height: 40px !important;
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: var(--primary-color);
    color: var(--white);
}

.select2-search__field:focus {
    outline: none !important;
}

.document-search-container .select2-selection__rendered {
    line-height: 48px !important;
    text-align: left;
    font-size: 20px;
}

.document-search-container .select2-selection,
.document-search-container .select2-selection__arrow {
    height: 48px !important;
}

.document-search-container .select2-selection__placeholder {
    color: #444 !important;
}

/* --- Navigation --- */
.navbar-brand {
    padding: 0.25rem 0 0;
}

#sectionNavbar ul {
    width: 100%;
    font-size: 0.8rem;
    justify-content: space-evenly;
    display: flex;
    margin: auto;
}

#sectionNavbar .active,
#navbarNav .active {
    font-weight: bold;
}

#navbarNav {
    border-top: 1px solid #f0ecec;
    margin-top: 10px;
}

.dot:before {
    content: "\21E8";
    margin-right: 2px;
    color: #343a40;
}

/* --- Table Utilities --- */
.table td.fit,
.table th.fit {
    white-space: nowrap;
    width: 1%;
}

/* --- Button & Link States --- */
a.disabled {
    pointer-events: none;
}

.tooltip {
    pointer-events: none;
}

.dropdown-item.disabled,
.dropdown-item:disabled {
    color: #6c757d78;
    background-color: transparent;
}

.btn-default {
    background: var(--white);
    color: var(--black) !important;
    border: 1px solid var(--border-color) !important;
}

/* --- Form Validation --- */
.has-error input:not([type="radio"]):not([type="checkbox"]),
.has-error textarea {
    box-shadow: 0 0 5px red;
}

.has-warning {
    box-shadow: 0 0 5px orange;
    border-color: transparent;
}

/* --- Utility Classes --- */
.bold {
    font-weight: bold;
}

.card {
    border-radius: 8px;
}

/* --- Fancybox --- */
.fancybox-container {
    z-index: 100000000;
}

.fancybox-caption__body {
    padding-bottom: 2%;
}

/* --- Typography Scale --- */
h1, .h1 {
    font-size: 2rem;
}

h2, .h2 {
    font-size: 1.75rem;
}

h3, .h3 {
    font-size: 1.5rem;
}

h4, .h4 {
    font-size: 1.25rem;
}

h5, .h5,
h6, .h6 {
    font-size: 1rem;
}

/* --- Responsive Design Consolidation --- */
@media (min-width: 1024px) {

    .dataTables_filter {
        display: inline-block;
        float: right;
    }

    .dataTables_length {
        display: inline-block;
        float: left;
    }

    .navbar .divider-vertical {
        height: 40px;
        margin: 0 10px;
    }

    .site-content p,
    .site-content li,
    .site-content div {
        text-align: justify;
    }

    blockquote {
        display: block;
        margin: 1em 40px;
    }
}

@media (min-width: 768px) {
    .visible-xs {
        display: none !important;
    }

    .showcase .showcase-text {
        padding: 7rem;
    }

    .tooltip-inner {
        max-width: 300px;
    }
}

@media (max-width: 768px) {
    .footer-bullet {
        display: none;
    }

    .footer-link {
        display: block !important;
        margin-right: 0.5rem !important;
    }

    .fancybox-caption__body {
        text-align: left;
    }

    .site-content img {
        float: none !important;
        display: block;
        margin: 0 auto;
    }

    .text-small-left {
        text-align: left !important;
    }

    #sectionNavbar ul li {
        width: 100% !important;
    }

    .dot {
        display: none;
    }

    .dot:before {
        content: "" !important;
    }

    .mobile-content {
        display: none;
    }

    .mobile-navbar-brand {
        display: inline-block;
        padding: 0.3125rem 1rem 0.3125rem 0;
        font-size: 1.25rem;
        width: 65%;
        line-height: inherit;
        color: rgba(0, 0, 0, .9);
    }

    .g-recaptcha {
        transform: scale(0.8);
        transform-origin: 0 0;
    }
}

#alert-widget {
    margin: 0;
    padding: 0;
    border: 0;
    overflow: hidden;
    position: fixed;
    z-index: 16000002;
    width: 360px;
    height: 63px;
    right: 10px;
    bottom: 0;
    text-align: center;
    border-radius: 6px 6px 0 0;
    cursor: default;
}

/* --- Form Input Styles --- */
.text-input-error {
    color: red;
}

.transform-lowercase {
    text-transform: lowercase;
}

::-webkit-input-placeholder,
:-moz-placeholder,
::-moz-placeholder,
:-ms-input-placeholder,
::placeholder {
    text-transform: none;
}

input[data-datepicker] {
    background-color: var(--white) !important;
    cursor: pointer;
}

/* --- Overlay --- */
#overlay {
    position: fixed;
    display: none;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 2;
    cursor: pointer;
}

/* --- Alert Styles --- */
.site-content .alert-danger {
    color: var(--black);
    background-color: #e96e7a30;
    border-color: #f5c6cb;
}

.alert-secondary {
    color: #383d41;
    background-color: #e2e3e563;
    border-color: #d6d8db;
}

/* --- Button Sizes --- */
.btn-group-xs > .btn,
.btn-xs {
    padding: 0.25rem 0.4rem;
    font-size: 0.7rem;
    line-height: 0.5;
    border-radius: 0.2rem;
}

/* --- Table Specific --- */
#document-drafts-table .dropdown,
#documents-table .dropdown {
    min-width: 120px;
}

#documents-table th,
#document-drafts-table th {
    font-weight: 600;
}

/* --- Search & Document Components --- */
.full-width {
    width: 100%;
}

.custom-select {
    border-radius: 0.25rem 0 0 0.25rem;
}

.custom-select:focus {
    outline: none;
    box-shadow: none;
    border-color: var(--border-color);
}

#search-document-placeholder {
    padding-left: 15px;
}

#search-document {
    width: 100% !important;
}

#search-document-container .select2-selection__arrow {
    display: none;
}

#search-document-container .select2,
#search-document-container .select2-selection--single {
    width: 100% !important;
    height: 38px !important;
}

#search-document-container .select2-selection__rendered {
    line-height: 2.3 !important;
}

#search-document-container .select2-selection__placeholder {
    display: block;
    line-height: 2.3;
}

#select2-search-document-container {
    padding-left: 15px;
}

/* --- Toggle Buttons --- */
#wizard-toggle {
    border-radius: 10px 0 0 10px;
}

#editor-toggle {
    border-radius: 0 10px 10px 0;
}

#wizard-toggle:focus,
#editor-toggle:focus {
    outline: none !important;
    border-color: var(--border-color);
    box-shadow: none;
}

/* --- Content Effects --- */
.fading-content {
    position: relative;
    max-height: 100vh;
    overflow: hidden;
}

.fadeout {
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 300px;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 1) 100%);
}

/* --- User Selection Controls --- */
.disable-select,
.prevent-select {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -ms-user-drag: none;
    user-drag: none;
}

#document-preview {
    pointer-events: none;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

/* --- DataTable Container --- */
.datatable-container {
    min-height: 200px;
    height: 55vh;
    padding: 5px;
}

.datatable-container::-webkit-scrollbar {
    width: 0.5em;
}

.datatable-container::-webkit-scrollbar-track {
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
}

.datatable-container::-webkit-scrollbar-thumb {
    background-color: darkgrey;
    outline: 1px solid slategrey;
}

/* --- Navigation Enhancements --- */
.nav-link {
    padding: 0.5rem 1rem;
}

.section-navbar-sticky {
    position: sticky;
    top: 40px;
    background-color: inherit;
    z-index: 2;
}

/* --- Badges & Status --- */
.badge-orange {
    color: var(--white);
    background-color: orange;
}

.dirty-row {
    background: #ffeded;
}

.article-post-title {
    color: #aaa;
    font-size: 0.8rem;
}

/* --- Footnotes --- */
ol.footnotes {
    margin-left: 0;
    padding-left: 15px;
    list-style-type: decimal;
}

ol.footnotes li a {
    text-decoration: none;
    font-size: smaller;
    color: #666;
}

/* --- Payment Integration --- */
.StripeElement {
    border: 1px solid var(--border-color);
    padding: 0.475rem 0.75rem;
    border-radius: 0.25rem;
    background: var(--white);
}

/* --- Third-party Integrations --- */
.grecaptcha-badge {
    visibility: hidden !important;
}

/* --- Additional Responsive Rules --- */
@media (min-width: 768px) {
    .navbar-expand-md .navbar-nav .nav-link {
        padding: 0.5rem 1rem;
    }

    .navbar-nav .dropdown:hover > .dropdown-menu {
        display: block;
        z-index: 1050;
    }

    .navbar-nav .dropdown-menu {
        margin-top: 0;
    }
}

@media (max-width: 1024px) {
    .fading-content {
        max-height: 50vh;
    }

    #navbar-dropdown-menu {
        background-color: #343a40;
        color: var(--white);
        border: none;
    }

    #navbar-dropdown-menu .dropdown-item {
        color: rgba(255, 255, 255, 0.5);
    }

    #navbar-dropdown-menu .dropdown-item:active {
        color: var(--white) !important;
        background-color: rgba(255, 255, 255, 0.15) !important;
    }

    #navbar-dropdown-menu .dropdown-item:focus,
    #navbar-dropdown-menu .dropdown-item:hover {
        background-color: rgba(255, 255, 255, 0.15);
    }
}

@media (max-width: 992px) {
    #user-menu {
        border: 0;
        background: #343a40;
    }

    #user-menu .dropdown-item {
        color: rgba(255, 255, 255, 0.5);
    }

    #user-menu .dropdown-item:hover {
        color: var(--white);
        background-color: rgba(255, 255, 255, 0.15);
    }
}

@media (max-width: 480px) {
    .documentDropdown,
    .documentDraftDropdown {
        margin-top: -20px;
    }
}

<div class="form-check">
    <label class="form-check-label">
        {{ html()->radio($name, null, $value)->class('form-check-input')->attributes($attributes)->disabled(isset($attributes['disabled']) && $attributes['disabled'])->checked(!empty($attributes['checked']) || ($model->get($name) !== null && $model->get($name) == $value)) }} {!! $label !!}
        @if($image_tooltip)
            <a class="fancybox" data-caption="{{ $tooltip }}" href="{{$image_tooltip}}"><i class="fa fa-question-circle"></i></a>
        @elseif($tooltip)
            <span data-toggle="tooltip"
                  data-original-title="{{ $tooltip }}">
                 <i class="fa fa-info-circle" style="padding-left: {{ $tooltip_padding }}"></i>
            </span>
        @endif
    </label>
</div>


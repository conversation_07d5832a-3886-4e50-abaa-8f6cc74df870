@if(!empty($label))
    <label for="{{ $name }}">
        @if($image_tooltip)
            @if(str_word_count($label) > 1)
                {!! substr($label, 0, -strlen(strrchr($label,' '))) !!}

                <span style="white-space: nowrap">
                   {!! strrchr($label,' ') !!}
                    @if($image_tooltip_explanation)
                        <span data-toggle="tooltip"
                              data-original-title="{{ $image_tooltip_explanation }}">
                        <i class="fa fa-info-circle" style={{"padding-left: $tooltip_padding" }}></i>
                        </span>
                    @endif
                    <a class="fancybox" data-caption="{{ $tooltip }}" href="{{$image_tooltip}}"><i class="fa fa-question-circle"></i></a>
                </span>
            @else
                {!! $label !!}
                @if($image_tooltip_explanation)
                    <span data-toggle="tooltip"
                          data-original-title="{{ $image_tooltip_explanation }}">
                        <i class="fa fa-info-circle" style={{"padding-left: $tooltip_padding" }}></i>
                        </span>
                @endif
                <a class="fancybox" data-caption="{{ $tooltip }}" href="{{$image_tooltip}}"><i class="fa fa-question-circle"></i></a>
            @endif
        @elseif($tooltip)
            @if(str_word_count($label) > 1)
                {!! substr($label, 0, -strlen(strrchr($label,' '))) !!}

                <span style="white-space: nowrap">
                   {!! strrchr($label,' ') !!}
                     <span data-toggle="tooltip"
                           data-original-title="{{ $tooltip }}">
                        <i class="fa fa-info-circle" style={{"padding-left: $tooltip_padding" }}></i>
                    </span>
                </span>
            @else
                {!! $label !!}
                <span data-toggle="tooltip"
                      data-original-title="{{ $tooltip }}">
                        <i class="fa fa-info-circle" style={{"padding-left: $tooltip_padding" }}></i>
                </span>
            @endif
        @else
            {!! $label !!}
        @endif
    </label>
@endif
<div class="text-input">
    {{ html()->textarea($name, $value)->class('form-control')->attributes($attributes)->rows($rows) }}
    <span class="text-input-error"></span>
</div>


<div class="form-check">
    <label class="form-check-label w-100">
        {{ html()->checkbox($name, null, $value)->class('form-check-input')->attributes($attributes)->checked($model->isChecked($name, $value, $attributes)) }}
    @if(!empty($label))
            @if($image_tooltip)
                @if(str_word_count($label) > 1)
                    {!! substr($label, 0, -strlen(strrchr($label,' '))) !!}

                    <span style="white-space: nowrap">
                   {!! strrchr($label,' ') !!}
                    <a class="fancybox" data-caption="{{ $tooltip }}" href="{{$image_tooltip}}"><i class="fa fa-question-circle"></i></a>
                </span>
                @else
                    {!! $label !!}
                    <a class="fancybox" data-caption="{{ $tooltip }}" href="{{$image_tooltip}}"><i class="fa fa-question-circle"></i></a>
                @endif
            @elseif($tooltip)
                @if(str_word_count($label) > 1)
                    {!! substr($label, 0, -strlen(strrchr($label,' '))) !!}

                    <span style="white-space: nowrap">
                   {!! strrchr($label,' ') !!}
                     <span data-toggle="tooltip"
                           data-original-title="{{ $tooltip }}">
                        <i class="fa fa-info-circle" style={{"padding-left: $tooltip_padding" }}></i>
                    </span>
                </span>
                @else
                    {!! $label !!}
                    <span data-toggle="tooltip"
                          data-original-title="{{ $tooltip }}">
                        <i class="fa fa-info-circle" style={{"padding-left: $tooltip_padding" }}></i>
                </span>
                @endif
            @else
                {!! $label !!}
            @endif
        @endif
    </label>
</div>

@if(!empty($label))
    <label @if(isset($attributes['zero-margin-label'])) style="margin-bottom: 0;" @endif for="{{ $name }}">
        @if($image_tooltip)
            @if(str_word_count($label) > 1)
                {!! substr($label, 0, -strlen(strrchr($label,' '))) !!}

                <span style="white-space: nowrap">
                   {!! strrchr($label,' ') !!}
                    @if($image_tooltip_explanation)
                        <span data-toggle="tooltip"
                              data-original-title="{{ $image_tooltip_explanation }}">
                        <i class="fa fa-info-circle" style={{"padding-left: $tooltip_padding" }}></i>
                        </span>
                    @endif
                    <a class="fancybox" data-caption="{{ $tooltip }}" href="{{$image_tooltip}}"><i class="fa fa-question-circle"></i></a>

                </span>
            @else
                {!! $label !!}
                @if($image_tooltip_explanation)
                    <span data-toggle="tooltip"
                          data-original-title="{{ $image_tooltip_explanation }}">
                        <i class="fa fa-info-circle" style={{"padding-left: $tooltip_padding" }}></i>
                        </span>
                @endif
                <a class="fancybox" data-caption="{{ $tooltip }}" href="{{$image_tooltip}}"><i class="fa fa-question-circle"></i></a>

            @endif
        @elseif($tooltip)
            @if(str_word_count($label) > 1)
                {!! substr($label, 0, -strlen(strrchr($label,' '))) !!}

                <span style="white-space: nowrap">
                   {!! strrchr($label,' ') !!}
                     <span data-toggle="tooltip"
                           data-original-title="{{ $tooltip }}">
                        <i class="fa fa-info-circle" style={{"padding-left: $tooltip_padding" }}></i>
                    </span>
                </span>
            @else
                {!! $label !!}
                <span data-toggle="tooltip"
                      data-original-title="{{ $tooltip }}">
                        <i class="fa fa-info-circle" style={{"padding-left: $tooltip_padding" }}></i>
                </span>
            @endif
        @else
            {!! $label !!}
        @endif
    </label>
@endif

@php if(isset($attributes['data-datepicker'])) $attributes['readonly'] = true; @endphp

<div class="text-input">
    @if($addon)
        @if(!is_array($addon))
            <div class="input-group">
                <div class="input-group-prepend">
                    <span class="input-group-text">{!! $addon !!}</span>
                </div>
                {{ html()->input('text', $name, $value)->class('form-control')->attributes($attributes)  }}

            </div>
        @else
            <div class="input-group">
                @if(!empty($addon['prepend']))
                    <div class="input-group-prepend">
                        <span class="input-group-text">{!! $addon['prepend'] !!}</span>
                    </div>
                @endif

                {{ html()->input('text', $name, $value)->class('form-control')->attributes($attributes)  }}


                @if(!empty($addon['append']))
                    <div class="input-group-append">
                        <span class="input-group-text">{!! $addon['append'] !!}</span>
                    </div>
                @endif
            </div>
        @endif

    @else
        @php // we set id method explicitly because otherwise it might accidentally generate a duplicate id (automatically from name) and that causes some frontend libraries (datepicker) to not work. @endphp
        {{ html()->input('text', $name, $value)->class('form-control')->attributes($attributes)->id($attributes['id'] ?? null)  }}

    @endif
        <span class="text-input-error"></span>
</div>


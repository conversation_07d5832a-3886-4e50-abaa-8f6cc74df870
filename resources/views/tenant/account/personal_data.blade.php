@extends('tenant.layouts.common.master')
@section('title', 'Uredi podatke')

@section('content')
    <h1 class="pb-4">Uredi podatke</h1>
    <div class="row">
        <div class="col-lg-6">
            <div class="alert alert-warning">
                <PERSON><PERSON>elite promijeniti adresu e-pošte, potrebno je kliknuti na poveznicu koju ćemo Vam poslati na novu adresu.
            </div>
            <form method="post" action="{{ route('tenant.account.data.update') }}">
                @csrf
                <div class="form-group">
                    {{ html()->label('Adresa e-pošte', 'email') }}
                    {{
                        html()->email('email')
                        ->value(Auth::user()->email)
                        ->placeholder('Upiši adresu e-pošte...')
                        ->class('form-control')
                        ->required()
                    }}
                </div>
                <div class="form-group">
                    {{ html()->label('Ime i prezime', 'name') }}
                    {{
                        html()->text('name')
                        ->value(Auth::user()->name)
                        ->placeholder('Upiši ime i prezime...')
                        ->class('form-control')
                        ->required()
                    }}</div>

                <div class="form-group">
                    <button type="submit" class="btn btn-primary">Spremi promjene</button>
                </div>
            </form>
        </div>
    </div>

@endsection

@push('scripts')
    <script type="text/javascript">
        $(function() {


        });
    </script>
@endpush

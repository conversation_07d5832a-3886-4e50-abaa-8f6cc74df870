@extends('tenant.layouts.common.master')
@section('title', 'Postavke obavijesti')

@section('content')
    <h1 class="pb-4">Postavke obavijesti</h1>

    <div class="pb-4">
        Adresa e-pošte: <i> {{ $preferences->email }} </i> @auth @if(auth()->user()->email == $preferences->email ) <a href="{{ route('tenant.account.data') }}"><small>(uredi)</small></a> @endif @endauth
    </div>

    <form method="post" action="{{ URL::signedRoute('tenant.email.preferences.store', ['email' => $preferences->email]) }}">
        @csrf


        <div class="row">
            <div class="col-lg-12">
                <div class="form-check">
                    <input @if($preferences->general) checked @endif name="general" class="form-check-input" type="checkbox" value="1" id="general" @if(!$guest) disabled @endif>
                    <label class="form-check-label" for="general">
                        <PERSON><PERSON><PERSON> od Legaldeska primati osnovne obavijesti @if($guest) <strong> - isključivanjem ove opcije nećete moći primati osnovnu e-poštu (dokumente, zahtjeve za e-potpisima i druge obavijesti) od Legaldeska dok je ponovo ne uključite</strong> @endif
                    </label>
                </div>
            </div>
        </div>


        <div class="row mt-3">
            <div class="col-lg-12">
                <div class="form-check">
                    <input @if($preferences->newsletter) checked @endif name="newsletter" class="form-check-input" type="checkbox" value="1" id="newsletter">
                    <label class="form-check-label" for="newsletter">
                        Želim od Legaldeska povremeno primati poruke s novostima
                    </label>
                </div>
            </div>
        </div>

        @if($guest)
            <div class="row mt-3">
                <div class="col-lg-12">
                    <div class="form-check">
                        <input @if(!$preferences->general && !$preferences->newsletter) checked @endif class="form-check-input" type="checkbox" value="1" id="none">
                        <label class="form-check-label" for="none">
                            Ne želim od Legaldeska primati nikakve obavijesti
                        </label>
                    </div>
                </div>
            </div>
        @endif

        <div class="row mt-3">
            <div class="col-lg-12">
                <input type="submit" value="Spremi postavke" class="btn btn-primary">
            </div>
        </div>

    </form>

@endsection

@push('scripts')
    <script type="text/javascript">
        $(function() {

            $('#general').on('click', function(e){
                $('#none').prop('checked', false);
            })

            $('#newsletter').on('click', function(e){
                $('#none').prop('checked', false);
            })

            $('#none').on('click', function(e){
                if(e.target.checked) {
                    $('#general').prop('checked', false);
                    $('#newsletter').prop('checked', false);
                }
            })
        });
    </script>
@endpush

@extends('tenant.layouts.common.master')
@section('title', '<PERSON><PERSON> račun')

@section('content')
    <h1 class="pb-4"><PERSON><PERSON> ra<PERSON></h1>

    <div class="row">
        <div class="col-lg-12">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                    <tr>
                        <td>Ime</td>
                        <td>E-po<PERSON>ta</td>
                        <td>Registriran</td>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td>{{ Auth::user()->name }}</td>
                        <td>{{ Auth::user()->email }}</td>
                        <td>{{ date('d.m.Y.', strtotime(Auth::user()->created_at)) }}</td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-12">
            <div class="dropdown">

                <button class='btn btn-primary dropdown-toggle' type='button' id='dropdownMenuButton' data-toggle='dropdown' aria-haspopup='true' aria-expanded='false'>
                     Odaberi radnju
                </button>

                <div class="dropdown-menu" aria-labelledby="dropdownMenuLink">
                    <a class="dropdown-item" href="{{ route('tenant.account.data') }}">Uredi podatke</a>
                    <a class="dropdown-item" href="{{ URL::signedRoute('tenant.email.preferences.show', ['email' => Auth::user()->email]) }}">Postavke obavijesti</a>
                    <form id="delete-account-form" method="post" action="{{ route('tenant.account.delete') }}">
                        @csrf
                        <a style="cursor: pointer" id="delete-account" class="dropdown-item">Izbriši račun</a>
                    </form>
                </div>
            </div>
        </div>
    </div>



@endsection

@push('scripts')
    <script type="text/javascript">
        $(function() {
            $('#delete-account').on('click', function(e){

                e.preventDefault();

                window.pleaseConfirm('Jeste li sigurni da želite trajno izbrisati Vaš račun na Legaldesku? Brisanjem računa bit će izbrisani svi Vaši osobni podaci i izrađeni dokumenti te im više nećete moći pristupiti.').then(result => {
                    if(result){
                        $('#delete-account-form').submit();
                    }
                });
            })
        });
    </script>
@endpush

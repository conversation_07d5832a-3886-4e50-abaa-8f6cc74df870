<!DOCTYPE html>

<html lang="en">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>

    @php
        $tenant = tenant();

        /* ---------- HEADER DATA ---------- */
        $headerLogo      = $tenant->headerLogoPath;
        $headerLogoPos   = $tenant->headerLogoPosition ?? ($headerLogo ? 'left' : null);
        $headerText      = $tenant->headerText;
        $headerTextPos   = $tenant->headerTextPosition;

        /* ---------- FOOTER DATA ---------- */
        $footerLogo      = $tenant->footerLogoPath;
        $footerLogoPos   = $tenant->footerLogoPosition ?? ($footerLogo ? 'left' : null);
        $footerText      = $tenant->footerText;
        $footerTextPos   = $tenant->footerTextPosition;

        /* ---------- FLAGS ---------- */
        $hasHeaderContent = $headerLogo || $headerText;
        $hasFooterContent = $footerLogo || $footerText;

    @endphp

    <style type="text/css" media="all">

        @page {
            @if(!$hasHeaderContent)
                margin-top: 2cm;
            @endif
            margin-left: 2cm;
            margin-right: 2cm;
            margin-bottom: 2cm;
            header: page-header;
            footer: page-footer;
        }

        .text-center {
            text-align: center;
        }

        .font-weight-bold {
            font-weight: bold;
        }

        h2 {
            padding-top: 50px;
            padding-bottom: 50px;
        }

        .article-header{
            page-break-after: avoid;
            page-break-inside: avoid;
            font-weight: bold;
            margin-bottom: 0!important;
            margin-top: 10px!important;
        }

        .mt-0 {
            margin-top:0!important;
        }

        .avoid-page-break{
            page-break-inside: avoid;
        }

        body{
            text-align: justify;
            text-justify: inter-ideograph;
            font-family: Arial, serif;
        }

        .signatures_table{
            width:100%;
            border-collapse:separate;
            border-spacing:3em;
        }

        .signatures_table td{
            vertical-align:top;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table, .table th, .table td {
            border: 1px solid #bfbfbf;
        }

        .table th, .table td{
            padding: 10px;
        }

        .strong{
            font-weight: bold;
        }

        .page-break {
            page-break-after: always;
        }

        h2.editable-segment{
            margin-top:0;
        }

        .underline{
            text-decoration: underline;
        }

        .pb-0{
            padding-bottom: 0;
        }

        .mb-0{
            margin-bottom: 0;
        }

        a {
            color: black;
            text-decoration: none;
        }

        .mt-2 {
            margin-top: 0.5rem;
        }

        .mt-5 {
            margin-top: 4rem;
        }

        .mb-5 {
            margin-bottom: 4rem;
        }


    </style>

    @if(Auth::user() && Auth::user()->isAdmin())
        <style>
            .segment-dirty .article-header {
                color: #FF0000FF!important;
            }
        </style>
    @endif
</head>

{{-- ---------- PAGE HEADER ---------- --}}
@if($hasHeaderContent)
    <htmlpageheader name="page-header">
        <table width="100%">
            <tr>
                {{-- LEFT --}}
                <td width="33%" align="left" valign="top">
                    @if($headerLogo && $headerLogoPos === 'left')
                        <img src="{{ tenant_asset($headerLogo) }}" style="max-width:250px;" alt="Logo">
                    @elseif($headerText && $headerTextPos === 'left')
                        <small>{!! nl2br(e($headerText)) !!}</small>
                    @endif
                </td>

                {{-- CENTER --}}
                <td width="34%" align="center" valign="top">
                    @if($headerLogo && $headerLogoPos === 'center')
                        <img src="{{ tenant_asset($headerLogo) }}" style="max-width:250px;" alt="Logo">
                    @elseif($headerText && $headerTextPos === 'center')
                        <small>{!! nl2br(e($headerText)) !!}</small>
                    @endif
                </td>

                {{-- RIGHT --}}
                <td width="33%" align="right" valign="top">
                    @if($headerLogo && $headerLogoPos === 'right')
                        <img src="{{ tenant_asset($headerLogo) }}" style="max-width:250px;" alt="Logo">
                    @elseif($headerText && $headerTextPos === 'right')
                        <small>{!! nl2br(e($headerText)) !!}</small>
                    @endif
                </td>
            </tr>
        </table>
        <hr/>
    </htmlpageheader>
@else
    <htmlpageheader name="page-header"></htmlpageheader>
@endif

<body>
{!! $content !!}
</body>

{{-- ---------- PAGE FOOTER ---------- --}}
<htmlpagefooter name="page-footer">
    <hr/>
    <table width="100%" style="color:#373737;">
        <tr>
            {{-- LEFT --}}
            <td width="33%" align="left" valign="top">
                @if($footerLogo && $footerLogoPos === 'left')
                    <img src="{{ tenant_asset($footerLogo) }}" style="max-height:40px;" alt="Logo">
                @elseif($footerText && $footerTextPos === 'left')
                    <small>{!! nl2br(e($footerText)) !!}</small>
                @endif
            </td>

            {{-- CENTER --}}
            <td width="34%" align="center" valign="top">
                @if($footerLogo && $footerLogoPos === 'center')
                    <img src="{{ tenant_asset($footerLogo) }}" style="max-height:40px;" alt="Logo">
                @elseif($footerText && $footerTextPos === 'center')
                    <small>{!! nl2br(e($footerText)) !!}</small>
                @endif
            </td>

            {{-- RIGHT – uvijek broj stranice --}}
            <td width="33%" align="right" valign="top">
                <small>Stranica {PAGENO} od {nb}</small>
            </td>
        </tr>
    </table>
</htmlpagefooter>

</html>

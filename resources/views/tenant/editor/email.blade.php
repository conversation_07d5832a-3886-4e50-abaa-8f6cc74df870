@extends('tenant.layouts.common.master')
@section('title', 'Pošalji uređeni dokument')

@section('content')

    <div class="row">
        <div class="col-lg-6 offset-lg-3 text-center">

            <h3>
                Pošalji uređeni dokument: {{ $draft->title }}
            </h3>

            <hr/>

            <button id="show_preview" class="btn btn-default btn-block ">Pregledaj uređeni dokument</button>

            <form method="post" action="{{ route('tenant.editor.draft.send', $draft) }}">
                @csrf
                <div id="email_container">

                    @if(!empty(old('email')))
                        @foreach(old('email') as $_old_email_i => $_old_email)
                            <div class="email_content">
                                <div class="input-group mt-2 mb-2 @if($errors->has("email.$_old_email_i")) has-error @endif">
                                    <input type="email" placeholder="Unesi adresu e-pošte..."
                                           class="email form-control"
                                           name="email[]" value="{{ $_old_email }}">
                                    <div class="input-group-append remove_email"
                                         style="cursor: pointer;">
                                            <span class="input-group-text">
                                                <i class="fa fa-trash"></i>
                                            </span>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    @else
                        <div class="email_content">
                            <div class="input-group mt-2 mb-2">
                                <input type="email" placeholder="Unesi adresu e-pošte..."
                                       class="email form-control"
                                       name="email[]">
                                <div class="input-group-append remove_email"
                                     style="cursor: pointer;">
                                                <span class="input-group-text">
                                                    <i class="fa fa-trash"></i>
                                                </span>
                                </div>
                            </div>
                        </div>
                    @endif


                </div>

                <div class="row mt-3">
                    <div class="col-lg-6 mb-1">
                        <input type="button" value="Dodaj adresu e-pošte" class="btn btn-secondary btn-block"
                               id="add_email">
                    </div>
                    <div class="col-lg-6">
                        <input type="submit" value="Pošalji" class="btn btn-primary please-confirm btn-block"
                               data-message="Jeste li sigurni da želite poslati uređeni dokument na odabrane adrese e-pošte?">
                    </div>
                </div>
            </form>

            <div id="email_template" class="d-none">
                <div class="email_content">
                    <div class="input-group mt-2 mb-2">
                        <input type="email" placeholder="Unesi adresu e-pošte..."
                               class="email form-control"
                               name="email[]">
                        <div class="input-group-append remove_email"
                             style="cursor: pointer;">
                                            <span class="input-group-text">
                                                <i class="fa fa-trash"></i>
                                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="preview" class="d-none">
        <hr/>
        <div class="row">
            <div class="col-lg-10 offset-lg-1">
                @include('tenant.common/partials/preview', ['url' => \Illuminate\Support\Facades\URL::signedRoute('tenant.editor.draft.stream', [$draft])])
            </div>
        </div>
    </div>

@endsection

@push('scripts')
    <script type="text/javascript">
        $(function () {
            $('#add_email').on('click', function (e) {
                $('#email_container').append($('#email_template').html());
                bindRemoveEmail();
            });

            bindRemoveEmail();

            function bindRemoveEmail() {
                $('.remove_email').unbind('click').click(function () {

                    if ($('.email_content:visible').length > 1) {
                        $(this).closest('.email_content').remove();
                    }


                });
            }

            $('#show_preview').on('click', function (e) {
                $('#preview').removeClass('d-none');
                $('html, body').animate({
                    scrollTop: $("#preview").offset().top - 50
                }, 500);
            })
        });
    </script>
@endpush

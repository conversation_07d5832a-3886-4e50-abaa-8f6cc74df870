<!DOCTYPE html>

<html lang="hr">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <style media="all">

        /* Allow colors to be printed in the PDF */
        html {
            -webkit-print-color-adjust: exact;
        }

        /* prevent blank last page in the PDF */
        html, body { height: 100% }
        body, body > *:last-child {
            margin: 0;
        }

        body{
            margin-left: 2cm;
            margin-right: 2cm;
            text-align: justify;
            text-justify: inter-ideograph;
            font-family:  monospace;
            font-size: 1.1em;
        }

        .dash{
            font-size: .6em;
            font-weight: 600;
        }

        .article-header{
            page-break-after: avoid;
            page-break-inside: avoid;
            font-weight: bold;

            margin-top: 0;
            margin-bottom: 0;

            padding-top: 8px;

            padding-bottom: 6px;
            border-bottom: 1px dashed;
        }

        .editable-segment, .article-body p{
            margin: 0;
            padding: 0;
        }

        .editable-segment[data-type="title"]{
            font-size: 1.7em;
            padding-top: 8px;
            padding-bottom: 3px;
        }

        .article-body p{
            padding-bottom: 9px;
            padding-top: 6px;
            border-bottom: 1px dashed;
        }

        .editable-segment[data-type="body"] p {
            padding-bottom: 9px;
            padding-top: 6px;
            border-bottom: 1px dashed;
            border-top: 1px dashed;
        }

        .article-body ul,
        .article-body ol,
        .editable-segment[data-type="body"] ul,
        .editable-segment[data-type="body"] ol
        {
            border-top: 1px dashed;
            border-bottom: 1px dashed;
            margin-top: 0;
            margin-bottom: 0;
            padding-top: 6px;
            padding-bottom: 9px;
        }

        /* avoid duplicating borders (bottom+top) */
        .article-body p + ul,
        .article-body p + ol,
        ul + ul,
        ul + ol,
        ol + ul,
        ol + ol
        {
            border-top: none!important;
        }

        ol ul, ul ol, ul ul, ol ol{
            margin-top: 10px!important;
            border-bottom: none!important;
            padding-bottom: 0!important;
        }

        .text-center {
            text-align: center;
        }

        .font-weight-bold {
            font-weight: bold;
        }

        h2 {
            padding-top: 50px;
            padding-bottom: 50px;
        }

        .mt-0 {
            margin-top:0!important;
        }

        .mb-0 {
            margin-bottom:0!important;
        }

        .pb-0 {
            padding-bottom:0!important;
        }

        .avoid-page-break{
            page-break-inside: avoid;
        }

        .document_placeholder{
            background: #f0f6ff;
            font-weight: normal;
            color:#f0f6ff;
            border-bottom: 0.01em solid silver;
        }

        .signatures_table{
            width:100%;
            border-collapse:separate;
            border-spacing:3em;
        }

        .signatures_table td{
            vertical-align:top;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table, .table th, .table td {
            border: 1px solid #bfbfbf;
        }

        .table th, .table td{
            padding: 10px;
        }

        .strong{
            font-weight: bold;
        }

        .page-break {
            page-break-after: always;
        }

        h2.editable-segment{
            border-top: 1px dashed;
            margin-top:0;
            border-bottom: 1px dashed;
        }

        .editable-segment[data-type="parties"] p {
            border-bottom: 1px dashed;
            padding-top: 6px;
            padding-bottom: 9px;
            margin-top: 0;
            margin-bottom: 0;
            
        }

        .editable-segment[data-type="parties"] p:last-child {
            border-bottom: none;
        }

        .signature-line {
            font-size: .85em;
        }

        ul, ol {
            list-style-position: inside;
            padding-left: 0; /* Removes padding from the ul or ol */
        }

        ul > li:before {
            content: "";
            margin-left: -5px;
        }

        ol > li:before {
            content: "";
            margin-left: -2px;
        }

        blockquote {
            margin: 0;
        }

        a {
            color: black;
            text-decoration: none;
        }

        .underline{
            text-decoration: underline;
        }

        p {
            text-align: justify!important;
        }

        .mt-2 {
            margin-top: 0.5rem !important;
        }

        .mt-5 {
            margin-top: 4rem !important;
        }

        .mb-3 {
            margin-bottom: 2rem !important;
        }

        .mb-5 {
            margin-bottom: 4rem !important;
        }

    </style>

    @if(Auth::user() && Auth::user()->isAdmin())
        <style>
            .segment-dirty .article-header {
                color: #FF0000FF!important;
            }
        </style>
    @endif
</head>

<body>
    {!! $content !!}
</body>

</html>

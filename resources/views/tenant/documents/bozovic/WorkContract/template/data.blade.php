@extends($builder->getLayout())
@section('content')
    @php

    // define variables for rendering view
    $data['employer_name'] = $builder->get('employer_name');
    $data['employer_address'] = $builder->get('employer_address');
    $data['employer_city'] = $builder->get('employer_city');
    $data['employer_postal_code'] = $builder->get('employer_postal_code');
    $data['employer_country'] = $builder->get('employer_country');
    $data['employer_oib'] = $builder->get('employer_oib');
    $data['authorized_employer_persons'] = $builder->get('authorized_employer_persons', false);

    // standardize array keys
    if(!empty($data['authorized_employer_persons'])){
        $data['authorized_employer_persons'] = array_values($data['authorized_employer_persons']);

        // default case
        foreach($data['authorized_employer_persons'] as &$_authorized_employer_person){
            if(empty($_authorized_employer_person['name'])) $_authorized_employer_person['name'] = $builder->get('placeholder');
            if(empty($_authorized_employer_person['role'])) $_authorized_employer_person['role'] = $builder->get('placeholder');

			$_authorized_employer_person['has_entered_address'] = !empty($_authorized_employer_person['address'])
			|| !empty($_authorized_employer_person['city'])
			|| !empty($_authorized_employer_person['postal_code']);

        }

		// always unset when looping by reference!
		unset($_authorized_employer_person);
    }

    $data['employee_name'] = $builder->get('employee_name');
    $data['employee_address'] = $builder->get('employee_address');
    $data['employee_city'] = $builder->get('employee_city');
    $data['employee_postal_code'] = $builder->get('employee_postal_code');
    $data['employee_country'] = $builder->get('employee_country');
    $data['employee_oib'] = $builder->get('employee_oib');

    $data['contract_type'] = $builder->get('contract_type', false); // definite, indefinite
    $data['work_start_date'] = $builder->get('work_start_date', false);
    $data['work_start_date_custom'] = $builder->get('work_start_date_custom');
    $data['expected_duration'] = $builder->get('expected_duration', false);
    $data['expected_duration_until_date'] = $builder->get('expected_duration_until_date');
    $data['expected_duration_custom'] = $builder->get('expected_duration_custom');
    $data['definite_contract_reason'] = $builder->get('definite_contract_reason');
    $data['probation_period'] = $builder->get('probation_period', false);
    $data['probation_period_type'] = $builder->get('probation_period_type', false);
    $data['probation_period_custom'] = $builder->get('probation_period_custom', false);
    $data['has_work_regulations'] = $builder->get('has_work_regulations', false);
    $data['has_collective_agreement'] = $builder->get('has_collective_agreement', false);
    $data['collective_agreement_name'] = $builder->get('collective_agreement_name');

    $data['work_location_type'] = $builder->get('work_location_type', false);
    $data['work_location'] = $builder->get('work_location');
    $data['work_locations'] = $builder->get('work_locations');
    $data['work_title'] = $builder->get('work_title');
    $data['work_responsibilities'] = array_values(array_filter($builder->get('work_responsibilities', false) ?: []));
    $data['is_full_time'] = $builder->get('is_full_time', false);
    $data['week_hours'] = $builder->get('week_hours', false);
    $data['week_hours_custom'] = $builder->get('week_hours_custom');
    $data['working_hours'] = $builder->get('working_hours', false) ? $builder->get('working_hours', false)[0] : null;
    $data['working_hours_custom_start_day'] = $builder->get('working_hours_custom_start_day');
    $data['working_hours_custom_start_hour'] = $builder->get('working_hours_custom_start_hour');
    $data['working_hours_custom_end_day'] = $builder->get('working_hours_custom_end_day');
    $data['working_hours_custom_end_hour'] = $builder->get('working_hours_custom_end_hour');
    $data['paid_holidays_duration'] = $builder->get('paid_holidays_duration', false);
    $data['paid_holidays_duration_custom'] = $builder->get('paid_holidays_duration_custom');
    $data['compensation_management_type'] = $builder->get('compensation_management_type', false);
    $data['gross_monthly_salary_custom'] = $builder->get('gross_monthly_salary_custom');
    $data['salary_bonuses'] = $builder->get('salary_bonuses', false);
    $data['custom_salary_bonuses'] = array_values($builder->get('custom_salary_bonuses', false) ?: []);
    $data['salary_perks'] = $builder->get('salary_perks', false);
    $data['salary_perk_car_amount'] = $builder->get('salary_perk_car_amount');
    $data['salary_perk_car_licence_plates'] = $builder->get('salary_perk_car_licence_plates');
    $data['salary_perk_car_model'] = $builder->get('salary_perk_car_model');
    $data['salary_perk_car_identification_number'] = $builder->get('salary_perk_car_identification_number');
	$data['custom_salary_perks'] = array_values($builder->get('custom_salary_perks', false) ?: []);
	$data['material_perks'] = array_values($builder->get('material_perks', false) ?: []);
    $data['material_perk_christmas'] = $builder->get('material_perk_christmas');
    $data['material_perk_easter'] = $builder->get('material_perk_easter');
    $data['material_perk_child_gift'] = $builder->get('material_perk_child_gift');
    $data['material_perk_vacation_subvention'] = $builder->get('material_perk_vacation_subvention');
    $data['material_perk_meal_subvention'] = $builder->get('material_perk_meal_subvention');
    $data['material_perk_transport_subvention'] = $builder->get('material_perk_transport_subvention');
    $data['material_perk_transport_subvention_intercity'] = $builder->get('material_perk_transport_subvention_intercity');
	$data['custom_material_perks'] = array_values($builder->get('custom_material_perks', false) ?: []);
    $data['salary_day'] = $builder->get('salary_day', false);
    $data['salary_day_custom'] = $builder->get('salary_day_custom');
    $data['notice_management_type'] = $builder->get('notice_management_type', false);
    $data['custom_notice_period_1'] = $builder->get('custom_notice_period_1');
    $data['custom_notice_period_2'] = $builder->get('custom_notice_period_2');
    $data['custom_notice_period_3'] = $builder->get('custom_notice_period_3');
    $data['custom_notice_period_4'] = $builder->get('custom_notice_period_4');
    $data['custom_notice_period_5'] = $builder->get('custom_notice_period_5');

    $data['is_confidential'] = $builder->get('is_confidential', false);
    $data['regulate_competition'] = $builder->get('regulate_competition', false);
    $data['regulate_competition_period'] = $builder->get('regulate_competition_period');
    $data['regulate_competition_has_compensation'] = $builder->get('regulate_competition_has_compensation', false);
    $data['regulate_competition_compensation'] = $builder->get('regulate_competition_compensation', false);
    $data['regulate_competition_compensation_custom'] = $builder->get('regulate_competition_compensation_custom');
    $data['regulate_competition_has_penalty'] = $builder->get('regulate_competition_has_penalty', false);
    $data['regulate_competition_penalty'] = $builder->get('regulate_competition_penalty', false);
    $data['regulate_competition_penalty_custom'] = $builder->get('regulate_competition_penalty_custom');
    $data['transfer_intellectual_rights_to_employer'] = $builder->get('transfer_intellectual_rights_to_employer', false);

    $data['contract_copy_count'] = $builder->get('contract_copy_count');
    $data['employer_contract_copy_count'] = $builder->get('employer_contract_copy_count');
    $data['employee_contract_copy_count'] = $builder->get('employee_contract_copy_count');
    $data['contract_date'] = $builder->get('contract_date');
    $data['contract_place'] = $builder->get('contract_place');
    $data['custom_provisions'] = $builder->get('custom_provisions', false);

    @endphp

    @include($builder->render_view, ['builder' => $builder, 'data' => $data])

@endsection

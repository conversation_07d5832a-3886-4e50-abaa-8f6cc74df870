@extends('tenant.layouts.document.master')
@section('content')

{{ Form::model($model, ['url' => $route, 'autocomplete' => 'off' ]) }}
<div class="row">
    <div class="col">

        <div class="card mb-4">
            <div class="card-header">
                <PERSON><PERSON><PERSON><PERSON><PERSON>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="form-group col-lg-6">
                        {{  Form::fNumber($model, 'contract_copy_count', null, 'U kojem broju primjeraka se sklapa ovaj ugovor?', ['placeholder' => 'Npr: 4'])  }}
                    </div>
                    <div class="form-group col-lg-6">
                        {{  Form::fNumber($model, 'employer_contract_copy_count', null, 'Koliko primjeraka ugovora zadržava poslodavac?', ['placeholder' => 'Npr: 2'])  }}
                    </div>
                </div>
                <div class="row">
                    <div class="form-group col-lg-6">
                        {{  Form::fNumber($model, 'employee_contract_copy_count', null, 'Koliko primjeraka ugovora zadržava radnik?', ['placeholder' => 'Npr: 2'])  }}
                    </div>
                    <div class="form-group col-lg-6">
                        {{  Form::fText($model, 'contract_date', null, 'Koji je datum sklapanja ugovora?', ['data-datepicker' => 1, 'autocomplete' => 'off', 'placeholder' => 'Npr: '.date('j. n. Y.')])  }}
                    </div>
                </div>
                <div class="row">
                    <div class="form-group col-lg-6">
                        {{  Form::fText($model, 'contract_place',null,'Koje je mjesto sklapanja ugovora?', ['placeholder' => 'Npr: Zagreb'])  }}
                    </div>
                </div>

                <hr/>

                <div id="custom_provision_template" class="d-none">
                    <div class="custom_provision_content">
                        <div class="card mb-4">
                            <div class="card-header">
                                Dodana odredba
                                <strong><a class="btn btn-danger btn-sm float-right remove_custom_provision"><i
                                                class="fa fa-trash"></i> Ukloni</a></strong>
                            </div>
                            <div class="card-body">
                                {{  Form::fText($model, 'custom_provisions[{INDEX}][title]', null, null, ['placeholder' => 'Upiši naziv odredbe', 'data-force-start-case' => 'upper'])  }}
                                {{  Form::fTextArea($model, 'custom_provisions[{INDEX}][provision]', null, null, ['placeholder' => 'Upiši tekst odredbe', 'class' => 'form-control mb-3 mt-3', 'rows' => 7, 'data-force-start-case' => 'upper'])  }}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="form-group col-lg-12">
                        {{ Form::fLabel($model, 'custom_provisions', 'Želiš li u ugovor dodati odredbu koju mi nismo predvidjeli? <small>(opcionalno)</small>') }}
                    </div>
                </div>
                <div id="custom_provisions_container">
                    @if(!empty($model->custom_provisions))
                        @foreach($model->custom_provisions as $_i => $_custom_provision)
                            <div class="custom_provision_content">
                                <div class="card mb-4">
                                    <div class="card-header">
                                        Dodana odredba
                                        <strong><a class="btn btn-danger btn-sm float-right remove_custom_provision"><i
                                                        class="fa fa-trash"></i> Ukloni</a></strong>
                                    </div>
                                    <div class="card-body">
                                        {{  Form::fText($model, "custom_provisions[$_i][title]", isset($_custom_provision['title']) ? $_custom_provision['title'] : null, null, ['placeholder' => 'Upiši naziv odredbe', 'data-force-start-case' => 'upper'])  }}
                                        {{  Form::fTextArea($model, "custom_provisions[$_i][provision]", isset($_custom_provision['provision']) ? $_custom_provision['provision'] : null, null, ['placeholder' => 'Upiši tekst odredbe', 'class' => 'form-control mb-3 mt-3', 'rows' => 7, 'data-force-start-case' => 'upper'])  }}
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    @endif

                </div>
                <div class="row">
                    <div class="form-group col-lg-12">
                        <a class="btn btn-primary" id="add_custom_provision">+ Dodaj odredbu</a>
                    </div>
                </div>
            </div>
        </div>

</div>

</div>


{{ Form::close() }}
@endsection

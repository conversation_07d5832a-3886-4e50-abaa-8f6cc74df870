@extends('tenant.layouts.document.master')
@section('content')

    {{ Form::model($model, ['url' => $route, 'autocomplete' => 'off' ]) }}
    <div class="row">
        <div class="col">

            <div class="card mb-4">
                <div class="card-header">
                    Upiši podatke o poslodavcu
                    <span data-toggle="tooltip"
                          data-original-title="Prema Zakonu o radu, poslodavac je fizička ili pravna osoba koja zapošljava radnika i za koju radnik u radnom odnosu obavlja određene poslove.">
                        <i class="fa fa-info-circle"></i>
                    </span>
                </div>
                <div class="card-body" id="employer_content">
                    <div class="maps_autofill_container">
                        <div class="row">
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, 'employer_name', null, 'Ime i prezime/naziv', ['placeholder' => 'Npr: ACME d.o.o.']) }}
                            </div>
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, 'employer_address', null, 'Adresa', ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, 'employer_city', null, 'Grad/mjesto', ['data-maps-autofill' => 'city', 'placeholder' => 'Npr: Zagreb']) }}
                            </div>
                            <div class="form-group col-lg-6">
                                {{ Form::fNumber($model, 'employer_postal_code', null, 'Poštanski broj', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, 'employer_country', null, 'Država', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                            </div>
                            <div class="form-group col-lg-6">
                                {{ Form::fNumber($model, 'employer_oib', null, 'Osobni identifikacijski broj (OIB)', ['placeholder' => 'Npr: 12345678901']) }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group col-lg-12 pt-2">
                                {{ Form::fCheckbox($model, '', 1, 'Postoji zakonski zastupnik ili druga ovlaštena fizička osoba koja sklapa ugovor u ime i za račun poslodavca', ['id' => 'authorized_employer_persons_exist', 'checked' => !$model->is_edit || !empty($model->authorized_employer_persons)], 'Ovu opciju označi ako je poslodavac pravna osoba (npr. d.o.o., j.d.o.o., udruga, itd.). U tom slučaju, ugovor u ime i za račun poslodavca u pravilu sklapa i potpisuje jedan ili više zakonskih zastupnika ili punomoćnika (npr. direktor, članovi uprave, prokurist, itd.). Ovu opciju možeš označiti i ako je poslodavac fizička osoba (obrt, samostalna djelatnost, itd.), ali je poslodavac ovlastio neku drugu fizičku osobu da u njegovo ime i račun sklopi ugovor (npr. punomoćnik).') }}
                            </div>
                        </div>
                    </div>

                    <div id="authorized_employer_persons_container"
                         class="dynamic {{ $model->is_edit && empty($model->authorized_employer_persons) ? 'd-none' : '' }}">
                        <div id="authorized_employer_persons">
                            @if(!empty($model->authorized_employer_persons))
                                @foreach($model->authorized_employer_persons as $i => $_authorized_employer_person)
                                    <div class="authorized_employer_person_content maps_autofill_container">
                                        <hr/>
                                        <div class="row">
                                            <div class="col-lg-12">
                                                <strong><a class="btn btn-danger btn-sm float-right remove_authorized_employer_person"><i
                                                                class="fa fa-trash"></i> Ukloni</a></strong>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_employer_persons[$i][role]", null, 'Svojstvo zastupnika', ['placeholder' => 'Npr: direktor'], null, 'Upiši u kojem svojstvu navedena fizička osoba zastupa poslodavca') }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_employer_persons[$i][name]", null, 'Ime i prezime', ['placeholder' => 'Npr: Ante Antić'], null, 'Upiši ime i prezime fizičke osobe ovlaštene za sklapanje ugovora') }}
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_employer_persons[$i][address]", null, 'Adresa <small>(opcionalno)</small>', ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_employer_persons[$i][city]", null, 'Grad/mjesto <small>(opcionalno)</small>', ['placeholder' => 'Npr: Zagreb', 'data-maps-autofill' => 'city']) }}
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_employer_persons[$i][postal_code]", null, 'Poštanski broj <small>(opcionalno)</small>', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_employer_persons[$i][country]", null, 'Država <small>(opcionalno)</small>', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fNumber($model, "authorized_employer_persons[$i][oib]", null, 'Osobni identifikacijski broj (OIB) <small>(opcionalno)</small>', ['placeholder' => 'Npr: 12345678901']) }}
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            @endif
                            @if(empty($model->authorized_employer_persons))
                                    <div class="authorized_employer_person_content maps_autofill_container">
                                        <hr/>
                                        <div class="row">
                                            <div class="col-lg-12">
                                                <strong><a class="btn btn-danger btn-sm float-right remove_authorized_employer_person"><i
                                                                class="fa fa-trash"></i> Ukloni</a></strong>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_employer_persons[0][role]", null, 'Svojstvo zastupnika', ['placeholder' => 'Npr: direktor'], null, 'Upiši u kojem svojstvu navedena fizička osoba zastupa poslodavca') }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_employer_persons[0][name]", null, 'Ime i prezime', ['placeholder' => 'Npr: Ante Antić'], null, 'Upiši ime i prezime fizičke osobe ovlaštene za sklapanje ugovora') }}
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_employer_persons[0][address]", null, 'Adresa <small>(opcionalno)</small>', ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_employer_persons[0][city]", null, 'Grad/mjesto <small>(opcionalno)</small>', ['placeholder' => 'Npr: Zagreb', 'data-maps-autofill' => 'city']) }}
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_employer_persons[0][postal_code]", null, 'Poštanski broj <small>(opcionalno)</small>', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                                            </div>
                                            <div class="form-group col-lg-6">
                                                {{ Form::fText($model, "authorized_employer_persons[0][country]", null, 'Država <small>(opcionalno)</small>', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                {{ Form::fNumber($model, "authorized_employer_persons[0][oib]", null, 'Osobni identifikacijski broj (OIB) <small>(opcionalno)</small>', ['placeholder' => 'Npr: 12345678901']) }}
                                            </div>
                                        </div>
                                    </div>
                            @endif
                            <div id="authorized_employer_person_template" class="d-none">
                                <div class="authorized_employer_person_content maps_autofill_container">
                                    <hr/>
                                    <div class="row">
                                        <div class="col-lg-12">
                                            <strong><a class="btn btn-danger btn-sm float-right remove_authorized_employer_person"><i
                                                            class="fa fa-trash"></i> Ukloni</a></strong>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="form-group col-lg-6">
                                            {{ Form::fText($model, "authorized_employer_persons[{INDEX}][role]", null, 'Svojstvo zastupnika', ['placeholder' => 'Npr: direktor'], null, 'Upiši u kojem svojstvu navedena fizička osoba zastupa poslodavca') }}
                                        </div>
                                        <div class="form-group col-lg-6">
                                            {{ Form::fText($model, "authorized_employer_persons[{INDEX}][name]", null, 'Ime i prezime', ['placeholder' => 'Npr: Ante Antić'], null, 'Upiši ime i prezime fizičke osobe ovlaštene za sklapanje ugovora') }}
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="form-group col-lg-6">
                                            {{ Form::fText($model, "authorized_employer_persons[{INDEX}][address]", null, 'Adresa <small>(opcionalno)</small>', ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                                        </div>
                                        <div class="form-group col-lg-6">
                                            {{ Form::fText($model, "authorized_employer_persons[{INDEX}][city]", null, 'Grad/mjesto <small>(opcionalno)</small>', ['placeholder' => 'Npr: Zagreb', 'data-maps-autofill' => 'city']) }}
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="form-group col-lg-6">
                                            {{ Form::fText($model, "authorized_employer_persons[{INDEX}][postal_code]", null, 'Poštanski broj <small>(opcionalno)</small>', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                                        </div>
                                        <div class="form-group col-lg-6">
                                            {{ Form::fText($model, "authorized_employer_persons[{INDEX}][country]", null, 'Država <small>(opcionalno)</small>', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="form-group col-lg-6">
                                            {{ Form::fNumber($model, "authorized_employer_persons[{INDEX}][oib]", null, 'Osobni identifikacijski broj (OIB) <small>(opcionalno)</small>', ['placeholder' => 'Npr: 12345678901']) }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group col-lg-12">
                                <a class="btn btn-primary" id="add_authorized_employer_person">+ Dodaj zastupnika</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mb-4" id="employee_content">
                <div class="card-header">
                    Upiši podatke o radniku
                    <span data-toggle="tooltip"
                          data-original-title="Prema Zakonu o radu, radnik (zaposlenik, uposlenik, djelatnik, namještenik, službenik i slično) je fizička osoba koja u radnom odnosu obavlja određene poslove za poslodavca.">
                        <i class="fa fa-info-circle"></i>
                    </span>
                </div>
                <div class="card-body">
                    <div class="maps_autofill_container">
                        <div class="row">
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, 'employee_name', null, 'Ime i prezime', ['placeholder' => 'Npr: Stanko Zorić']) }}
                            </div>
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, 'employee_address', null, 'Adresa',
                                ['class' => 'maps-autofill-input form-control', 'data-maps-autofill-type' => 'address', 'data-maps-autofill' => 'address', 'placeholder' => 'Npr: Ilica 128']) }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, 'employee_city', null, 'Grad/mjesto', ['data-maps-autofill' => 'city', 'placeholder' => 'Npr: Zagreb']) }}
                            </div>
                            <div class="form-group col-lg-6">
                                {{ Form::fNumber($model, 'employee_postal_code', null, 'Poštanski broj', ['data-maps-autofill' => 'zip', 'placeholder' => 'Npr: 10000']) }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group col-lg-6">
                                {{ Form::fText($model, 'employee_country', null, 'Država', ['data-maps-autofill' => 'country', 'placeholder' => 'Npr: Hrvatska']) }}
                            </div>
                            <div class="form-group col-lg-6">
                                {{ Form::fNumber($model, 'employee_oib', null, 'Osobni identifikacijski broj (OIB)', ['placeholder' => 'Npr: 12345678901']) }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>


        </div>


    </div>

    {{ Form::close() }}
@endsection

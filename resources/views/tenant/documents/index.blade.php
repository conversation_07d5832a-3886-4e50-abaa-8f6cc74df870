@extends('tenant.layouts.common.master')

@section('title', 'Nadzorna ploča')

@section('content')
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <h1 class="mb-4">Nadzorna ploča</h1>

                    <!-- Document Creation Form -->
                    <div class="row">
                        <div class="col-lg-12">
                            <h5 class="mb-3">Izradi novi dokument</h5>
                            {{ html()->form('POST', route('tenant.document.create'))->open() }}
                            @csrf
                            <div class="form-row">
                                <div class="col-12 col-md-9 mb-2 mb-md-0">
                                    <div class="document-search-container">
                                        <select name="template_id" id="tenant-select-document"
                                                class="form-control form-control-lg" required>
                                            <option value="">Odaberi obrazac...</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-12 col-md-3">
                                    {{ html()->button('Izradi')->type('submit')->class('btn btn-primary btn-lg btn-block') }}
                                </div>
                            </div>
                            {{ html()->form()->close() }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <h4 class="mb-3">Dokumenti</h4>
                    <div class="row mb-4 mt-2 mb-lg-3 mt-lg-0">
                        <div class="col-lg-12">
                            <div class="btn-group" role="group">
                                <button id="wizard-toggle" type="button"
                                        class="btn @if(request()->hasCookie('documentsTab') && request()->cookie('documentsTab') !== 'wizard') btn-default @else btn-primary @endif">
                                    Obrasci
                                    @if($is_desktop)
                                        <span style="font-size: .9em;" data-placement="right" data-toggle="tooltip"
                                              data-original-title="Klikni ovdje ako u tablici ispod želiš prikazati dokumente izrađene pomoću obrazaca.">
                                            <i class="fa fa-info-circle"></i>
                                        </span>
                                    @endif
                                </button>
                                <button id="editor-toggle" type="button"
                                        class="btn @if(request()->cookie('documentsTab') === 'editor') btn-primary @else btn-default @endif">
                                    Uređivač
                                    @if($is_desktop)
                                        <span style="font-size: .9em;" data-placement="right" data-toggle="tooltip"
                                              data-original-title="Klikni ovdje ako u tablici ispod želiš prikazati dokumente uređene pomoću uređivača.">
                                            <i class="fa fa-info-circle"></i>
                                        </span>
                                    @endif
                                </button>
                            </div>
                        </div>
                    </div>

                    <div id="wizard-table-container"
                         @if(request()->hasCookie('documentsTab') && request()->cookie('documentsTab') !== 'wizard') style="display: none;" @endif>
                        <table class="table table-bordered table-hover table-striped" id="documents-table">
                            <thead>
                            <tr>
                                <th>ID</th>
                                <th>Dokument</th>
                                <th>Bilješka</th>
                                <th>Stvoren</th>
                                <th>Ažuriran</th>
                                <th></th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div id="editor-table-container"
                         @if(request()->cookie('documentsTab') !== 'editor') style="display: none;" @endif>
                        <table class="table table-bordered table-hover table-striped" id="document-drafts-table">
                            <thead>
                            <tr>
                                <th>ID</th>
                                <th>Dokument</th>
                                <th>Bilješka</th>
                                <th>Stvoren</th>
                                <th>Ažuriran</th>
                                <th></th>
                            </tr>
                            </thead>
                        </table>
                    </div>

                </div>
            </div>
        </div>
    </div>

    <div id="download-message" class="d-none mt-3 text-center">
        <div class="spinner-border spinner-border-sm text-primary" role="status">
            <span class="sr-only">Loading...</span>
        </div>
        Priprema dokumenta za preuzimanje...
    </div>

    <div id="modal-comment" class="modal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <form method="post" action="">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Bilješka</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <textarea rows="4" placeholder="Upiši bilješku..." name="comment"
                                      class="form-control"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="submit" class="btn btn-primary">Spremi bilješku</button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <div id="modal-editor-comment" class="modal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <form method="post" action="">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Bilješka</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <textarea rows="4" placeholder="Upiši bilješku..." name="comment"
                                      class="form-control"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="submit" class="btn btn-primary">Spremi bilješku</button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <div id="modal-title" class="modal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <form method="post" action="">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Naziv dokumenta</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <input required type="text" placeholder="Unesi naziv dokumenta..." name="title"
                                   class="form-control"/>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="submit" class="btn btn-primary">Spremi naziv</button>
                    </div>
                </div>
            </form>
            >
        </div>
    </div>
@endsection

@include('tenant.documents.partials.index.wizard_scripts')
@include('tenant.documents.partials.index.editor_scripts')

@push('scripts')
    <script type="text/javascript">
        $(function () {
            // Initialize Select2 for tenant document search
            if ($('#tenant-select-document').length) {
                $('#tenant-select-document').select2({
                    placeholder: 'Odaberi obrazac...',
                    search: true,
                    allowClear: false,
                    minimumInputLength: 0,
                    language: {
                        errorLoading: function () {
                            return 'Nije pronađen nijedan dokument.';
                        },
                        loadingMore: function () {
                            return 'Pretražujem dokumente...';
                        },
                        maximumSelected: function (args) {
                            return 'Maksimalan broj odabranih stavki je ' + args.maximum;
                        },
                        noResults: function () {
                            return 'Nema rezultata';
                        },
                        searching: function () {
                            return 'Pretražujem dokumente...';
                        }
                    },
                    escapeMarkup: function (markup) {
                        return markup; // Allows rendering of HTML markup
                    },
                    ajax: {
                        url: '{{ route('tenant.search.templates') }}',
                        type: 'post',
                        dataType: 'json',
                        delay: 0,
                        data: function (params) {
                            return {
                                _token: $("[name='_token']").val(), // csrf token
                                query: params.term || '', // search term (empty string for initial load)
                            };
                        },
                        processResults: function (data) {
                            let results = [];

                            if (data['query']) {
                                // When there's a search query, show flat results
                                $.each(data['results'], ($_i, _data) => {
                                    results.push({
                                        'id': _data['id'],
                                        'text': _data['title'],
                                    });
                                });
                            } else {
                                // When no search query, show categorized results
                                $.each(data['results'], (_category, _data) => {

                                    let templates = [];

                                    $.each(_data['templates'], (__order_index, __template_data) => {
                                        templates.push({
                                            'id': __template_data['id'],
                                            'text': __template_data['title'],
                                        });
                                    });

                                    results.push({
                                        'text': _data['category'],
                                        'children': templates
                                    });
                                });
                            }

                            return {
                                results: results
                            };
                        },
                        cache: true
                    }
                });
            }
        });
    </script>
@endpush

@push('scripts')
    <script type="text/javascript">
        $(function () {

            // set documents tab cookie expiration date (+1 year)
            const date = new Date();
            date.setTime(date.getTime() + (365 * 24 * 60 * 60 * 1000));

            const tabCookieExpiration = "expires=" + date.toUTCString();

            $('#wizard-toggle').on('click', function () {
                // remember tab
                document.cookie = 'documentsTab=wizard; ' + tabCookieExpiration + "; path=/";

                $('#editor-toggle').removeClass('btn-primary').addClass('btn-default');
                $('#translations-toggle').removeClass('btn-primary').addClass('btn-default');
                $(this).removeClass('btn-default').addClass('btn-primary');

                $('#editor-table-container').hide();
                $('#translations-table-container').hide();
                $('#wizard-table-container').show();
                $('#documents-table').DataTable().draw(); // Redraw the DataTable

                $('#upload-translation').addClass('d-none');
            });

            $('#editor-toggle').on('click', function () {
                // remember tab
                document.cookie = 'documentsTab=editor; ' + tabCookieExpiration + "; path=/";

                $('#wizard-toggle').removeClass('btn-primary').addClass('btn-default');
                $('#translations-toggle').removeClass('btn-primary').addClass('btn-default');
                $(this).removeClass('btn-default').addClass('btn-primary');

                $('#wizard-table-container').hide();
                $('#translations-table-container').hide();
                $('#editor-table-container').show();
                $('#document-drafts-table').DataTable().draw(); // Redraw the DataTable

                $('#upload-translation').addClass('d-none');
            });
        });
    </script>
@endpush


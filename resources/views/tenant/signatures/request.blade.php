@extends('tenant.layouts.common.master')
@section('title', 'Potpis')

@section('content')

    <div class="row">
        <div class="col-lg-6 offset-lg-3">

            <h2 class="mb-3 text-lg-center">{{ $party->document->title }}</h2>
            <hr/>
            <button id="show_preview" class="btn btn-default btn-block ">Pregledaj dokument</button>
            <hr/>

            <form action="{{ route('tenant.signature.request.send', [$document, $party]) }}" method="post">
                @csrf
                <div class="form-group">
                    <div class="alert alert-warning">

                        {!! $party->label !!}
                    </div>
                    <input required type="email" class="form-control" name="email" placeholder="Upiši adresu e-pošte...">
                </div>

                <input type="submit" class="btn btn-primary btn-block" value="Zatraži e-Potpis">
            </form>
        </div>
    </div>

    <div id="preview" class="d-none">
        <hr/>
        <div class="row">
            <div class="col-lg-10 offset-lg-1">
                @include('tenant.common/partials/preview', ['url' => \Illuminate\Support\Facades\URL::signedRoute('tenant.document.stream', [$document])])
            </div>
        </div>
    </div>



@endsection

@push('scripts')
    <script type="text/javascript">
        $(function() {
            $('#show_preview').on('click', function(e) {
                $('#preview').removeClass('d-none');
                $('html, body').animate({
                    scrollTop: $("#preview").offset().top - 50
                }, 500);
            })
        });
    </script>
@endpush

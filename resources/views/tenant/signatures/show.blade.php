@extends('tenant.layouts.common.master')
@section('title', 'Potpis')

@section('content')

    <div class="row">
        <div class="col-lg-6 offset-lg-3 text-center">

            <h2 class="mb-3 text-lg-center">{{ $party->document->title }}</h2>
            <hr/>

            <div class="row text-center">
                <hr/>
                @if(!empty($party->signature))
                    <a href="{{ \Illuminate\Support\Facades\URL::signedRoute('tenant.document.download', $party->document) }}" class="btn btn-primary btn-block">Preuzmi dokument</a>
                    <div class="col-lg-12">
                        <hr/>
                        <h3>Vaš e-Potpis</h3>
                        <img alt="signature" src="{{ \Illuminate\Support\Facades\URL::signedRoute('tenant.signature.image', $party) }}">
                        <div class="mt-2 mb-3">
                            {!! $party->label !!}
                        </div>
                    </div>
                @else
                    <button id="show_preview" class="btn btn-default btn-block ">Pregledaj dokument</button>
                    <div class="col-lg-12">
                        <hr/>
                        <h3>Novi e-Potpis</h3>

                        <div id="canvas-container" class="mt-3 mb-3">
                            <canvas style="border: 1px dotted black; background: rgb(11 11 7 / 2%);"></canvas>
                            <div>
                                {!! $party->label !!}
                            </div>
                        </div>

                        <div id="save_signature">
                            <form class="mb-4" method="post" action="{{ \Illuminate\Support\Facades\URL::signedRoute('tenant.signature.save', $party) }}">
                                @csrf
                                <input type="hidden" name="party_id" value="{{ $party->id }}">
                                <input type="hidden" name="signature_request_id" value="{{ $signature_request_id }}">
                                <input id="party_signature" type="hidden" name="party_signature">

                                <hr/>
                                @if($party->document->template->isContract())
                                <div class="form-check mb-3">
                                    <input required class="form-check-input" type="checkbox" value="" id="tos">
                                    <label class="form-check-label" for="tos">
                                        Potvrđujem da sam upoznat s time i da u potpunosti razumijem da stavljanjem svojeg potpisa na ovoj stranici i klikom na gumb "Potpiši dokument" prihvaćam sve odredbe ovog ugovora u cijelosti te me ugovor pravno obvezuje, što znači da sam dužan ispuniti sve svoje obveze navedene u ugovoru i odgovaram za njihovo ispunjenje.
                                    </label>
                                </div>
                                <hr/>
                                @endif

                                <input class="btn btn-primary" type="submit" value="Potpiši dokument">
                                <button type="button" class="btn btn-secondary" id="clear">Poništi</button>
                            </form>
                        </div>
                    </div>
                @endif

            </div>
        </div>
    </div>

    <div id="preview" class="d-none">
        <hr/>
        <div class="row">
            <div class="col-lg-10 offset-lg-1">
                @include('tenant.common/partials/preview', ['url' => \Illuminate\Support\Facades\URL::signedRoute('tenant.document.stream', [$party->document])])
            </div>
        </div>
    </div>





@endsection

@push('scripts')
    <script src="/js/signature_pad.min.js"></script>

    <script type="text/javascript">
        $(function() {

            let canvas = document.querySelector("canvas");
            let container = $('#canvas-container');

            canvas.width = container.width();
            canvas.height = container.height();

            let signaturePad = new SignaturePad(canvas, {
                penColor: "rgb(24,17,122)"
            });

            $('#clear').on("click", function (e) {
                signaturePad.clear();
            });

            $('#undo').on("click", function (e) {
                var data = signaturePad.toData();

                if (data) {
                    data.pop(); // remove the last dot or line
                    signaturePad.fromData(data);
                }
            });


            $('#save_signature form').on('submit', function(e){
                if (signaturePad.isEmpty()) {
                    alert("Molimo unesite e-Potpis na za to predviđeno mjesto koristeći miš ili prste ako koristite zaslon osjetljiv na dodir.");
                    e.preventDefault();
                } else {
                    let signature = signaturePad.toDataURL();
                    $('#party_signature').val(signature);
                }
            });

        });

        $(function() {
            $('#show_preview').on('click', function(e) {
                $('#preview').removeClass('d-none');
                $('html, body').animate({
                    scrollTop: $("#preview").offset().top - 50
                }, 500);
            })
        });
    </script>
@endpush

<!DOCTYPE html>

<html lang="hr">

<head>
    @include('tenant.layouts.common.partials.head')
    <!-- Editor CSS -->
    <link href="{{ mix('css/editor.css') }}" rel="stylesheet">
</head>

<body>
<div id="overlay"></div>
<div id="layout-container">

    @include('tenant.layouts.common.partials.nav')
    <div class="fixed-top pt-3 mt-5" id="editor-toolbar"></div>
    <div id="layout-content">

        <div class="container margin-top pb-5">
            @if (session('success'))
                <div class="alert alert-success">
                    {!! session('success') !!}
                </div>
            @endif
            @if (session('error'))
                <div class="alert alert-danger">
                    {!! session('error') !!}
                </div>
            @endif
            @if (isset($errors) && $errors->any())
                <div class="alert alert-danger">
                    @if($errors->has('override'))
                        {{ collect($errors->get('override'))->first() }}
                    @else
                        <ul>
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    @endif

                </div>
            @endif
            <div class="row">
                <div class="col-lg-12">
                    @yield('content')
                </div>
            </div>
        </div>
    </div>
    @include('tenant.layouts.common.partials.footer')
</div>

@include('tenant.layouts.common.partials.footer-scripts')

<script src="/vendor/tinymce/tinymce.min.js?ver=6.8.3" referrerpolicy="origin"></script>
<script src="/vendor/tinymce/langs/hr.js" referrerpolicy="origin"></script>
<script src="{{ mix('js/editor/parties.js') }}"></script>
<script src="{{ mix('js/editor/title.js') }}"></script>
<script src="{{ mix('js/editor/body.js') }}"></script>
<script src="{{ mix('js/editor/article_header.js') }}"></script>
<script src="{{ mix('js/editor/article_body.js') }}"></script>
<script src="{{ mix('js/editor/signees.js') }}"></script>
<script src="{{ mix('js/editor/attachments.js') }}"></script>
<script src="{{ mix('js/editor/legal_remedy.js') }}"></script>
<script src="{{ mix('js/editor/recipients.js') }}"></script>
<script src="{{ mix('js/editor/editor.js') }}"></script>
<script src="{{ mix('js/editor/spellcheck.js') }}"></script>

</body>

</html>

<!DOCTYPE html>

<html lang="hr">

<head>
    @include('tenant.layouts.common.partials.head')
</head>

<body>
<div id="overlay"></div>
<div id="layout-container">

    @include('tenant.layouts.common.partials.nav')

    <div id="layout-content">
        <div class="container margin-top pb-5">
            @if (session('success'))
                <div class="alert alert-info">
                    {!! session('success') !!}
                </div>
            @endif
            @if (session('error'))
                <div class="alert alert-danger">
                    {!! session('error') !!}
                </div>
            @endif
            @if (isset($errors) && $errors->any())
                <div class="alert alert-danger">
                    @if($errors->has('override'))
                        {{ collect($errors->get('override'))->first() }}
                    @else
                        <ul>
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    @endif

                </div>
            @endif
            <div class="row">
                <div class="col-lg-12">
                    @yield('content')
                </div>
            </div>
        </div>
    </div>

    @include('tenant.layouts.common.partials.footer')

</div>

@include('tenant.layouts.common.partials.footer-scripts')

</body>

</html>

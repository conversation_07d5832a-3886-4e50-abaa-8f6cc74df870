<nav class="bg-white-5 fixed-top navbar navbar-expand-md navbar-light bg-white shadow-sm">
    <div class="container">
        <a class="navbar-brand" href="{{ route('tenant.dashboard') }}">
            {{ config('app.name') }}
        </a>
        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="{{ __('Toggle navigation') }}">
            <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarSupportedContent">
            <!-- Left Side Of Navbar -->
            <ul class="navbar-nav mr-auto">

            </ul>

            <!-- Right Side Of Navbar -->
            <ul class="navbar-nav ml-auto navbar-spacing">
                @auth
                    <li class="nav-item dropdown">
                        <a id="navbarDropdown" class="nav-link dropdown-toggle" href="#" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" v-pre>
                            {{ Auth::user()->name }}
                        </a>

                        <div class="dropdown-menu dropdown-menu-right" aria-labelledby="navbarDropdown">

                            <a class="dropdown-item" href="{{ route('tenant.account') }}">
                                {{ __('Moj račun') }}
                            </a>
                            <a class="dropdown-item" href="{{ route('tenant.dashboard') }}">
                                {{ __('Nadzorna ploča') }}
                            </a>
                            <a class="dropdown-item" href="{{ route('tenant.branding') }}">
                                {{ __('Branding') }}
                            </a>
                            <a class="dropdown-item" href="{{ route('tenant.logout') }}"
                               onclick="event.preventDefault();
                                                     document.getElementById('logout-form').submit();">
                                {{ __('Odjava') }}
                            </a>

                            <form id="logout-form" action="{{ route('tenant.logout') }}" method="POST" class="d-none">
                                @csrf
                            </form>
                        </div>
                    </li>
                @endauth
                @guest
                    <li class="nav-item">
                        <a class="nav-link @if(Route::current()->getName() == 'tenant.login') active @endif" onclick="event.preventDefault(); document.getElementById('login-form').submit()" href="{{ route('tenant.login') }}">{{ __('Prijava') }}</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link @if(Route::current()->getName() == 'contact') active @endif" href="#">{{ __('Kontakt') }}</a>
                    </li>
                @endguest
            </ul>
        </div>
    </div>
</nav>

<style>
    #canvas_container {
        text-align: center;
        border: solid 1px;
    }

    #navigation_controls {
        padding: 20px;
        text-align: center;
    }
</style>
<div>
    <div id="navigation_controls">
        <button class="go_previous btn btn-primary">Prethodna</button>
        <input disabled style="padding-top:2px; display:inline-block; max-width: 65px; text-align: center" class="current_page form-control" value="1" type="number"/>
        <button class="go_next btn btn-primary">Slje<PERSON>ća</button>
    </div>
    <div id="canvas_container">
        <div id="loading">Učitavam...</div>
        <canvas style="width: 100%;" id="pdf_renderer">Učitavam...</canvas>
    </div>
    <div id="navigation_controls">
        <button class="go_previous btn btn-primary">Prethodna</button>
        <input disabled style="padding-top:2px; display:inline-block; max-width:65px; text-align: center" class="current_page form-control" value="1" type="number"/>
        <button class="go_next btn btn-primary">S<PERSON><PERSON><PERSON><PERSON>a</button>
    </div>
</div>

<script
        src="/js/pdf.min.js">
</script>
<script>
    let myState = {
        pdf: null,
        currentPage: 1,
        zoom: 2
    };

    pdfjsLib.getDocument('{{ $url }}').then((pdf) => {

        myState.pdf = pdf;
        render();

    });

    function render() {
        myState.pdf.getPage(myState.currentPage).then((page) => {

            var canvas = document.getElementById("pdf_renderer");
            var ctx = canvas.getContext('2d');

            var viewport = page.getViewport(myState.zoom);

            canvas.width = viewport.width;
            canvas.height = viewport.height;

            page.render({
                canvasContext: ctx,
                viewport: viewport
            });

            document.getElementById("loading").style.display = "none";

        });
    }

    Array.prototype.forEach.call(document.getElementsByClassName('go_previous'), function(el) {
        el.addEventListener('click', (e) => {
            if(myState.pdf == null
                || myState.currentPage == 1) return;
            myState.currentPage -= 1;

            Array.prototype.forEach.call(document.getElementsByClassName("current_page"), function(el) {
                el.value = myState.currentPage;
            });

            render();
        });
    });

    Array.prototype.forEach.call(document.getElementsByClassName('go_next'), function(el) {
        el.addEventListener('click', (e) => {
            if(myState.pdf == null
                || myState.currentPage >= myState.pdf
                    ._pdfInfo.numPages)
                return;

            myState.currentPage += 1;

            Array.prototype.forEach.call(document.getElementsByClassName("current_page"), function(el) {
                el.value = myState.currentPage;
            });

            render();
        });
    });


</script>

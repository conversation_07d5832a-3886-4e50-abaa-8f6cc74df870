@component('mail::message')
{{-- Greeting --}}
@if (! empty($greeting))
# {{ $greeting }}
@endif

{{-- Intro Lines --}}
@foreach ($introLines as $line)
{!! $line !!}

@endforeach

{{-- Action Button --}}
@isset($actionText)
@php
    switch ($level) {
        case 'success':
        case 'error':
            $color = $level;
            break;
        default:
            $color = 'primary';
    }
@endphp
@component('mail::button', ['url' => $actionUrl, 'color' => $color])
{{ $actionText }}
@endcomponent
@endisset

{{-- Outro Lines --}}
@foreach ($outroLines as $line)
{{ $line }}

@endforeach

{{-- Salutation --}}
@if (! empty($salutation))
    @if($salutation !== "none")
        {{ $salutation }}
    @endif
@else
Zahvaljujemo što koristite Legaldesk.hr!
@endif

{{-- Subcopy --}}
@isset($actionText)
@slot('subcopy')
@lang(
            "Ako imate poteškoća s otvaranjem poveznice \":actionText\", kopirajte i zalijepite ovu poveznicu\n".
            'u svoj web preglednik:',
            [
                'actionText' => $actionText,
            ]
        ) <span class="break-all"><a href="{{ $actionUrl }}">{{ $displayableActionUrl }}</a></span>
@isset($email_preferences_link)
    <br/><br/>
    <span style="color:#74787e;">Ako ne želite više primati e-poštu od Legaldeska ili želite naknadno urediti svoje postavke za obavijesti, možete to učiniti ovdje:</span>
    <span class="break-all"> <a href="{{ $email_preferences_link }}">{{ $email_preferences_link }}</a></span>
@endisset
@endslot
@endisset
@endcomponent

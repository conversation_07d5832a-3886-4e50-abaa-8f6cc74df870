@extends('layouts.common.master')
@section('title', 'Kontakt')
@section('description', 'Pošalji upit vezan uz Legaldesk.hr')

@section('content')
    <h1 class="pb-4">Kontaktirajte nas</h1>

    {{ html()->form('POST', action('ContactController@store'))->attribute('enctype', 'multipart/form-data')->open() }}
    {!! GoogleReCaptchaV3::renderField('contact_id','contact') !!}
    <div class="row">
        <div class="col-lg-8">
            <x-honeypot/>
            <div class="form-group">
                {{ html()->label('Vrsta upita', 'topic_id') }}
                {{ html()->select('topic_id', \App\Models\Contact::$topics, null)
                    ->placeholder('Odaberite vrstu upita...')
                    ->id('topic-id')
                    ->class('form-control')
                    ->required() }}
            </div>
        </div>
    </div>
    @if(Auth::guest())
        <div class="row">
            <div class="col-lg-8">
                <div class="form-group">
                    {{ html()->label('Va<PERSON> adresa e-pošte', 'email') }}
                    {{ html()->email('email')
                        ->placeholder('Upišite svoju adresu e-pošte...')
                        ->class('form-control')
                        ->required() }}
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-lg-8">
                <div class="form-group">
                    {{ html()->label('Broj telefona', 'phone') }} <small>(opcionalno)</small>
                    {{ html()->input('text', 'phone')
                        ->placeholder('Upišite svoj broj telefona ili mobitela...')
                        ->class('form-control') }}
                </div>
            </div>
        </div>
    @else
        {{ html()->hidden('email', Auth::user()->email) }}
    @endif

    <div class="row">
        <div class="col-lg-8">
            <div class="form-group">
                {{ html()->label('Vaša poruka', 'message') }}
                {{ html()->textarea('message')
                    ->placeholder('Upišite svoju poruku...')
                    ->class('form-control')
                    ->rows(5)
                    ->required() }}
            </div>
        </div>
    </div>

    <div id="uploads-container" class="row"
         @if(!old('topic_id') || old('topic_id') != array_flip(\App\Models\Contact::$topics)['Prijava pogreške']) style="display: none;" @endif>
        <div class="col-lg-8">
            <div class="form-group">
                {{ html()->label('Priloži slike', 'slike') }} <span data-toggle="tooltip"
                                                                    data-original-title="Kako bismo čim prije ispravili pogrešku koju prijavljujete, molimo da uz svoju poruku priložite sliku zaslona na kojoj je pogreška jasno vidljiva."><i
                            class="fa fa-info-circle"></i></span> <small>(opcionalno)</small> <br/>
                {{ html()->file('slike[]')->attribute('multiple')->attribute('accept', 'image/*') }}
            </div>
        </div>
    </div>

    @guest
        <div class="row">
            <div class="col-lg-8">
                <div class="form-check form-group">
                    {{ html()->checkbox('checkbox', false, 1)->id('checkbox')->class('form-check-input')->required() }}
                    {{ html()->label('Dajem privolu da se moja adresa e-pošte odnosno telefonski broj koriste za odgovor na moj upit', 'checkbox')->class('form-check-label') }}
                </div>
            </div>
        </div>
    @endguest
    <div class="row">
        <div class="col-lg-8">
            <div class="form-group">
                {{ html()->submit('Pošalji upit')->class('btn btn-md btn-primary btn-block') }}
            </div>
        </div>
    </div>
@endsection


@push('scripts')
    <script type="text/javascript">
        $(document).ready(function () {
            // if get parameter "topic" is "account", change #topic-id
            if (window.location.href.indexOf('topic=account') > -1) {
                $('#topic-id').val('{{ array_flip(\App\Models\Contact::$topics)['Želim otvoriti Legaldesk račun'] }}').trigger('change');
            }

            // on #topic-id change
            $('#topic-id').on('change', function () {
                // if value is "Prijava pogreške", show #uploads-container
                if (this.value === "{{ array_flip(\App\Models\Contact::$topics)['Prijava pogreške'] }}") {
                    $('#uploads-container').show();
                } else {
                    $('#uploads-container').hide();
                }
            })
        })
    </script>
@endpush

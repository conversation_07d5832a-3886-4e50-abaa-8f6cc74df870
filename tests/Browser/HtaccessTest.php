<?php

namespace Tests\Browser;

use Illuminate\Support\Facades\App;
use <PERSON><PERSON>\Dusk\Browser;
use Tests\DuskTestCase;

/**
 * Class HtaccessTest
 *
 * Tests that certain pages are 403 protected by .htaccess
 *
 * @package Tests\Browser
 */
class HtaccessTest extends DuskTestCase {

    public function testStagingIsProtected() {

        $this->browse(function (Browser $browser) {
            $browser->visit('https://staging.legaldesk.hr')->assertDontSee('Legaldesk');    // expect 403
        });

    }

}


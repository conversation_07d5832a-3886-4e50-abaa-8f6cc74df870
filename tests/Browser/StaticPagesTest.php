<?php

namespace Tests\Browser;

use Illuminate\Support\Facades\App;
use <PERSON><PERSON>\Dusk\Browser;
use Tests\DuskTestCase;

/**
 * Class StaticPagesTest
 *
 * Test rendering of static pages
 *
 * @package Tests\Browser
 */
class StaticPagesTest extends DuskTestCase {

	public function test() {

		$this->browse(function (Browser $browser) {

			$routes = [
				'/' => 'Pametni način izrade pravnih dokumenata',
			];

			foreach($routes as $_route => $_expected_text) {
				$browser->visit($_route)->assertSee($_expected_text);
			}

            $browser->quit();

		});
	}

}

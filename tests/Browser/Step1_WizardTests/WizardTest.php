<?php

namespace Tests\Browser\Step1_WizardTests;

use App\Models\DocumentTemplate;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\DatabaseTruncation;
use Illuminate\Support\Facades\URL;
use <PERSON><PERSON>\Dusk\Browser;
use Tests\DuskTestCase;

/**
 * Class WizardTest
 *
 * Test wizard flow as both registered and guest user
 *
 * @package Tests\Browser
 */
class WizardTest extends DuskTestCase {

	use DatabaseTruncation;

	protected $tablesToTruncate = [
		'users',
		'user_options',
		'documents',
		'document_content',
		'document_template_section_values',
		'document_parties',
		'email_preferences'
	];

    private $templates;

    public function setUp(): void {
        parent::setUp();
        $this->templates = DocumentTemplate::with('sections')->where(['is_visible' => 1])->get();
    }

    public function tearDown(): void {
        parent::tearDown();
        foreach (static::$browsers as $browser) {
            $browser->driver->manage()->deleteAllCookies();
        }
    }

    public function testWizardGuest() {
        $this->browse(function (Browser $browser) {
            $browser->visit('/')->assertSee('Pametni način izrade pravnih dokumenata');
            $this->acceptTOS($browser);

            foreach ($this->templates as $_template) {
                $browser->visit('/');
                $browser->script('$(".document-search-container").html("<input type=\'hidden\' name=\'template_id\' value=\''.$_template->id.'\'>")');
                $browser->press('Izradi');

                if($browser->seeLink('Izradi dokument')) {
                    $browser->clickLink('Izradi dokument');
                }

                $this->runWizard($browser, $_template);
                $browser->assertPathIs('/login');
                $browser->assertSee('Prijavite se ili besplatno napravite račun da biste preuzeli svoj dokument');
            }

            $this->registerUser($browser);
            $browser->assertPathIs('/email/verify');
            $browser->assertSee('Potvrdi adresu e-pošte');
            $browser->assertPathIs('/moji-dokumenti');
            $browser->assertSee('Uspješno ste spremili dokument');

            // go to homepage to prevent errors once migrations are refreshed
            $browser->visit('/');
        });
    }

    public function testWizardUser() {
        $this->browse(function (Browser $browser) {
            $browser->visit('/')->assertSee('Pametni način izrade pravnih dokumenata');
            $this->acceptTOS($browser);
            $this->registerUser($browser);
            $browser->visit('/')->assertSee('Pametni način izrade pravnih dokumenata');

            foreach ($this->templates as $_template) {
                $browser->visit('/');
                $browser->script('$(".document-search-container").html("<input type=\'hidden\' name=\'template_id\' value=\''.$_template->id.'\'>")');
                $browser->press('Izradi');

                if($browser->seeLink('Izradi dokument')) {
                    $browser->clickLink('Izradi dokument');
                }

                $this->runWizard($browser, $_template);
                $browser->assertPathIs('/moji-dokumenti');
                $browser->assertSee('Uspješno ste spremili dokument');

                // go to homepage to prevent errors once migrations are refreshed
                $browser->visit('/');
            }
        });
    }

    private function runWizard(Browser $browser, DocumentTemplate $template) {
        foreach ($template->sections as $_section) {
            $browser->assertSee($_section->nav_title);

            if ($_section->view !== 'final_provisions') {
                $browser->clickLink('Spremi i nastavi');
            } else {
                $browser->clickLink('Spremi dokument');
            }
        }
    }

    private function acceptTOS(Browser $browser) {
        $browser->waitForText('O kolačićima na ovoj stranici');
        $browser->press('Prihvaćam sve');
    }

    private function registerUser(Browser $browser) {
        $browser->visit('/register')->assertSee('Novi korisnik');
        $browser->type('name', 'Dusk');
        $browser->type('email', env('DUSK_USER'));
        $browser->type('password', 'dusk123');
        $browser->type('password_confirmation', 'dusk123');
        $browser->check('#tos');
        $browser->press('Izradi račun');
    }
}

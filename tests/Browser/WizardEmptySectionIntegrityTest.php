<?php

namespace Tests\Browser;

use App\Helpers\DocumentBuilder;
use App\Models\Document;
use App\Models\DocumentTemplate;
use Illuminate\Support\Facades\Storage;
use Lara<PERSON>\Dusk\Browser;
use Tests\DuskTestCase;

/**
 * Class WizardEmptySectionIntegrityTest
 *
 * Not entering any values should produce same output, regardless of which section you press the "preview" button on
 *
 * @package Tests\Browser
 */
class WizardEmptySectionIntegrityTest extends DuskTestCase {

    private $templates;
    private $document_html;

    public function setUp(): void {

        parent::setUp();

        // get document templates
        $this->templates = DocumentTemplate::with('sections')->where(['is_visible' => 1])->get();
    }

    public function testTemplates() {

        $this->browse(function (Browser $browser) {

            // visit homepage
            $browser->visit('/')->assertSee('Pametni način izrade pravnih dokumenata');;
            $this->acceptTOS($browser);

            // run wizard for each document template
            foreach ($this->templates as $_template) {

                // visit homepage
                $browser->visit('/')->assertSee('Pametni način izrade pravnih dokumenata');

                // inject template input
                $browser->script('$(".document-search-container").html("<input type=\'hidden\' name=\'template_id\' value=\''.$_template->id.'\'>")');

                $browser->press('Izradi');

				// in case it has a landing page, click the Izradi link
	            if($browser->seeLink('Izradi dokument')) {
					$browser->clickLink('Izradi dokument');
	            }

                // run wizard
                $this->runWizard($browser, $_template);
            }

            $browser->quit();

        });
    }

    private function runWizard(Browser $browser, DocumentTemplate $template) {

        foreach ($template->sections as $_section) {

            $browser->assertSee($_section->nav_title);

            if ($_section->view !== 'final_provisions') {
                $browser->clickLink('Spremi i nastavi');

                $document = Document::all()->last();
                $document->load(['template', 'sectionValues']);

                $builder = new DocumentBuilder($document);
                $document_html = str_replace(" ", "", $builder->getContent());

                if(!empty($this->document_html)){
                    if($this->document_html != $document_html) {
                        Storage::put('tests/document_output_1.txt', $this->document_html);
                        Storage::put('tests/document_output_2.txt', $document_html);
                        throw new \Exception('Section "'.$_section->nav_title.'" of '.$template->title.' produced different output');
                    }
                }

                $this->document_html = $document_html;

            } else {
                // reset for next document template
                $this->document_html = null;
            }
        }
    }

    private function acceptTOS(Browser $browser) {

        $browser->waitForText('O kolačićima na ovoj stranici');
        $browser->press('Prihvaćam sve');
    }
}

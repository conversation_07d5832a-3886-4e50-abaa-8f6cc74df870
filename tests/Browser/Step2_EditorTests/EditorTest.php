<?php

namespace Tests\Browser\Step2_EditorTests;

use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverKeys;
use Illuminate\Support\Facades\App;
use <PERSON><PERSON>\Dusk\Browser;
use Tests\DuskTestCase;

/**
 * Class EditorTest
 *
 * Test editor flow as document exports
 *
 * Important - this test file should run after WizardTest, in order to make sure documents are already available
 *
 * @package Tests\Browser
 */
class EditorTest extends DuskTestCase {

    public function testDocumentExport() {
        $this->browse(function (Browser $browser) {
            $this->login($browser);
            $browser->assertPathIs('/moji-dokumenti');

	        $browser->pause(1000);

            $export_links = [];

            // function to get total pages
            $get_total_pages = function() use ($browser) {
                return count($browser->elements('#documents-table_paginate .paginate_button')) - 2; // Exclude "Previous" and "Next" buttons
            };

            $total_pages = $get_total_pages();

            // loop through each page of the table.
            for ($page = 0; $page < $total_pages; $page++) {
                // extract the "Izvezi u uređivač" links from the jQuery datatable and store them in the array
                $export_links = array_merge($export_links, $this->getExportLinksFromTable($browser));

                // click the "Next" button if there are more pages to navigate
                if ($page < $total_pages - 1) {
                    $browser->click('#documents-table_next a'); // click the anchor tag inside the element with ID "documents-table_next"

                    // wait for the new page to load, then re-fetch the total pages
                    $browser->pause(500);
                    $total_pages = $get_total_pages();
                }
            }

            if(!empty($export_links)) {
                $this->processLinks($browser, $export_links);
            } else {
				$browser->screenshot('no_documents_found');
                $this->fail('No documents found');
            }
        });
    }

    private function processLinks($browser, $export_links) {
        foreach ($export_links as $_export_link) {
            $browser->visit($_export_link)->assertSee('Spremi dokument');

            // refresh page if tutorial modal present
            if ($browser->element('#tutorialModal')) {
                $browser->refresh();
                $browser->pause(500);
            }

            $browser->clickLink('Spremi dokument')->waitForText('Spremi i završi')->clickLink('Spremi i završi')  ;
        }
    }

    private function login($browser) {
        $browser->visit('/login')->assertSee('Prijavi se');
        $this->acceptTOS($browser);
        $browser->type('email', env('DUSK_USER'));
        $browser->type('password', 'dusk123');
        $browser->press('Prijavi se');
    }

    private function acceptTOS(Browser $browser) {
        $browser->waitForText('O kolačićima na ovoj stranici');
        $browser->press('Prihvaćam sve');
    }

    private function getExportLinksFromTable($browser) {
        $links = [];

        // get all dropdowns with class "documentDropdown"
        $dropdowns = $browser->elements('.documentDropdown');

        foreach ($dropdowns as $dropdown) {
            // click the dropdown to expand it.
            $browser->driver->action()->click($dropdown)->perform();

            // get the "Izvezi u uređivač" link from the expanded dropdown
            $link_element = $dropdown->findElement(WebDriverBy::xpath('.//a[contains(text(), "Izvezi u uređivač")]'));

            // add the link to the array.
            $links[] = $link_element->getAttribute('href');

            // close the dropdown.
            $browser->driver->action()->click($dropdown)->perform();
        }

        return $links;
    }

}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateContractPartiesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('contract_parties', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('contract_id')->index();
            $table->text('name');
            $table->text('label');
            $table->string('side');
            $table->string('signature')->nullable();
            $table->timestamps();
            $table->dateTime('signed_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('contract_parties');
    }
}

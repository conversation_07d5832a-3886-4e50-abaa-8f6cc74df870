<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('document_translation_orders', function (Blueprint $table) {

	        $table->tinyInteger('type_id')->after('id')->default(1)->index();

            $table->tinyInteger('personal_delivery_type_id')->after('translation_id')->nullable()->index();
	        $table->tinyInteger('custom_delivery_type_id')->after('translation_id')->nullable()->index();
	        $table->tinyInteger('binding_type_id')->after('translation_id')->nullable()->index();


        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('document_translation_orders', function (Blueprint $table) {

			$table->dropColumn('type_id');
			$table->dropColumn('binding_type_id');
			$table->dropColumn('custom_delivery_type_id');
			$table->dropColumn('personal_delivery_type_id');
        });
    }
};

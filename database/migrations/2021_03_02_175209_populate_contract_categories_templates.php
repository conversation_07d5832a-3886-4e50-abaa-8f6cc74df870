<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class PopulateContractCategoriesTemplates extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
	    DB::statement("INSERT INTO `contract_categories_templates` (`id`, `category_id`, `template_id`, `order_index`, `created_at`, `updated_at`) VALUES
(1, 1, 1, 0, '2021-02-20 21:09:59', '2021-02-20 21:09:59'),
(2, 2, 2, 0, '2021-02-20 21:09:59', '2021-02-20 21:09:59'),
(3, 2, 3, 1, '2021-02-20 21:09:59', '2021-02-20 21:09:59'),
(4, 3, 4, 0, '2021-02-20 21:10:00', '2021-02-20 21:10:00'),
(5, 4, 5, 0, '2021-02-20 21:10:00', '2021-02-20 21:10:00'),
(6, 5, 6, 0, '2021-02-20 21:10:00', '2021-02-20 21:10:00'),
(7, 5, 7, 1, '2021-02-20 21:10:00', '2021-02-20 21:10:00'),
(8, 4, 8, 1, '2021-02-20 21:10:00', '2021-02-20 21:10:00'),
(9, 3, 9, 1, '2021-02-20 21:10:00', '2021-02-20 21:10:00');");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
		Schema::table('document_translation_orders', function (Blueprint $table) {
			$table->renameColumn('custom_delivery_type_id', 'scanned_delivery_type_id');
			$table->renameColumn('personal_delivery_type_id', 'original_delivery_type_id');
		});
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
		Schema::table('document_translation_orders', function (Blueprint $table) {
			$table->renameColumn('scanned_delivery_type_id', 'custom_delivery_type_id');
			$table->renameColumn('original_delivery_type_id', 'personal_delivery_type_id');
		});
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class PopulateTypeIdOnDocumentDraftsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
	    DB::table('document_drafts')
	      ->join('documents', 'document_drafts.document_id', '=', 'documents.id')
	      ->join('document_templates', 'documents.template_id', '=', 'document_templates.id')
	      ->update(['document_drafts.type_id' => DB::raw('document_templates.type_id')]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}

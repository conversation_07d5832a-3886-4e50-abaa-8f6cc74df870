<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class InsertTemplateNonMatrimonialRealestateTransferConsent extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
	    \DB::insert("
	        INSERT INTO `document_templates` (
	            `title`, 
	            `public_title`, 
	            `tags`, 
	            `time_to_fill`, 
	            `slug`, 
	            `precontract_for_id`, 
	            `type_id`, 
	            `is_visible`, 
	            `created_at`, 
	            `updated_at`
	        ) 
	        VALUES ('NonMatrimonialRealestateTransferConsent', 'Iz<PERSON>va da nekretnina nije bračna stečevina', 'izjava da nekretnina nije bracna stecevina, izjava da nekretnina nije izvanbracna stecevina, izjava da nekretnina nije partnerska stecevina, stečevina, kuća, stan', '5', 'izjava-da-nekretnina-nije-bracna-izvanbracna-partnerska-stecevina', NULL, '2', 1, NOW(), NOW())");
		    
		$result = \DB::select('SELECT MAX(id) as id FROM document_templates;');
		$template_id = intval($result[0]->id);
		
		DB::insert("INSERT INTO `document_categories_templates` (`category_id`, `template_id`, `order_index`, `created_at`, `updated_at`) VALUES ('1', ?, '7', NOW(), NOW())", [$template_id]);
		
		DB::insert("INSERT INTO `document_template_sections` (`template_id`, `title`, `view`, `order_index`, `nav_title`, `created_at`, `updated_at`) VALUES
		(?, 'Stranke', 'parties', '1', 'Stranke', NOW(), NOW())", [$template_id]);
		
		DB::insert("INSERT INTO `document_template_sections` (`template_id`, `title`, `view`, `order_index`, `nav_title`, `created_at`, `updated_at`) VALUES
		(?, 'Nekretnina', 'realestate', '2', 'Nekretnina', NOW(), NOW())", [$template_id]);
		
		DB::insert("INSERT INTO `document_template_sections` (`template_id`, `title`, `view`, `order_index`, `nav_title`, `created_at`, `updated_at`) VALUES
		(?, 'Završne odredbe', 'final_provisions', '3', 'Završne odredbe', NOW(), NOW())", [$template_id]);
		
	}

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
		$template_id = \DB::table('document_templates')->select('id')->where('title', 'NonMatrimonialRealestateTransferConsent')->value('id');
		\DB::table('document_templates')->where('id', $template_id)->delete();
		\DB::table('document_categories_templates')->where('template_id', $template_id)->delete();
		\DB::table('document_template_sections')->where('template_id', $template_id)->delete();
		
	}
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('document_translations', function (Blueprint $table) {
            $table->dropColumn('lang');
        });

        Schema::table('document_translation_orders', function (Blueprint $table) {
            $table->char('lang', 2)->default('en')->after('type_id')->index();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('document_translation_orders', function (Blueprint $table) {
            $table->dropColumn('lang');
        });

        Schema::table('document_translations', function (Blueprint $table) {
            $table->char('lang', 2)->default('en')->after('character_count')->index();
        });

    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('document_translation_orders', function (Blueprint $table) {
            $table->string('shipping_phone')->after('payment_confirmation_url')->nullable();
            $table->string('shipping_country')->after('payment_confirmation_url')->nullable();
            $table->string('shipping_postal_code')->after('payment_confirmation_url')->nullable();
            $table->string('shipping_city')->after('payment_confirmation_url')->nullable();
            $table->string('shipping_address')->after('payment_confirmation_url')->nullable();
            $table->string('shipping_name')->after('payment_confirmation_url')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('document_translation_orders', function (Blueprint $table) {
            //
        });
    }
};

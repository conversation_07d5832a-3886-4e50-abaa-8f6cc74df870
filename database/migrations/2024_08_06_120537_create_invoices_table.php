<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoices', function (Blueprint $table) {
	        $table->id();
	        $table->tinyInteger('status_id')->default(0)->index();
	        $table->string('number');
			$table->string('title');
	        $table->unsignedBigInteger('user_id')->index();
	        $table->unsignedBigInteger('invoiceable_id');
	        $table->string('invoiceable_type');
	        $table->string('customer_type');
	        $table->string('name');
	        $table->string('email');
	        $table->string('address');
	        $table->string('city');
	        $table->string('postal_code');
	        $table->string('country');
	        $table->string('oib')->nullable();
	        $table->string('phone')->nullable();
	        $table->decimal('total', 10, 2)->nullable();
	        $table->decimal('vat')->default(25);
	        $table->decimal('shipping')->nullable();
	        $table->json('items')->nullable();
	        $table->string('pdf')->nullable();
	        $table->timestamps();
			$table->timestamp('sent_at')->nullable();
			$table->timestamp('due_date')->nullable();
	        $table->softDeletes();

	        // polymorphic index
	        $table->index(['invoiceable_id', 'invoiceable_type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoices');
    }
};

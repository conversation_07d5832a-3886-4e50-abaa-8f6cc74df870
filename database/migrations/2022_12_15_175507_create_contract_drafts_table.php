<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateContractDraftsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('contract_drafts', function (Blueprint $table) {
            $table->id();
	        $table->integer('user_id')->index();
	        $table->integer('contract_id')->nullable()->index();
			$table->string('title');
	        $table->longText('html');
	        $table->text('comment')->nullable();
            $table->timestamps();
			$table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('contract_drafts');
    }
}

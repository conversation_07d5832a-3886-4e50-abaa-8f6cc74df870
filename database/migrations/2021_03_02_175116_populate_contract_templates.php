<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class PopulateContractTemplates extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
	    DB::statement("INSERT INTO `contract_templates` (`id`, `title`, `public_title`, `tags`, `time_to_fill`, `slug`, `precontract_for_id`, `version_id`, `is_visible`, `created_at`, `updated_at`, `deleted_at`) VALUES
(1, 'PropertyRentalAgreement', 'Ugovor o najmu stana', 'stan, najam, ugovor o stanu, iznajmljivanje stana', 10, 'ugovor-o-najmu-stana', NULL, 1, 1, '2021-02-20 21:09:58', '2021-02-23 15:25:18', NULL),
(2, 'WorkContract', 'Ugovor o radu', 'ugovor o radu, radni ugovor', 10, 'ugovor-o-radu', NULL, 1, 1, '2021-02-20 21:09:59', '2021-02-20 21:10:00', NULL),
(3, 'WorkContractRemote', 'Ugovor o radu na izdvojenom mjestu rada', 'ugovor o radu, radni ugovor, ugovor o radu na izdvojenom mjestu rada', 10, 'ugovor-o-radu-na-izdvojenom-mjestu-rada', NULL, 1, 1, '2021-02-20 21:09:59', '2021-02-20 21:10:00', NULL),
(4, 'MotorVehicleSalesAgreement', 'Ugovor o kupoprodaji motornog vozila', 'motor, auto, ugovor o kupoprodaji motornog vozila, ugovor o kupoprodaji auta, ugovor o kupoprodaji automobila, ugovor o kupoprodaji motora', 5, 'ugovor-o-kupoprodaji-motornog-vozila', NULL, 1, 1, '2021-02-20 21:10:00', '2021-02-20 21:10:00', NULL),
(5, 'MotorVehicleGiftAgreement', 'Ugovor o darovanju motornog vozila', 'motor, auto, ugovor o darovanju motornog vozila, ugovor o darovanju auta, ugovor o darovanju automobila, ugovor o darovanju motora', 5, 'ugovor-o-darovanju-motornog-vozila', NULL, 1, 1, '2021-02-20 21:10:00', '2021-02-20 21:10:00', NULL),
(6, 'ServiceContract', 'Ugovor o djelu', 'ugovor o djelu, djelo', 5, 'ugovor-o-djelu', NULL, 1, 1, '2021-02-20 21:10:00', '2021-02-20 21:10:00', NULL),
(7, 'AuthorServiceContract', 'Ugovor o stvaranju autorskog djela po narudžbi', 'ugovor o autorskom djelu, autorsko djelo', 5, 'ugovor-o-stvaranju-autorskog-djela-po-narudzbi', NULL, 1, 1, '2021-02-20 21:10:00', '2021-02-20 21:10:00', NULL),
(8, 'RealestateGiftContract', 'Ugovor o darovanju nekretnine', 'ugovor o darovanju nekretnine, darovanje nekretnine', 10, 'ugovor-o-darovanju-nekretnine', NULL, 1, 1, '2021-02-20 21:10:00', '2021-02-20 21:10:00', NULL),
(9, 'RealestateSalesPrecontract', 'Predugovor o kupoprodaji nekretnine', 'predugovor o kupoprodaji nekretnine, kupoprodaja nekretnine', 15, 'predugovor-o-kupoprodaji-nekretnine', NULL, 1, 1, '2021-02-20 21:10:00', '2021-02-23 15:25:18', NULL);");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('document_translation_orders', function (Blueprint $table) {
            $table->dropColumn('shipment_tracking_url');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('document_translation_orders', function (Blueprint $table) {
            $table->string('shipment_tracking_url')->nullable();
        });
    }
};

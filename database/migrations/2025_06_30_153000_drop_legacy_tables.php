<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::dropIfExists('document_categories');
        Schema::dropIfExists('document_categories_templates');
        Schema::dropIfExists('document_content');
        Schema::dropIfExists('document_drafts');
        Schema::dropIfExists('document_parties');
        Schema::dropIfExists('document_template_section_values');
        Schema::dropIfExists('document_template_sections');
        Schema::dropIfExists('document_templates');
        Schema::dropIfExists('documents');
        Schema::dropIfExists('email_preferences');
        Schema::dropIfExists('feedback');
        Schema::dropIfExists('feedback_requests');
        Schema::dropIfExists('invoices');
        Schema::dropIfExists('receipts');
        Schema::dropIfExists('search_logs');
        Schema::dropIfExists('signature_requests');
        Schema::dropIfExists('subscription_items');
        Schema::dropIfExists('subscriptions');
        Schema::dropIfExists('template_pricelist');
        Schema::dropIfExists('translation_documents');
        Schema::dropIfExists('translations');
        Schema::dropIfExists('users');
        Schema::dropIfExists('user_options');
        Schema::dropIfExists('password_resets');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
		Schema::table('document_template_section_values', function (Blueprint $table) {
			$table->mediumText('data')->change();
		});
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
		Schema::table('document_template_section_values', function (Blueprint $table) {
			$table->text('data')->change();
		});
    }
};

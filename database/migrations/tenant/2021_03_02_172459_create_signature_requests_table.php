<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSignatureRequestsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('signature_requests', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('contract_id')->index();
            $table->integer('party_id')->index();
            $table->string('email');
            $table->text('link');
            $table->integer('expiration_days')->default(5);
            $table->tinyInteger('is_signed')->default(0);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('signature_requests');
    }
}

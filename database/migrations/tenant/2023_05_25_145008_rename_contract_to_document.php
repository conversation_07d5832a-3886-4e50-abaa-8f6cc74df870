<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class RenameContractToDocument extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
	    Schema::rename('contracts', 'documents');

	    Schema::table('documents', function (Blueprint $table) {
		    $table->renameColumn('contract_template_id', 'template_id');
	    });

	    Schema::rename('contract_categories', 'document_categories');
	    Schema::rename('contract_categories_templates', 'document_categories_templates');
	    Schema::rename('contract_content', 'document_content');

	    Schema::table('document_content', function (Blueprint $table) {
		    $table->renameColumn('contract_id', 'document_id');
	    });

	    Schema::rename('contract_drafts', 'document_drafts');

	    Schema::table('document_drafts', function (Blueprint $table) {
		    $table->renameColumn('contract_id', 'document_id');
	    });

	    Schema::rename('contract_parties', 'document_parties');

	    Schema::table('document_parties', function (Blueprint $table) {
		    $table->renameColumn('contract_id', 'document_id');
	    });

	    Schema::rename('contract_templates', 'document_templates');
	    Schema::rename('contract_template_sections', 'document_template_sections');

	    Schema::table('document_template_sections', function (Blueprint $table) {
		    $table->renameColumn('contract_template_id', 'template_id');
	    });

	    Schema::rename('contract_template_section_values', 'document_template_section_values');

	    Schema::table('document_template_section_values', function (Blueprint $table) {
		    $table->renameColumn('contract_id', 'document_id');
		    $table->renameColumn('contract_template_section_id', 'document_template_section_id');
	    });

	    Schema::rename('contract_transactions', 'document_transactions');

	    Schema::table('document_transactions', function (Blueprint $table) {
		    $table->renameColumn('contract_id', 'document_id');
	    });

	    Schema::rename('contract_transaction_receipts', 'document_transaction_receipts');

	    Schema::table('document_transaction_receipts', function (Blueprint $table) {
		    $table->renameColumn('contract_transaction_id', 'document_transaction_id');
	    });

	    Schema::table('signature_requests', function (Blueprint $table) {
		    $table->renameColumn('contract_id', 'document_id');
	    });

	    Schema::table('user_options', function (Blueprint $table) {
		    $table->renameColumn('is_contract_tutorial_shown', 'is_document_tutorial_shown');
	    });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
	    Schema::rename('documents', 'contracts');

	    Schema::table('contracts', function (Blueprint $table) {
		    $table->renameColumn('template_id', 'contract_template_id');
	    });

	    Schema::rename('document_categories', 'contract_categories');
	    Schema::rename('document_categories_templates', 'contract_categories_templates');
	    Schema::rename('document_content', 'contract_content');

	    Schema::table('contract_content', function (Blueprint $table) {
		    $table->renameColumn('document_id', 'contract_id');
	    });

	    Schema::rename('document_drafts', 'contract_drafts');

	    Schema::table('contract_drafts', function (Blueprint $table) {
		    $table->renameColumn('document_id', 'contract_id');
	    });

	    Schema::rename('document_parties', 'contract_parties');

	    Schema::table('contract_parties', function (Blueprint $table) {
		    $table->renameColumn('document_id', 'contract_id');
	    });

	    Schema::rename('document_templates', 'contract_templates');
	    Schema::rename('document_template_sections', 'contract_template_sections');

	    Schema::table('contract_template_sections', function (Blueprint $table) {
		    $table->renameColumn('template_id', 'contract_template_id');
	    });

	    Schema::rename('document_template_section_values', 'contract_template_section_values');

	    Schema::table('contract_template_section_values', function (Blueprint $table) {
		    $table->renameColumn('document_id', 'contract_id');
		    $table->renameColumn('document_template_section_id', 'contract_template_section_id');
	    });

	    Schema::rename('document_transactions', 'contract_transactions');

	    Schema::table('contract_transactions', function (Blueprint $table) {
		    $table->renameColumn('document_id', 'contract_id');
	    });

	    Schema::rename('document_transaction_receipts', 'contract_transaction_receipts');

	    Schema::table('contract_transaction_receipts', function (Blueprint $table) {
		    $table->renameColumn('document_transaction_id', 'contract_transaction_id');
	    });

	    Schema::table('signature_requests', function (Blueprint $table) {
		    $table->renameColumn('document_id', 'contract_id');
	    });

	    Schema::table('user_options', function (Blueprint $table) {
		    $table->renameColumn('is_document_tutorial_shown', 'is_contract_tutorial_shown');
	    });
    }
}

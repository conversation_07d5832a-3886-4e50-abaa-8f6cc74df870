<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class RenameRealestateSpouseTransferConsentTemplate extends Migration
{
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		DB::table('document_templates')
		  ->where('title', 'RealestateSpouseTransferConsent')
		  ->update(['title' => 'MatrimonialRealestateTransferConsent']);
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		DB::table('document_templates')
		  ->where('title', 'MatrimonialRealestateTransferConsent')
		  ->update(['title' => 'RealestateSpouseTransferConsent']);
	}
}

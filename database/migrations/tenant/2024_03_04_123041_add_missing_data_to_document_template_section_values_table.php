<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('document_template_section_values', function (Blueprint $table) {
            $table->text('missing_data')->nullable()->default(null)->after('data');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('document_template_section_values', function (Blueprint $table) {
            $table->dropColumn('missing_data');
        });
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddIsVisibleToDocumentDraftsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('document_drafts', function (Blueprint $table) {
            $table->boolean('is_visible')->default(false)->after('is_dirty')->index();
        });

	    \Illuminate\Support\Facades\DB::table('document_drafts')->whereNull('deleted_at')->update(['is_visible' => true]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('document_drafts', function (Blueprint $table) {
            $table->dropColumn('is_visible');
        });
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class RenameContractToDocumentIndexes extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
		// Retrieve all table names
	    $connection = Schema::getConnection();
	    $tableNames = $connection->getDoctrineSchemaManager()->listTableNames();

	    foreach ($tableNames as $tableName) {
		    // Retrieve existing index names for the table
		    $indexNames = $connection->getDoctrineSchemaManager()->listTableIndexes($tableName);

		    foreach ($indexNames as $indexName => $index) {
			    if (strpos($indexName, 'contract') !== false) {
				    // Replace the substring 'contract' with 'document'
				    $newIndexName = str_replace('contract', 'document', $indexName);

					$columns = $index->getColumns();

				    Schema::table($tableName, function (Blueprint $table) use ($indexName, $newIndexName, $columns, $tableName) {
					    DB::statement("ALTER TABLE `$tableName` DROP INDEX `$indexName`");
					    $table->index($columns, $newIndexName);
				    });
			    }
		    }
	    }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
		// Retrieve all table names
	    $connection = Schema::getConnection();
	    $tableNames = $connection->getDoctrineSchemaManager()->listTableNames();

	    foreach ($tableNames as $tableName) {
		    // Retrieve existing index names for the table
		    $indexNames = $connection->getDoctrineSchemaManager()->listTableIndexes($tableName);

		    foreach ($indexNames as $indexName => $index) {
			    if (strpos($indexName, 'document') !== false) {
				    // Replace the substring 'contract' with 'document'
				    $newIndexName = str_replace('document', 'contract', $indexName);

				    $columns = $index->getColumns();

				    Schema::table($tableName, function (Blueprint $table) use ($indexName, $newIndexName, $columns, $tableName) {
					    DB::statement("ALTER TABLE `$tableName` DROP INDEX `$indexName`");
					    $table->index($columns, $newIndexName);
				    });
			    }
		    }
	    }
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateContractsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('contracts', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('contract_template_id')->index();
            $table->integer('user_id')->nullable()->index();
            $table->string('guest_id')->nullable()->index();
            $table->string('title')->nullable();
            $table->timestamps();
            $table->softDeletes();
            $table->tinyInteger('is_visible')->default(0)->index();
            $table->integer('version_id')->nullable()->index();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('contracts');
    }
}

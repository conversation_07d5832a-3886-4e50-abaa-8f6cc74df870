<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('user_options', function (Blueprint $table) {
	        $table->boolean('is_wizard_tutorial_shown')->default(false)->after('is_admin');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('user_options', function (Blueprint $table) {
	        $table->dropColumn('is_wizard_tutorial_shown');
        });
    }
};

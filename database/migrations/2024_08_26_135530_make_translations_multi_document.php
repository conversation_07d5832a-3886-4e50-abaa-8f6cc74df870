<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('document_translation_orders', function (Blueprint $table) {
            $table->dropColumn('translation_id');
        });

        Schema::table('document_translations', function (Blueprint $table) {
            $table->integer('order_id')->after('id')->index();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('document_translations', function (Blueprint $table) {
            $table->dropColumn('order_id');
        });

        Schema::table('document_translation_orders', function (Blueprint $table) {
            $table->integer('translation_id')->after('id')->index();
        });
    }
};

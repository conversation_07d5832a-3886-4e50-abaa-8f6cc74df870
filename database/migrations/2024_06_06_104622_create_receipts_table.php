<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('receipts', function (Blueprint $table) {
	        $table->id();
	        $table->unsignedBigInteger('user_id')->index();
	        $table->unsignedBigInteger('receiptable_id');
	        $table->string('receiptable_type');
	        $table->string('customer_type');
	        $table->string('name');
	        $table->string('email');
	        $table->string('address');
	        $table->string('city');
	        $table->string('postal_code');
	        $table->string('country');
	        $table->string('oib')->nullable();
	        $table->string('phone')->nullable();
	        $table->string('zki')->nullable();
	        $table->string('jir')->nullable();
			$table->decimal('total', 10, 2);
			$table->json('items');
	        $table->timestamps();
	        $table->softDeletes();

	        // polymorphic index
	        $table->index(['receiptable_id', 'receiptable_type']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('receipts');
    }
};

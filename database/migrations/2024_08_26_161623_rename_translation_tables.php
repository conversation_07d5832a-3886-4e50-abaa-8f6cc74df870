<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::rename('document_translation_orders', 'translations');
        Schema::rename('document_translations', 'translation_documents');

        Schema::table('translation_documents', function (Blueprint $table) {
            $table->renameColumn('order_id', 'translation_id');
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::rename('translations', 'document_translation_orders');
        Schema::rename('translation_documents', 'document_translations');

        Schema::table('document_translations', function (Blueprint $table) {
            $table->renameColumn('translation_id', 'order_id');
        });
    }
};

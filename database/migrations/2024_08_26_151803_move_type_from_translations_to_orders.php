<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('document_translations', function (Blueprint $table) {
            $table->dropColumn('type_id');
        });

        Schema::table('document_translation_orders', function (Blueprint $table) {
            $table->tinyInteger('source_id')->after('id')->nullable()->index();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('document_translation_orders', function (Blueprint $table) {
            $table->dropColumn('source_id');
        });

        Schema::table('document_translations', function (Blueprint $table) {
            $table->tinyInteger('type_id')->after('order_id')->nullable()->index();
        });

    }
};

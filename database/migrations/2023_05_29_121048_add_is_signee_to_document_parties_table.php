<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddIsSigneeToDocumentPartiesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('document_parties', function (Blueprint $table) {
            $table->boolean('is_signee')->default(true)->after('side')->index();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('document_parties', function (Blueprint $table) {
            $table->dropColumn('is_signee');
        });
    }
}

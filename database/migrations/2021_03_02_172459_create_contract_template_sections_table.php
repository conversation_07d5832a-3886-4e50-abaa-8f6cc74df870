<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateContractTemplateSectionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('contract_template_sections', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('contract_template_id')->index();
            $table->string('title');
            $table->text('description')->nullable();
            $table->string('view');
            $table->integer('order_index')->index();
            $table->string('nav_title', 128);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('contract_template_sections');
    }
}

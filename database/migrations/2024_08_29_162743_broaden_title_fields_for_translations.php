<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // change title from string to text
        Schema::table('invoices', function (Blueprint $table) {
            $table->text('title')->change();
        });

        Schema::table('receipts', function (Blueprint $table) {
            $table->text('title')->change();
        });

        Schema::table('translations', function (Blueprint $table) {
            $table->text('title')->change();
        });

        Schema::table('translation_documents', function (Blueprint $table) {
            $table->text('title')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};

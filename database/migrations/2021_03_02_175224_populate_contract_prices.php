<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class PopulateContractPrices extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::statement("INSERT INTO `contract_prices` (`id`, `contract_template_id`, `price`, `created_at`, `updated_at`, `deleted_at`) VALUES
(1, 1, 20.00, '2021-02-20 21:09:58', '2021-02-20 21:09:58', NULL),
(2, 2, 20.00, '2021-02-20 21:09:59', '2021-02-20 21:09:59', NULL),
(3, 4, 20.00, '2021-02-20 21:10:00', '2021-02-20 21:10:00', NULL),
(4, 5, 20.00, '2021-02-20 21:10:00', '2021-02-20 21:10:00', NULL),
(5, 6, 20.00, '2021-02-20 21:10:00', '2021-02-20 21:10:00', NULL),
(6, 7, 20.00, '2021-02-20 21:10:00', '2021-02-20 21:10:00', NULL),
(7, 8, 20.00, '2021-02-20 21:10:00', '2021-02-20 21:10:00', NULL),
(8, 9, 20.00, '2021-02-20 21:10:00', '2021-02-20 21:10:00', NULL),
(9, 10, 20.00, '2021-02-20 21:10:01', '2021-02-20 21:10:01', NULL);");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('document_translation_orders', function (Blueprint $table) {
            $table->tinyInteger('payment_status_id')->nullable()->after('status_id')->index();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('document_translation_orders', function (Blueprint $table) {
            $table->dropColumn('payment_status_id');
        });
    }
};

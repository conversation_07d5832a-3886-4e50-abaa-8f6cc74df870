<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateContractTransactionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('contract_transactions', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('contract_id')->index();
            $table->tinyInteger('payment_type');
            $table->tinyInteger('status')->index();
            $table->double('tax_rate', 8, 2);
            $table->double('total_amount', 8, 2);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('contract_transactions');
    }
}

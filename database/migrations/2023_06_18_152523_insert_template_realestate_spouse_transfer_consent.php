<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class InsertTemplateRealestateSpouseTransferConsent extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
	    \DB::insert("
	        INSERT INTO `document_templates` (
	            `title`, 
	            `public_title`, 
	            `tags`, 
	            `time_to_fill`, 
	            `slug`, 
	            `precontract_for_id`, 
	            `type_id`, 
	            `is_visible`, 
	            `created_at`, 
	            `updated_at`
	        ) 
	        VALUES ('RealestateSpouseTransferConsent', 'Suglasnost za otuđenje i opterećenje nekretnine', 'suglasnost za otudeđenje i opterećenje nekretnine, otuđenje stana, opterećenje stana, otuđenje nkuće, otuđenje kuće, suglasnost', '5', 'suglasnost-za-otudenje-i-opterecenje-nekretnine-koja-je-bracna-izvanbracna-partnerska-stecevina', NULL, '2', 1, NOW(), NOW())");
		    
		$result = \DB::select('SELECT MAX(id) as id FROM document_templates;');
		$template_id = intval($result[0]->id);
		
		DB::insert("INSERT INTO `document_categories_templates` (`category_id`, `template_id`, `order_index`, `created_at`, `updated_at`) VALUES ('1', ?, '6', NOW(), NOW())", [$template_id]);
		
		DB::insert("INSERT INTO `document_template_sections` (`template_id`, `title`, `view`, `order_index`, `nav_title`, `created_at`, `updated_at`) VALUES
		(?, 'Stranke', 'parties', '1', 'Stranke', NOW(), NOW())", [$template_id]);
		
		DB::insert("INSERT INTO `document_template_sections` (`template_id`, `title`, `view`, `order_index`, `nav_title`, `created_at`, `updated_at`) VALUES
		(?, 'Nekretnina', 'realestate', '2', 'Nekretnina', NOW(), NOW())", [$template_id]);
		
		DB::insert("INSERT INTO `document_template_sections` (`template_id`, `title`, `view`, `order_index`, `nav_title`, `created_at`, `updated_at`) VALUES
		(?, 'Poslovi', 'authorities', '3', 'Poslovi', NOW(), NOW())", [$template_id]);
		
		DB::insert("INSERT INTO `document_template_sections` (`template_id`, `title`, `view`, `order_index`, `nav_title`, `created_at`, `updated_at`) VALUES
		(?, 'Važenje suglasnosti', 'validity', '4', 'Važenje suglasnosti', NOW(), NOW())", [$template_id]);
		
		DB::insert("INSERT INTO `document_template_sections` (`template_id`, `title`, `view`, `order_index`, `nav_title`, `created_at`, `updated_at`) VALUES
		(?, 'Završne odredbe', 'final_provisions', '5', 'Završne odredbe', NOW(), NOW())", [$template_id]);
		
	}

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
		$template_id = \DB::table('document_templates')->select('id')->where('title', 'RealestateSpouseTransferConsent')->value('id');
		\DB::table('document_templates')->where('id', $template_id)->delete();
		\DB::table('document_categories_templates')->where('template_id', $template_id)->delete();
		\DB::table('document_template_sections')->where('template_id', $template_id)->delete();
		
	}
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('document_translation_orders', function (Blueprint $table) {
            $table->string('shipment_tracking_url')->after('stripe_id')->nullable();
            $table->string('shipment_tracking_code')->after('stripe_id')->nullable();
            $table->string('payment_confirmation_url')->after('stripe_id')->nullable();
            $table->timestamp('deadline')->after('stripe_id')->nullable();
            $table->string('eta')->after('stripe_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('document_translation_orders', function (Blueprint $table) {
            $table->dropColumn('payment_confirmation_url');
            $table->dropColumn('shipment_tracking_url');
            $table->dropColumn('deadline');
            $table->dropColumn('eta');
        });
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('document_translation_orders', function (Blueprint $table) {
	        $table->id();
	        $table->unsignedBigInteger('translation_id')->index();
	        $table->unsignedBigInteger('status_id')->default(1)->index();
	        $table->string('stripe_id')->nullable()->index();
	        $table->timestamps();
	        $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('document_translation_orders');
    }
};

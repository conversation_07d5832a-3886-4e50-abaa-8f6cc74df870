<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class PopulateContractCategories extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
	    DB::statement("INSERT INTO `contract_categories` (`id`, `title`, `order_index`, `created_at`, `updated_at`) VALUES
(1, 'Ugovori o najmu', 0, '2021-02-20 21:09:59', '2021-02-20 21:09:59'),
(2, 'Ugovori o radu', 1, '2021-02-20 21:09:59', '2021-02-20 21:09:59'),
(3, 'Ugovori o kupoprodaji', 2, '2021-02-20 21:10:00', '2021-02-20 21:10:00'),
(4, 'Ugovori o darovanju', 3, '2021-02-20 21:10:00', '2021-02-20 21:10:00'),
(5, 'Ugo<PERSON>i o djelu i autorskom djelu', 4, '2021-02-20 21:10:00', '2021-02-20 21:10:00');");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}

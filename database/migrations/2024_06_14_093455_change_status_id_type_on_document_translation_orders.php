<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('document_translation_orders', function (Blueprint $table) {
            $table->dropColumn('status_id');
        });

        Schema::table('document_translation_orders', function (Blueprint $table) {
            $table->tinyInteger('status_id')->after('id')->default(1)->index();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('document_translation_orders', function (Blueprint $table) {
            $table->dropColumn('status_id');
        });

        Schema::table('document_translation_orders', function (Blueprint $table) {
            $table->integer('status_id')->after('id')->default(1)->index();
        });
    }
};

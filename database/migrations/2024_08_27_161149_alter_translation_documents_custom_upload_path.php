<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('translation_documents', function (Blueprint $table) {
            $table->string('output_path')->after('custom_upload_path')->nullable();
            $table->renameColumn('custom_upload_path', 'input_path');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('translation_documents', function (Blueprint $table) {
            $table->dropColumn('output_path');
            $table->renameColumn('input_path', 'custom_upload_path');
        });
    }
};

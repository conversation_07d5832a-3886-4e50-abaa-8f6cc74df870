<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('document_translations', function (Blueprint $table) {
            $table->id();
			$table->integer('user_id')->index();
	        $table->tinyInteger('status_id')->default(1)->index();
	        $table->tinyInteger('type_id')->nullable()->index();
	        $table->string('document_title');
	        $table->integer('character_count')->nullable();
	        $table->char('lang', 2)->default('en')->index();
            $table->timestamps();
			$table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('document_translations');
    }
};

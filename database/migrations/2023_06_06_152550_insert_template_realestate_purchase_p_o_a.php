<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class InsertTemplateRealestatePurchasePOA extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
	    \DB::insert("
	        INSERT INTO `document_templates` (
	            `title`, 
	            `public_title`, 
	            `tags`, 
	            `time_to_fill`, 
	            `slug`, 
	            `precontract_for_id`, 
	            `type_id`, 
	            `is_visible`, 
	            `created_at`, 
	            `updated_at`
	        ) 
	        VALUES ('RealestatePurchasePOA', 'Punomoć za kupnju nekretnine', 'punomoć, punomoć za kupnju nekretnine, punomoć za kupovinu nekretnine, punomoć za kupnju stana, punomoć za kupovinu stana, puno<PERSON>ć za kupnju kuće, punomoć za kupovinu kuće, kupoprodaja nekretnine, kupoprodaja stana, kupoprodaja kuće', '5', 'punomoc-za-kupnju-nekretnine', NULL, '2', 1, NOW(), NOW())");
		    
		$result = \DB::select('SELECT MAX(id) as id FROM document_templates;');
		$template_id = intval($result[0]->id);
		
		DB::insert("INSERT INTO `document_categories_templates` (`category_id`, `template_id`, `order_index`, `created_at`, `updated_at`) VALUES ('3', ?, '3', NOW(), NOW())", [$template_id]);
		
		DB::insert("INSERT INTO `document_template_sections` (`template_id`, `title`, `view`, `order_index`, `nav_title`, `created_at`, `updated_at`) VALUES
		(?, 'Stranke', 'parties', '1', 'Stranke', NOW(), NOW())", [$template_id]);
		
		DB::insert("INSERT INTO `document_template_sections` (`template_id`, `title`, `view`, `order_index`, `nav_title`, `created_at`, `updated_at`) VALUES
		(?, 'Nekretnina', 'realestate', '2', 'Nekretnina', NOW(), NOW())", [$template_id]);
		
		DB::insert("INSERT INTO `document_template_sections` (`template_id`, `title`, `view`, `order_index`, `nav_title`, `created_at`, `updated_at`) VALUES
		(?, 'Ovlaštenja', 'authorities', '3', 'Ovlaštenja', NOW(), NOW())", [$template_id]);
		
		DB::insert("INSERT INTO `document_template_sections` (`template_id`, `title`, `view`, `order_index`, `nav_title`, `created_at`, `updated_at`) VALUES
		(?, 'Važenje punomoći', 'validity', '4', 'Važenje punomoći', NOW(), NOW())", [$template_id]);
		
		DB::insert("INSERT INTO `document_template_sections` (`template_id`, `title`, `view`, `order_index`, `nav_title`, `created_at`, `updated_at`) VALUES
		(?, 'Završne odredbe', 'final_provisions', '5', 'Završne odredbe', NOW(), NOW())", [$template_id]);
		
	}

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
		$template_id = \DB::table('document_templates')->select('id')->where('title', 'RealestatePurchasePOA')->value('id');
		\DB::table('document_templates')->where('id', $template_id)->delete();
		\DB::table('document_categories_templates')->where('template_id', $template_id)->delete();
		\DB::table('document_template_sections')->where('template_id', $template_id)->delete();
		
	}
}

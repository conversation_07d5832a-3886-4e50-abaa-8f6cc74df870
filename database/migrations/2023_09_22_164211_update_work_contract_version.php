<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
	    DB::table('document_templates')
	      ->where('title', 'WorkContract')
	      ->increment('version_id', 1);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
	    DB::table('document_templates')
	      ->where('title', 'WorkContract')
	      ->decrement('version_id', 1);
    }
};

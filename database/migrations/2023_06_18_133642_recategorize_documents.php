<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class RecategorizeDocuments extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
		// truncate all
	    DB::table('document_categories')->truncate();
	    DB::table('document_categories_templates')->truncate();

	    DB::insert("INSERT INTO `document_categories` (`title`, `order_index`, `created_at`, `updated_at`) VALUES
		('Nekretnine', 1, NOW(), NOW()),
		('Pokretnine', 2, NOW(), NOW()),
		('Radni odnosi', 3, NOW(), NOW()),
		('Obavljanje poslova izvan radnog odnosa', 4, NOW(), NOW())");

		$documents = [
			'Nekretnine' => [
				'RealestateGiftContract',
				'RealestateSalesPrecontract',
				'RealestateSalesPOA',
				'RealestatePurchasePOA',
				'PropertyRentalAgreement'
			],
			'Pokretnine' => [
				'MotorVehicleGiftAgreement',
				'MotorVehicleSalesAgreement',
			],
			'Radni odnosi' => [
				'WorkContract',
				'WorkContractRemote'
			],
			'Obavljanje poslova izvan radnog odnosa' => [
				'ServiceContract',
				'AuthorServiceContract'
			],
		];

		foreach($documents as $_category => $_templates) {
			$_category_id = DB::table('document_categories')->where('title', $_category)->value('id');
			foreach($_templates as $_order_index => $_template) {
				$_template_id = DB::table('document_templates')->where('title', $_template)->value('id');
				DB::insert('INSERT INTO document_categories_templates (category_id, template_id, order_index, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())', [
					$_category_id, $_template_id, ($_order_index + 1)
				]);
			}
		}

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}

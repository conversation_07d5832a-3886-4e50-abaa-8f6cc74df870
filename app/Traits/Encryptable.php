<?php
/**
 * Created by PhpStorm.
 * User: dinocagalj
 * Date: 2020-09-01
 * Time: 09:40
 */

namespace App\Traits;

use Illuminate\Support\Facades\Crypt;

trait Encryptable {

	public function getAttribute($key) {

		$value = parent::getAttribute($key);

		if (!empty($value) && in_array($key, $this->encryptable)) {
			$value = Crypt::decrypt($value);
		}

		return $value;
	}

	public function setAttribute($key, $value) {

		if (!empty($value) && in_array($key, $this->encryptable)) {
			$value = Crypt::encrypt($value);
		}

		return parent::setAttribute($key, $value);
	}
}

<?php

namespace App\Providers;

use App\Listeners\EventListener;
use App\Models\Document;
use App\Models\DocumentDraft;
use App\Models\DocumentTemplate;
use App\Models\DocumentTemplateSectionValue;
use App\Models\EmailPreference;
use App\Models\SignatureRequest;
use App\Models\User;
use App\Observers\DocumentDraftObserver;
use App\Observers\DocumentObserver;
use App\Observers\DocumentTemplateObserver;
use App\Observers\DocumentTemplateSectionValueObserver;
use App\Observers\EmailPreferenceObserver;
use App\Observers\SignatureRequestObserver;
use App\Observers\UserObserver;
use Illuminate\Auth\Events\Login;
use Illuminate\Auth\Events\Registered;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array
     */
    protected $listen = [
	    Event::class => [
		    EventListener::class
	    ],
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot()
    {
        parent::boot();

		User::observe(UserObserver::class);
	    Document::observe(DocumentObserver::class);
	    DocumentDraft::observe(DocumentDraftObserver::class);
        DocumentTemplate::observe(DocumentTemplateObserver::class);
		SignatureRequest::observe(SignatureRequestObserver::class);
        EmailPreference::observe(EmailPreferenceObserver::class);
		DocumentTemplateSectionValue::observe(DocumentTemplateSectionValueObserver::class);

		$this->registerEventLoggers();
    }

	/**
	 * Register event activity logging
	 * @return void
	 */
	private function registerEventLoggers(): void {

		Event::listen(Login::class, function($event) {
			activity()->causedBy($event->user)->withProperties([
				'ip' => request()->ip(),
				'agent' => request()->userAgent(),
			])->log('Login');
		});

		Event::listen(Registered::class, function($event) {
			activity()->withProperties([
				'ip' => request()->ip(),
				'agent' => request()->userAgent()
			])->causedBy($event->user)->log('Registration');
		});

	}
}

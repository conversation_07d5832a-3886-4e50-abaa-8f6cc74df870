<?php

namespace App\Providers;

use App\Html\FormBuilder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Str;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        if(!app()->isProduction()) {
            Model::preventLazyLoading();
        }

        $this->assignGuestId();
        $this->registerTelescope();
        $this->registerFormBuilder();

	    Paginator::useBootstrap();
    }

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    private function registerFormBuilder() {
        $this->app->singleton('form', function ($app) {
            return new FormBuilder();
        });
    }

    // assign guest id to every guest visitor
    private function assignGuestId()
    {
        if(!Auth::check())
        {
            if(!Cookie::get('guest_id')){
	            Cookie::queue('guest_id', Str::random(32));
            }
        }
    }

    private function registerTelescope()
    {
        $this->app->register(TelescopeServiceProvider::class);
    }
}



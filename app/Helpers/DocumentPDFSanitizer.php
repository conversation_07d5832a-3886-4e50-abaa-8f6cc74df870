<?php

namespace App\Helpers;

use Illuminate\Support\Str;
use voku\helper\HtmlDomParser;

class DocumentPDFSanitizer {

	/**
	 * Sanitize the given HTML string.
	 *
	 * @param string $html The HTML string to sanitize.
	 *
	 * @return array|string|null The sanitized HTML string.
	 */
	public static function sanitize(string $html): array|string|null {
		$html = self::removeBadCharacters($html);
		$html = self::removeColors($html);
		$html = self::removeUnsupportedSpanClasses($html);
		return $html;
	}

	/**
	 * Remove color attribute from CSS
	 *
	 * @param string $html
	 *
	 * @return array|string|string[]|null
	 */
	private static function removeColors(string $html): array|string|null {
		return preg_replace_callback(
			"/style=\"([^\"]*)\"|style='([^']*)'/",
			function ($matches) {
				// check which match exists and assign it to $style
				$style = $matches[1] ?? ($matches[2] ?? '');
				$styles = explode(';', $style);

				foreach ($styles as $key => $value) {
					// remove styles containing 'color:' but not 'background-color:'
					if (str_contains($value, 'color:') && ! str_contains($value, 'background-color:')) {
						unset($styles[$key]);
					}
				}

				// rebuild the style attribute
				$style = implode(';', $styles);
				return "style=\"$style\"";
			},
			$html
		);
	}

	/**
	 * Remove bad characters such as carriage return
	 *
	 * @param string $html
	 * @return string
	 */
	private static function removeBadCharacters(string $html): string {
		return Str::replace(["\r\n"], "\n", $html);
	}

	/**
	 * Remove unsupported span classes (mPDF has an issue with these, for example span class: xdj266r x11i5rnm xat24cr...)
	 *
	 * @param string $html
	 * @return string
	 */
	private static function removeUnsupportedSpanClasses(string $html): string {
		// Regex pattern to match span class attributes with "x[random]" style classes
		return preg_replace_callback(
			'/(<span\b[^>]*\bclass\s*=\s*["\'])([^"\']*)(["\'])/i',
			function ($matches) {
				// Split the class attribute value into an array of classes.
				$classes = preg_split('/\s+/', trim($matches[2]));
				// Filter out classes that start with "x"
				$filtered = array_filter($classes, function($cls) {
					return !preg_match('/^x/', $cls);
				});
				// Reassemble the remaining classes back into a string.
				$newClasses = implode(' ', $filtered);
				return $matches[1] . $newClasses . $matches[3];
			},
			$html
		);
	}

	/**
	 *  Modify HTML string by wrapping the header and first paragraph (or div with class "stick-to-header") of an article in a container with the
	 * 'avoid-page-break' class, and then wrapping the rest of the article body in a separate container with the
	 * 'modified-article-body' class. If the original article header or article body elements had the 'segment-dirty'
	 * class, it adds the 'segment-dirty' class to the 'editable-segment' element.
	 *
	 * @param string $html The HTML string to be modified
	 * @return string The modified HTML string
	 */
	public static function avoidPageBreakAfterArticleHeader(string $html, int $maxLength = 1000): string {

		$pbClass = 'avoid-page-break';    // css class to avoid page breaks
		$segmentDirtyClass = 'segment-dirty';    // css class to highlight dirty segments

		$dom = HtmlDomParser::str_get_html($html);

		foreach ($dom->findMulti('.editable-segment[data-type="article"]') as $_article) {

			$_article_dom = $_article->getHtmlDomParser();

			// get article header
			$_article_header = $_article_dom->findOne('.article-header');
			$_article_header_dirty = Str::contains($_article_header->parentNode()->getAttribute('class'), $segmentDirtyClass);
			$_article_header_html = $_article_header->html();

			// get stick-to-header div or first paragraph of article body
			$stick_to_header_div = $_article_dom->findOne('.stick-to-header');
			$_stick_to_header_div_or_first_paragraph = !empty($stick_to_header_div->html()) ? $stick_to_header_div : $_article_dom->findOne('.article-body p');
			$_stick_to_header_div_or_first_paragraph_html = $_stick_to_header_div_or_first_paragraph->html();

			$is_stick_to_header_div = !empty($stick_to_header_div->html());

			// max length rule for paragraph
			if(!$is_stick_to_header_div && strlen($_stick_to_header_div_or_first_paragraph_html) > $maxLength) {
				continue;
			}

			// if both contain html content
			if(!empty($_article_header_html) && !empty($_stick_to_header_div_or_first_paragraph_html)) {

				// delete original article header and first body paragraph or stick-to-header div
				$_article_header->delete();
				$_stick_to_header_div_or_first_paragraph->delete();

				// then, queue article body
				$_article_body = $_article_dom->findOne('.article-body');
				$_article_body_dirty = Str::contains($_article_body->parentNode()->getAttribute('class'), $segmentDirtyClass);

				// filter out empty text nodes from body
				$_article_body_elements = collect($_article_body->childNodes())->filter(function($node){
					return !empty($node->html());
				});

				// if article body begins with p, add mt-0 class to it (to prevent spacing between paragraphs of first and second container)
				if($_article_body_elements->count() && $_article_body_elements->first()->nodeName == "p") {
					$_article_body_p = $_article_body_elements->first();

					if(!Str::contains($_article_body_p->getAttribute('class'), 'mt-0')) {
						$_article_body_p->setAttribute('class', $_article_body_p->getAttribute('class') . ' mt-0');
					}
				}

				// group article header and stick-to-header div or first paragraph
				$_avoid_page_break_wrapper = '<div class="'.$pbClass.'">'.$_article_header_html.$_stick_to_header_div_or_first_paragraph_html.'</div>';

				// wrap newly modified article body into a separate container
				$_article_body_html = $_article_body->innerHtml();
				$_modified_article_body = !empty($_article_body_html) ? '<div class="modified-article-body">'.$_article_body_html.'</div>' : '';

				// finally, set new article html
				$_article->innerhtml = "$_avoid_page_break_wrapper $_modified_article_body";

				// if header or body dirty, add dirty class to the whole article container
				if($_article_header_dirty || $_article_body_dirty) {
					if(!Str::contains($_article->getAttribute('class'), $segmentDirtyClass)) {
						$_article->setAttribute('class', $_article->getAttribute('class') . " $segmentDirtyClass");
					}
				}
			}
		}

		return $dom->save();
	}

}
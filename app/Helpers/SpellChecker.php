<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Http;
use Exception;

/**
 * SpellChecker class for analyzing and fixing spelling errors.
 *
 * Utilizes the ispravi.me API for spell checking.
 */
class SpellChecker
{
	// Constants for the application and API URL
	const APP_NAME = "Legaldesk";
	const API_URL = 'https://ispravi.me/api/ispravi';

	/**
	 * Analyzes the given text for spelling errors.
	 *
	 * @param string $text The text to be analyzed.
	 * @param bool $context Use the context analysis?
	 * @param bool $punctuation Use the punctuation analysis?
	 * @return array The result of the spell check.
	 */
	public static function analyze(string $text, bool $context = true, bool $punctuation = true): array
	{
		try {
			$response = Http::asForm()->post(self::API_URL, [
				'app' => self::APP_NAME,
				'text' => $text,
				'context' => $context ? 'on' : 'off',
				'punctuation' => $punctuation ? 'on' : 'off',
			]);

			if ($response->successful()) {
				return [
					'success' => true,
					'message' => 'Spellcheck completed successfully.',
					'data' => $response->json(),
				];
			} else {
				return [
					'success' => false,
					'message' => 'Failed to complete spellcheck.',
					'error' => $response->body(),
					'status' => $response->status(),
				];
			}
		} catch (Exception $e) {
			return [
				'success' => false,
				'message' => 'An error occurred during the spellcheck process.',
				'error' => $e->getMessage(),
			];
		}
	}

	/**
	 * Fixes spelling errors in the provided content based on the analysis result.
	 * This function applies corrections to the original content using the spell check result,
	 * creating a new version of the content with highlighted errors and suggestions for correction.
	 * It returns an array with details about the adjustments made, errors found, and the corrected content.
	 *
	 * @param string $content The original content.
	 * @param string $text The text that was analyzed.
	 * @param array $result The result from the "analyze" function.
	 *
	 * @return array The result of applying spelling corrections.
	 * @throws Exception
	 */
	public static function fix(string $content, string $text, array $result): array
	{
		if (!$result['success']) {
			return ['adjustments' => false, 'errors' => false, 'content' => $content];
		}

		$response = $result['data']['response'];
		if (empty($response['errors'])) {
			return ['adjustments' => false, 'errors' => false, 'content' => $content];
		}

		$adjustments = [];
		foreach ($response['error'] as $_error) {

			$_error_replacement = self::replaceMisspelledTextWithSpan(
				$_error['suspicious'], $_error['header'], $_error['message'], $_error['suggestions']
			);

			foreach ($_error['position'] as $_pos) {
				$_is_spacing_error = mb_stripos($_error['message'], 'razmak', 0, 'UTF-8') !== false;
				if ($_adjusted_position = self::calculateHtmlPosition($text, $content, $_error['suspicious'], $_pos, $_is_spacing_error)) {
					$adjustments = self::addAdjustment($adjustments, $_adjusted_position, $_error, $_error_replacement);
				}
			}
		}

		// apply adjustments in reverse order to not disrupt the positions
		usort($adjustments, function ($a, $b) {
			return $b['position'] <=> $a['position'];
		});

		foreach ($adjustments as $adjustment) {
			$before = mb_substr($content, 0, $adjustment['position'], 'UTF-8');
			$after = mb_substr($content, $adjustment['position'] + $adjustment['length'], null, 'UTF-8');

			$content = $before . $adjustment['replacement'] . $after;
		}

		return ['adjustments' => !empty($adjustments), 'errors' => $response['error'], 'content' => $content];
	}

	/**
	 * Only add the adjustment if it doesn't overlap any existing adjustments
	 *
	 * @param $adjustments
	 * @param $_adjusted_position
	 * @param $_error
	 * @param $_error_replacement
	 *
	 * @return mixed
	 */
	private static function addAdjustment($adjustments, $_adjusted_position, $_error, $_error_replacement): mixed
	{
		foreach ($adjustments as $_adjustment) {
			$_end_position = $_adjusted_position + $_error['length'];
			$_adjustment_end_position = $_adjustment['position'] + $_adjustment['length'];
			if (self::overlapExists($_adjusted_position, $_end_position, $_adjustment['position'], $_adjustment_end_position)) {
				return $adjustments; // exists, no need to add
			}
		}

		// unique, add new adjustment
		$adjustments[] = [
			'position' => $_adjusted_position,
			'length' => $_error['length'],
			'replacement' => $_error_replacement
		];

		return $adjustments;
	}

	private static function overlapExists($start1, $end1, $start2, $end2): bool
	{
		return ($start1 >= $start2 && $start1 < $end2) || ($end1 > $start2 && $end1 <= $end2);
	}

	/**
	 * Replaces the misspelled text with a span element containing a popover with suggestions for correction.
	 * This function generates HTML code for a span element that highlights the misspelled word and contains a popover
	 * with a header, message, and suggestions for correction, allowing users to interactively select a correction.
	 *
	 *
	 * @param string $suspicious The misspelled text.
	 * @param string $header The header for the popover.
	 * @param string $message The message for the popover.
	 * @param array $suggestions Suggestions for the misspelled word.
	 * @return string The span element with the popover.
	 */
	private static function replaceMisspelledTextWithSpan(string $suspicious, string $header, string $message, array $suggestions): string
	{
		// take only first 5 suggestions
		$suggestions = array_slice($suggestions, 0, 5, true);

		$suggestions_html = array_map(function ($suggestion, $_index) {
			$checked = $_index === 0 ? ' checked' : '';
			return "<div class=\"form-check\"><input class=\"form-check-input spellcheck-option\" type=\"radio\" name=\"spellcheckOption\" id=\"suggestion$_index\" value=\"$suggestion\"$checked><label class=\"form-check-label\" for=\"suggestion$_index\">$suggestion</label></div>";
		}, $suggestions, array_keys($suggestions));

		$custom_input_html = '<div class="form-check"><input class="form-check-input spellcheck-option" type="radio" name="spellcheckOption" id="customInputOption" value="custom"' . (empty($suggestions) ? ' checked' : '') . '><label class="form-check-label" for="customInputOption"><input type="text" class="form-control d-inline-block radio_input_text" placeholder="Upiši zamjenu..."></label></div>';
		$suggestions_html[] = $custom_input_html;
		$popover_content = htmlspecialchars($message . implode('', $suggestions_html) . '<div class="mt-3"><button type="button" class="btn btn-secondary spellcheck-suggestion-ignore">Zanemari</button> <button type="button" class="btn btn-primary spellcheck-suggestion-apply">Zamijeni</button></div>', ENT_QUOTES, 'UTF-8');

		return sprintf('<span tabindex="0" class="spelling-error" data-toggle="popover" data-content="%s" data-html="true" title="%s">%s</span>', $popover_content, htmlspecialchars($header, ENT_QUOTES, 'UTF-8'), htmlspecialchars($suspicious, ENT_QUOTES, 'UTF-8'));
	}

	/**
	 * Calculates the position of the n-th full-word match in the content for a given misspelled word.
	 * This function determines the exact position in the content where the misspelled word occurs, taking
	 * into account HTML and other formatting that may affect the text's appearance, ensuring accurate replacement.
	 *
	 * @param string $text The original text.
	 * @param string $content The content in which to find the position.
	 * @param string $suspicious The misspelled word.
	 * @param int $position The position of the word in the text.
	 * @param bool $is_spacing_error The error is related to spacing.
	 *
	 * @return int|null The position in the content or null if not found.
	 */
	private static function calculateHtmlPosition(string $text, string $content, string $suspicious, int $position, bool $is_spacing_error): ?int
	{
		$text_before_position = mb_substr($text, 0, $position, 'UTF-8');
		$escaped_suspicious = preg_quote($suspicious, '/');

		// special cases (spacing errors OR string contains non-alphanumeric characters)
		if ($is_spacing_error || preg_match('/[^\p{L}\p{N}]/u', $suspicious)) {
			$pattern = sprintf('/%s/u', $escaped_suspicious);
		} else {
			// for regular words, use word boundaries
			$pattern = sprintf('/\b%s\b/u', $escaped_suspicious);
		}

		preg_match_all($pattern, $text_before_position, $matches);
		$n = count($matches[0]);

		$occurrence_count = 0;
		$current_position = 0;

		while (($current_position = mb_strpos($content, $suspicious, $current_position, 'UTF-8')) !== false) {
			if (self::isFullWordMatch($content, $suspicious, $current_position) || $is_spacing_error) {
				// found match
				if ($occurrence_count === $n) {
					return $current_position;
				}

				$occurrence_count++;
			}

			$current_position++;
		}

		return null;
	}

	/**
	 * Checks if the found match is a full word match.
	 * This function determines if a match for a misspelled word in the content is a full word or part of another word,
	 * ensuring that corrections are only applied to complete, standalone words. This is crucial for maintaining the accuracy
	 * of the spell check corrections and avoiding unintended replacements within larger words or within HTML tags.
	 *
	 * @param string $content The content.
	 * @param string $suspicious The misspelled word.
	 * @param int $position The position of the misspelled word in the content.
	 * @return bool True if it's a full word match, false otherwise.
	 */
	private static function isFullWordMatch(string $content, string $suspicious, int $position): bool
	{
		// special case - string contains non-alphanumeric characters
		if (preg_match('/[^\p{L}\p{N}]/u', $suspicious)) {
			return true;
		}

		$before = $position === 0 || !preg_match('/\w/u', mb_substr($content, $position - 1, 1, 'UTF-8'));
		$after = ($position + mb_strlen($suspicious, 'UTF-8') === mb_strlen($content, 'UTF-8')) || !preg_match('/\w/u', mb_substr($content, $position + mb_strlen($suspicious, 'UTF-8'), 1, 'UTF-8'));

		return $before && $after;
	}
}

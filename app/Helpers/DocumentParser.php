<?php

namespace App\Helpers;
use App\Models\DocumentDraft;
use DOMDocument;
use DOMXPath;
use voku\helper\HtmlDomParser;

class DocumentParser {

	private HtmlDomParser $dom;

	private int $type;
	private string $html;
    private array $signees = [];
	private array $segments = [];
	private string $legal_remedy;
	private array $recipients = [];
	private array $attachments = [];

	// span class for the signature lines
	public static string $signature_line_class = 'signature-line';

	public function __construct(string $html, int $type) {
		$this->type = $type;
		$this->setHtml($html);
	}

	public function getSegments(): array
    {
		return $this->segments;
	}

    public function getSignees(): array
    {
        return $this->signees;
    }

	public function getLegalRemedy(): string
	{
		return $this->legal_remedy;
	}

	public function getRecipients(): array
	{
		return $this->recipients;
	}

	public function getAttachments(): array
	{
		return $this->attachments;
	}

	private function setHtml(string $html): void {
		$this->html = $html;
		$this->parse();
	}

	public function getContent(): string {

		$content = collect($this->getSegments())->implode('raw');

		// add supplementaries (attachments, recipients, legal remedy)
		if($supplementaries_container = $this->dom->getElementById('supplementaries-container')){
			if($supplementaries_content = $supplementaries_container->html()) {
				$content.= $supplementaries_content;
			}
		}

		return $content;
	}

	private function parse(): void {
		$this->dom = HtmlDomParser::str_get_html($this->html);
		$segments = $this->dom->findMulti('.editable-segment');

		// extract segments
		$this->segments = collect($segments)->map(function($_segment){

			if($this->type == DocumentDraft::$types['contract']) {
				return [
					'raw' => $this->sanitize($_segment->outerHTML),
					'html' => $this->prepareDocumentForEditor($_segment->outerHTML),
					'type' => $_segment->getAttribute('data-type'),
					'description' => $_segment->getAttribute('data-description'),
				];
			} elseif($this->type == DocumentDraft::$types['statement']) {
				return [
					'raw' => $this->sanitize($_segment->outerHTML),
					'html' => $this->prepareStatementForEditor($_segment->outerHTML),
					'type' => $_segment->getAttribute('data-type'),
				];
			} else {
				throw new \Exception('Unrecognized document type: ' . $this->type);
			}

		})->toArray();

		// extract signees, attachments, recipients and legal remedy
		$this->extractSignees();
		$this->extractAttachments();
		$this->extractRecipients();
		$this->extractLegalRemedy();
	}

	private function extractSignees(): void {

		$xpath = new DOMXPath($this->dom->getDocument());

		$query = '//tr/td/span[contains(@class, "'.self::$signature_line_class.'")]/following-sibling::small';

		$nodes = $xpath->query($query);

		foreach ($nodes as $_node) {
			// replace <br> elements with newline characters
			foreach ($_node->getElementsByTagName('br') as $br) {
				$br->parentNode->replaceChild($this->dom->getDocument()->createTextNode("\r"), $br); // Adjusted
			}
			// determine the TD index (1-based) within the TR
			$tdIndex = $xpath->evaluate('count(../preceding-sibling::td) + 1', $_node);

			$this->signees[] = [
				'signee' => preg_replace("/\r\s+/", "\r", $_node->nodeValue), // trim spaces after new line
				'side' => $tdIndex == 1 ? 'left' : 'right'
			];
		}
	}

	private function extractRecipients(): void {

		$recipient_spans = $this->dom->findMulti('span[class="recipient"]');

		foreach($recipient_spans as $_recipient_span) {
			$this->recipients[] = $_recipient_span->innerHTML;
		}
	}

	private function extractAttachments(): void {

		$attachment_spans = $this->dom->findMulti('span[class="attachment"]');

		foreach($attachment_spans as $_attachment_span) {
			$this->attachments[] = $_attachment_span->innerHTML;
		}
	}

	private function extractLegalRemedy(): void {

		if($legal_remedy_div = $this->dom->getElementById('legal-remedy')) {
			$this->legal_remedy = str($legal_remedy_div->innerHTML)
				->remove(["<br>", "<br />"]); // remove <br> characters for display in editor
		}
	}

    private static function prepareDocumentForEditor($html): string {
		// perform basic sanitization
		$html = self::sanitize($html);

		// convert all article titles into ordered list items
        $html = preg_replace(
            '/<p class="article-header">\s*Članak \b[0-9]+\b.\s*(.*?)<\/p>/s',
            '<ol class="article-list"><li><p class="article-header">$1</p></li></ol>',
            $html
        );

        // convert all numbered paragraphs into ordered list items
		$html = preg_replace(
			'/<p>\d+\.\d+\.\s+(.+?)<\/p>/',
			'<ol class="paragraph-list"><li>$1</li></ol>',
			$html
		);

		// do the same for styled version of numbered paragraphs
		$html = preg_replace(
			'/<p style="(.+?)">\d+\.\d+\.\s+(.+?)<\/p>/',
			'<ol class="paragraph-list"><li style="$1">$2</li></ol>',
			$html
		);

		// convert bold table <td> into <strong>
		$html = preg_replace(
			'/<td class="strong">(.*?)<\/td>/',
			'<td><strong>$1</strong></td>',
			$html
		);

        // unwrap stick-to-header divs
        $html = preg_replace('/<div class="stick-to-header">(.*?)<\/div>/', '$1', $html);

        // additional post-processing
	    $html = self::postProcess($html);

        return $html;
	}

    private static function prepareStatementForEditor($html): string {
        $html = self::sanitize($html);
        $html = self::postProcess($html);

        return $html;
    }

	/**
	 * 1. Removes signature lines
	 * 2. Replaces class text-center with style tags
	 *
	 * @param $html
	 *
	 * @return string
	 */
	private static function postProcess($html): string {

		$dom = new DOMDocument();
		libxml_use_internal_errors(true); // prevent warnings on invalid HTML
		$dom->loadHTML(mb_convert_encoding($html, 'HTML-ENTITIES', 'UTF-8'), LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
		$xpath = new DOMXPath($dom);

		// find all tables that are parent of 'signature-line' class element
		$signature_tables = $xpath->query('//span[@class="'.self::$signature_line_class.'"]/ancestor::table');

		if(!empty($signature_tables)) {

			// remove these tables from the document
			foreach ($signature_tables as $_signature_table) {
				$_signature_table->parentNode->removeChild($_signature_table);
			}
		}

		// replace text-centered paragraphs
		$paragraphs = $xpath->query('//p[contains(@class, "text-center")]');

		foreach ($paragraphs as $_p) {
			$class_attribute = $_p->getAttribute('class');
			$new_class_attribute = str_replace('text-center', '', $class_attribute);
			$_p->setAttribute('class', trim($new_class_attribute));

			$style_attribute = $_p->getAttribute('style');
			$_p->setAttribute('style', trim($style_attribute . ' text-align: center;'));
		}

		// remove all counter-reset style attributes
		foreach ($xpath->query('//li[contains(@style, "counter-reset")]') as $li) {
			$li->setAttribute('style', trim(preg_replace('/counter-reset\s*:[^;]+;?/', '', $li->getAttribute('style')), " ;"))
				?: $li->removeAttribute('style');
		}

		$html = $dom->saveHTML();

		return html_entity_decode($html, ENT_QUOTES, 'UTF-8');
	}


	private static function sanitize($html): array|string {

        // trim inside html nodes
        $html = preg_replace_callback('/>\s+|\s+</', function($matches){
            return ($matches[0]);
        }, $html);

        // replace new line characters (and spaces after) with just a single space
        $html = preg_replace('/\n\s*/', ' ', $html);

        // remove extra spaces
        $html = str_ireplace(
            '<p class="article-header"> ',
            '<p class="article-header">',
            $html
        );

        // remove paragraph empty style tags
        $html = str_ireplace(
            '<p style="">',
            '<p>',
            $html
        );

        // trim leading paragraph spacing
        $html = str_ireplace(
            '<p> ',
            '<p>',
            $html
        );

        // unwrap document placeholder spans
        $html = preg_replace('/<span class="document_placeholder">(.*?)<\/span>/', '$1', $html);

		// replace signature img tag with multiple break tags
		$html = preg_replace('/<img alt="signature" src="(.+?)">/', '<br/><br/><br/><br/><br/><br/><br/><br/>', $html);

		// shorten signature line by 2 characters (overflow bug)
		$html = str_ireplace(
			'<span class="signature-line">_____________________________________________</span>',
			'<span class="signature-line">___________________________________________</span>',
			$html
		);

		return $html;
	}


    public static function formatArticleBody($html): array|string|null
    {
        // wrap the HTML inside a div
        $wrapped_html = '<div>' . $html . '</div>';

        // process the wrapped HTML
        $processed_html = self::prepareDocumentForEditor(
            StringHelper::enumerateParagraphs($wrapped_html)
        );

        // unwrap the HTML from the div by removing the starting and ending <div> tags
        return preg_replace('/^<div>(.*)<\/div>$/', '$1', $processed_html);
	}

}

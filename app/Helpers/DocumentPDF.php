<?php
/**
 * Created by PhpStorm.
 * User: dinocagalj
 * Date: 2020-06-08
 * Time: 14:21
 */

namespace App\Helpers;

use App\Helpers\DocumentPDFSanitizer as DocumentPDFSanitizer;
use Mpdf\Mpdf;
use misters<PERSON><PERSON>\LaravelPdf\Pdf as LaravelPdf;

class DocumentPDF extends LaravelPdf
{
    public function __construct(string $html = '', array $config = [], $watermark = null)
    {
        $this->config = $config;

        $mpdf_config = [
            'mode'                 =>   $this->getConfig('mode'),              // mode - default ''
            'format'               =>   $this->getConfig('format'),            // format - A4, for example, default ''
            'margin_left'          =>   $this->getConfig('margin_left'),       // margin_left
            'margin_right'         =>   $this->getConfig('margin_right'),      // margin right
            'margin_top'           =>   $this->getConfig('margin_top'),        // margin top
            'margin_bottom'        =>   $this->getConfig('margin_bottom'),     // margin bottom
            'margin_header'        =>   $this->getConfig('margin_header'),     // margin header
            'margin_footer'        =>   $this->getConfig('margin_footer'),     // margin footer
            'tempDir'              =>   $this->getConfig('tempDir'),           // temp dir
            'shrink_tables_to_fit' =>   false                                      // do not shrink tables to fit page
        ];

        // Handle custom fonts
        $mpdf_config = $this->addCustomFontsConfig($mpdf_config);

        $this->mpdf = new Mpdf($mpdf_config);

        // Set header and footer margins
        $this->mpdf->setAutoBottomMargin = 'stretch';

        if(tenant()->headerText || tenant()->headerLogoPath) {
            $this->mpdf->setAutoTopMargin = 'stretch';
        }

        $this->mpdf->SetTitle         ( $this->getConfig('title') );
        $this->mpdf->SetAuthor        ( $this->getConfig('author') );
        $this->mpdf->SetCreator       ( $this->getConfig('creator') );
        $this->mpdf->SetSubject       ( $this->getConfig('subject') );
        $this->mpdf->SetKeywords      ( $this->getConfig('keywords') );
        $this->mpdf->SetDisplayMode   ( $this->getConfig('display_mode') );

        if($watermark) {
            $this->mpdf->SetWatermarkText($watermark);
            $this->mpdf->showWatermarkText = true;
        }

	    $html = DocumentPDFSanitizer::avoidPageBreakAfterArticleHeader(
		    DocumentPDFSanitizer::sanitize($html)
	    );

        $this->mpdf->WriteHTML($html);
    }

}

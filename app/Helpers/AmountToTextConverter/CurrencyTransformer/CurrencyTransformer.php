<?php

namespace App\Helpers\AmountToTextConverter\CurrencyTransformer;

use App\Helpers\AmountToTextConverter\Exception\NumberToWordsException;
use App\Helpers\AmountToTextConverter\TransformerOptions\CurrencyTransformerOptions;

interface CurrencyTransformer
{
    /**
     * @throws NumberToWordsException
     */
    public function toWords(int $amount, string $currency, ?CurrencyTransformerOptions $options = null): string;
}

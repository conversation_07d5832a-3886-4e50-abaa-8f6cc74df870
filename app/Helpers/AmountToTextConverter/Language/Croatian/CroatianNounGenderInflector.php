<?php

namespace App\Helpers\AmountToTextConverter\Language\Croatian;

class CroatianNounGenderInflector
{
    public function inflectNounByNumber(int $number, string $singular, string $plural, string $genitivePlural): string
    {
        $units = $number % 10;
        $tens = ((int) ($number / 10)) % 10;

        if ($tens !== 1 && $units === 1) {
            return $singular;
        }

        if ($tens === 1 && $units > 1) {
            return $genitivePlural;
        }

        if ($units >= 2 && $units <= 4) {
            return $plural;
        }

        return $genitivePlural;
    }

	public function inflectNounByFractionNumber(int $number, string $singular, string $plural, string $genitivePlural): string
	{
		$units = $number % 10;
		$tens = ((int) ($number / 10)) % 10;

		if ($units === 1) {
			return $singular;
		}

		if($tens === 1) {
			return $plural;
		}

		if($units > 1 && $units < 5) {
			return $genitivePlural;
		}

		return $plural;
	}
}

<?php

namespace App\Helpers\AmountToTextConverter\Language\Croatian;

use App\Helpers\AmountToTextConverter\Language\Dictionary;

class CroatianDictionary implements Dictionary
{
    public const LOCALE = 'hr';
    public const LANGUAGE_NAME = 'Croatian';
    public const LANGUAGE_NAME_NATIVE = 'hrvatski';

    private static array $units = [
        0 => '',
        1 => 'jedan',
        2 => 'dva',
        3 => 'tri',
        4 => 'četiri',
        5 => 'pet',
        6 => 'šest',
        7 => 'sedam',
        8 => 'osam',
        9 => 'devet'
    ];

    private static array $teens = [
        0 => 'deset',
        1 => 'jedanaest',
        2 => 'dvanaest',
        3 => 'trinaest',
        4 => 'četrnaest',
        5 => 'petnaest',
        6 => 'šestnaest',
        7 => 'sedamnaest',
        8 => 'osamnaest',
        9 => 'devetnaest'
    ];

    private static array $tens = [
        0 => '',
        1 => 'deset',
        2 => 'dvadeset',
        3 => 'trideset',
        4 => 'četrdeset',
        5 => 'pedeset',
        6 => 'šezdeset',
        7 => 'sedamdeset',
        8 => 'osamdeset',
        9 => 'devedeset'
    ];

    private static array $hundreds = [
        0 => '',
        1 => 'sto',
        2 => 'dvjesto',
        3 => 'tristo',
        4 => 'četiristo',
        5 => 'petsto',
        6 => 'šeststo',
        7 => 'sedamsto',
        8 => 'osamsto',
        9 => 'devetsto'
    ];

    public static array $currencyNames = [
        'EUR' => [['euro', 'eura', 'eura'], ['cent', 'centi', 'centa']],
    ];

    public function getMinus(): string
    {
        return 'minus';
    }

    public function getZero(): string
    {
        return 'nula';
    }

    public function getCorrespondingUnit(int $unit): string
    {
        return self::$units[$unit];
    }

    public function getCorrespondingTen(int $ten): string
    {
        return self::$tens[$ten];
    }

    public function getCorrespondingTeen(int $teen): string
    {
        return self::$teens[$teen];
    }

    public function getCorrespondingHundred(int $hundred): string
    {
        return self::$hundreds[$hundred];
    }
}

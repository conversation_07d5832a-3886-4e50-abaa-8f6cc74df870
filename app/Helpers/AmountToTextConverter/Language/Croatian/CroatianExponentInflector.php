<?php

namespace App\Helpers\AmountToTextConverter\Language\Croatian;

use App\Helpers\AmountToTextConverter\Language\ExponentInflector;

class CroatianExponentInflector implements ExponentInflector
{
    private static array $exponent = [
		['', '', ''],
        ['tisuću', 'tisuće', 'tisuća'],
        ['milijun', 'milijuna', 'milijuna'],
        ['miljarda', 'miljarde', 'miljarda'],
        ['bilijun', 'bilijuna', 'bilijuna'],
    ];

    private CroatianNounGenderInflector $inflector;

    public function __construct(CroatianNounGenderInflector $inflector)
    {
        $this->inflector = $inflector;
    }

    public function inflectExponent(int $number, int $power): string
    {
        return $this->inflector->inflectNounByNumber(
            $number,
            self::$exponent[$power][0],
            self::$exponent[$power][1],
            self::$exponent[$power][2]
        );
    }
}

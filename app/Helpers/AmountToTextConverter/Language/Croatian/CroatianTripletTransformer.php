<?php

namespace App\Helpers\AmountToTextConverter\Language\Croatian;
use App\Helpers\AmountToTextConverter\Language\TripletTransformer;

class CroatianTripletTransformer implements TripletTransformer
{
    private CroatianDictionary $croatianDictionary;

    public function __construct(CroatianDictionary $croatianDictionary)
    {
        $this->croatianDictionary = $croatianDictionary;
    }

    public function transformToWords(int $number): string
    {
        $units = $number % 10;
        $tens = (int) ($number / 10) % 10;
        $hundreds = (int) ($number / 100) % 10;
        $words = [];

        if ($hundreds > 0) {
            $words[] = $this->croatianDictionary->getCorrespondingHundred($hundreds);
        }

        if ($tens === 1) {
            $words[] = $this->croatianDictionary->getCorrespondingTeen($units);
        }

        if ($tens > 1) {
            $words[] = $this->croatianDictionary->getCorrespondingTen($tens);
        }

        if ($units > 0 && $tens !== 1) {
            $words[] = $this->croatianDictionary->getCorrespondingUnit($units);
        }

        return implode(' ', $words);
    }
}

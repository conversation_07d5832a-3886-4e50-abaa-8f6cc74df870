<?php

namespace App\Helpers\AmountToTextConverter\Concerns;

use App\Helpers\AmountToTextConverter\CurrencyTransformer\CroatianCurrencyTransformer;
use App\Helpers\AmountToTextConverter\Exception\InvalidArgumentException;
use App\Helpers\AmountToTextConverter\CurrencyTransformer\CurrencyTransformer;

trait ManagesCurrencyTransformers
{
    private array $currencyTransformers = [
        'hr' => CroatianCurrencyTransformer::class,
    ];

    /**
     * @throws InvalidArgumentException
     */
    public function getCurrencyTransformer(string $language): CurrencyTransformer
    {
        if (!array_key_exists($language, $this->currencyTransformers)) {
            throw new InvalidArgumentException(sprintf(
                'Currency transformer for "%s" language is not implemented.',
                $language
            ));
        }

        return new $this->currencyTransformers[$language]();
    }

    public static function transformCurrency(string $language, int $number, string $currency): string
    {
        return (new static())->getCurrencyTransformer($language)->toWords($number, $currency);
    }
}

<?php


namespace App\Helpers;

/**
 * Class ViewSingleton
 * Used to sync dynamic data between nested Laravel blade views
 * @package App\Helpers
 */
class ViewSingleton {

    public $p_index; // paragraph index

    protected static $instance;

    public static function getInstance()
    {
        if (is_null(static::$instance)) {
            static::$instance = new static();
        }

        return static::$instance;
    }

    /**
     * Protected constructor to prevent creating a new instance of the
     * singleton via the `new` operator.
     */
    protected function __construct()
    {
        // your constructor logic here.
    }

    /**
     * Private clone method to prevent cloning of the instance of the singleton instance.
     */
    private function __clone()
    {
    }

    /**
     * Public unserialize method to prevent unserializing of the singleton instance.
     */
    public function __wakeup()
    {
    }

}

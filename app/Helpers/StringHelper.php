<?php

namespace App\Helpers;

use App\Helpers\AmountToTextConverter\NumberToWords;
use Illuminate\Support\Str;

class StringHelper {

	public static function calculateOwnershipRatio($num1, $denom1, $num2, $denom2): string {
		// first, check if division without remainder is possible
		if($num2 % $denom1 === 0) {
			$num = $num2 / $denom1;
			$denom = $denom2;
		} else {
			// else do a multiplication
			$num = $num1 * $num2;
			$denom = $denom1 * $denom2;
		}

		return "$num/$denom";
	}

	public static function getPrimjerakaString($num): string {

		if (in_array($num, [1,21,31,41,51,61,71,81,91])) {
			return "primjerku";
		}
        elseif(in_array($num ,[2,3,4,22,23,24,32,33,34,42,43,44,52,53,54,62,63,64,72,73,74,82,83,84,92,93,94])){
	        return "primjerka";
        } else {
	        return "primjeraka";
        }
    }

	public static function getRadnihDanaString($num): string {

		if (in_array($num, [1,21,31,41,51,61,71,81,91])) {
			return "radni dan";
		}
		elseif(in_array($num ,[2,3,4,22,23,24,32,33,34,42,43,44,52,53,54,62,63,64,72,73,74,82,83,84,92,93,94])){
			return "radna dana";
		}else {
			return "radnih dana";
		}
	}

	public static function getPossessionAreaString($num, $type): string {
		// set default type to "m2"
		$type = $type ?: "m2";

		// If type is "jutro", validate the number and declinate
		if ($type === "jutro") {
			$number = is_numeric($num) ? $num : null;

			if (!empty($number)) {
				// get the last two digits from a number (remainder of mod 100)
				$_rem = $number % 100;

				if (in_array($_rem, [1, 21, 31, 41, 51, 61, 71, 81, 91])) {
					$type = "jutro";
				} elseif (in_array($_rem, [2, 3, 4, 22, 23, 24, 32, 33, 34, 42, 43, 44, 52, 53, 54, 62, 63, 64, 72, 73, 74, 82, 83, 84, 92, 93, 94])) {
					$type = "jutra";
				} else {
					$type = "jutara";
				}
			}
		}

		return "$num $type";
	}

	public static function getSatiString($num): string {

		if (in_array($num, [1,21,31,41,51,61,71,81,91])) {
			return "sat";
		}
		elseif(in_array($num ,[2,3,4,22,23,24,32,33,34,42,43,44,52,53,54,62,63,64,72,73,74,82,83,84,92,93,94])){
			return "sata";
		} else {
			return "sati";
		}
	}

	public static function getAdditionalLandAreaAndIdentificationPrefix(array $data): string {

		return count($data) == 1 ? "(koju čini " : "(koju čine: ";
	}

	public static function countableConjunction($data_count, $index, $future_tense = false, $conjuction = "te", $end_notation = ".") {
		if ($index == $data_count - 1) {
			return $end_notation;
		} elseif ($index == $data_count - 2) {
			return $future_tense ? " te će" : " $conjuction";
		} else {
			return ",";
		}
	}

	/** Wordpress function
	 *  Replaces double line breaks with paragraph elements.
	 *  https://developer.wordpress.org/reference/functions/wpautop/
	 */
	public static function wordpressContent($content): string
	{
		// include the WordPress formatting functions
		require_once(__DIR__ . '/../../wordpress/wp-includes/formatting.php');

		// replace all <table> tags with a specific class
		$content = preg_replace('/<table.*?>/i', '<table class="table table-bordered">', $content);

		// extract all footnotes enclosed in double brackets and append them to the end of the content
		preg_match_all('/\(\((.*?)\)\)/', $content, $footnotes);
		$content = preg_replace_callback('/\(\((.*?)\)\)/', function ($matches) use (&$_footnote_index) {
			$_footnote_index++;
			return '<sup><a href="#footnote-' . $_footnote_index . '" id="ref-footnote-' . $_footnote_index . '">[' . $_footnote_index . ']</a></sup>';
		}, $content);

		// append footnotes as an ordered list
		if (!empty($footnotes[1])) {
			$content.= '<hr/>';
			$content .= '<ol class="footnotes">';
			$_footnote_index = 0; // reset index for the list
			foreach ($footnotes[1] as $_footnote) {
				$_footnote_index++;
				$content .= '<li id="footnote-' . $_footnote_index . '">' . $_footnote . ' <a href="#ref-footnote-' . $_footnote_index . '">↩</a></li>';
			}
			$content .= '</ol>';
		}

		// return the formatted content with automatic paragraph tags
		return wpautop($content);
	}

	/**
	 *
	 * Generates excerpt from a given string in case string is larger than $limit
	 * Preserves word boundaries
	 *
	 * @param $string
	 * @param int $limit
	 *
	 * @return string
	 */
	public static function generateExcerpt($string, int $limit = 300): string {
		if(!empty($string)) {

			if(strlen($string) > $limit) {
				$charAtPosition = "";
				$titleLength = strlen($string);

				do {
					$limit++;
					$charAtPosition = mb_substr($string, $limit, 1, 'UTF-8');
				} while ($limit < $titleLength && $charAtPosition != " ");

				return mb_substr($string, 0, $limit, 'UTF-8') . '...';
			}

		}

		return $string;

	}

	// replace last occurrence of string within a string
	public static function replaceLastOccurrence($search, $replace, $subject)
	{
		$pos = strrpos($subject, $search);

		if($pos !== false)
		{
			$subject = substr_replace($subject, $replace, $pos, strlen($search));
		}

		return $subject;
	}

	// check if strings contain same words
	public static function stringContainsAnotherStringWords($str1, $str2): bool {
		$str1_arr = explode(" ", mb_strtoupper($str1, 'UTF-8'));
		$str2_arr = explode(" ", mb_strtoupper($str2, 'UTF-8'));

		return array_intersect($str1_arr, $str2_arr) == $str1_arr;
	}

	// extract text from parenthesis
	public static function extractTextFromParenthesis($string): ?string {
		$opening_bracket_index = strpos($string, '(');
		$closing_bracket_index = strpos($string, ')');

		if($opening_bracket_index && $closing_bracket_index) {
			$length = $closing_bracket_index - $opening_bracket_index;
			return substr($string, $opening_bracket_index + 1, $length - 1);
		}

		return null;
	}

	// remove anything from a string that isn't a digit, period (.) or comma (,):
	private static function cleanAmountNumber($string): array|string|null {
		return preg_replace('/[^\d.,]/', '', $string);
	}

	public static function amountToText($number): string {

		$currency = "EUR";

		if($number !== DocumentBuilder::$placeholder) {
			$text = Str::replace(',', '', $number);
			$text = Str::replace('.', '', $text);

			// restrict the maximum number to 18 digits
			if(strlen("$text") < 18) {
				$text = NumberToWords::transformCurrency('hr', (int)$text, 'EUR');
				$number_cleaned = self::cleanAmountNumber($number);
				return "$number_cleaned $currency (slovima: $text)";
			}
		}

		return "$number $currency";

	}

	public static function dateToText($date, $default = null) {

		$timestamp = strtotime(str_replace(' ', '', $date));

		if ($timestamp === false) {
			return $default;
		}

		// enforce d.m.Y. format
		$formatted_date = date('d.m.Y.', $timestamp);

		$dtt = new DateToText();
		if ($spelled_out_date = $dtt->spellOutDate($formatted_date)) {
			return "$date (slovima: $spelled_out_date)";
		}

		return $default;
	}

	public static function getSubstringBetween($string, $start, $end): string {
		$pos_string = stripos($string, $start);
		$substr_data = substr($string, $pos_string);
		$string_two = substr($substr_data, strlen($start));
		$second_pos = stripos($string_two, $end);
		$string_three = substr($string_two, 0, $second_pos);

		return trim($string_three);
	}


	public static function enumerateParagraphs($html, $starting_number = 1): array|string|null {
		$counter = 0;
		return preg_replace_callback('/<p>(.*?)<\/p>/s', function ($matches) use ($starting_number, &$counter) {
			// check if the paragraph contains a <span> with the class "remove-enumeration"
			if (!preg_match('/<span.*?class="remove-enumeration".*?>(.*?)<\/span>/s', $matches[1], $span_matches)) {
				$counter++;
				$unique_string = "{$starting_number}.{$counter}";
				return "<p>{$unique_string}. " . $matches[1] . "</p>";
			} else {
				// if it contains a <span> with the class "remove-enumeration", wrap it with <p> tags w/o enumeration
				return "<p>{$matches[1]}</p>";
			}
		}, $html);
	}

	public static function sanatizeStringForDownload($string): array|string {
		$str = str_replace(['/', '\\'], '-', $string);
		$str = str_replace([':'], '', $str);
		return $str;
	}

	// escapes the value recursively
	public static function escape($value) {
		if (is_string($value)) {
			return htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
		} elseif (is_array($value)) {
			// Recursively process each element of the array
			return array_map([self::class, 'escape'], $value);
		}

		return $value;
	}

	public static function formatCurrency($number) {

		// check if number is empty
		if (empty($number)) {
			return '';
		}

		// remove non-digit, comma, or dot characters
		$number = preg_replace('/[^\d.,]/', '', $number);

		// replace commas with dots
		$number = str_replace(',', '.', $number);

		// replace all dots except the last one
		$number = preg_replace('/\.(?!\d*$)/', '', $number);

		if (strlen($number) > 0) {
			// convert to float
			$number = (float) $number;

			// format number to currency without symbol
			return number_format($number, 2, ',', '.');

		} else {
			// invalid input
			return '';
		}
	}

}


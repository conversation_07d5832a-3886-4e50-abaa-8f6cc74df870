<?php

namespace App\Helpers;

class ArrayHelper {

	/**
	 * Filters out empty values and standardizes keys
	 */
	public static function standardizeArrayKeys($array) {
		return !empty($array) ? array_values(array_filter($array)) : [];
	}

	/**
	 * Modifies the input array of signees so that each signature has both left and right sides.
	 */
    public static function adjustSignees($signees): array
    {
        $left_signees = array_filter($signees, function ($signee) {
            return $signee['side'] === 'left';
        });
        $right_signees = array_filter($signees, function ($signee) {
            return $signee['side'] === 'right';
        });

        // Get signee names
        $left_signees = array_column($left_signees, 'signee');
        $right_signees = array_column($right_signees, 'signee');

        // Calculate max length and pad both arrays with nulls to that length
        $max_length = max(count($left_signees), count($right_signees));
        $left_signees = array_pad($left_signees, $max_length, null);
        $right_signees = array_pad($right_signees, $max_length, null);

        // Combine left and right signees
        $modified_signees = [];
        for ($i = 0; $i < $max_length; $i++) {
            $modified_signees[] = [
                'left' => $left_signees[$i],
                'right' => $right_signees[$i]
            ];
        }

        return $modified_signees;
    }


}

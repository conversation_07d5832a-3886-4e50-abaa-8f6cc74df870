<?php

namespace App\Helpers;

class DateToText {

	private $day_declinated;
	private $month_declinated;
	private $last_two_digits_declinated;

	public function __construct() {
		$this->day_declinated = $this->generateDayDeclinated();
		$this->month_declinated = $this->generateMonthDeclinated();
		$this->last_two_digits_declinated = $this->generateLastTwoDigitsDeclinated();
	}

	// assumes date is in format d.m.Y.
	public function spellOutDate($date) {
		list($day, $month, $year) = explode('.', $date);

		$day_str = $this->day_declinated[$day] ?? "N/A";
		$month_str = $this->month_declinated[$month] ?? "N/A";

		$first_two_digits = substr($year, 0, 2);
		$last_two_digits = substr($year, 2, 2);

		$first_two_digits_str = '';
		$last_two_digits_str = '';

		if ($first_two_digits === '19') {
			$first_two_digits_str = $last_two_digits === '00' ? 'tisuću devetstote' : 'tisuću devetsto';
		} elseif ($first_two_digits === '20') {
			$first_two_digits_str = $last_two_digits === '00' ? 'dvije tisućite' : 'dvije tisuće';
		}

		if ($last_two_digits !== '00') {
			$last_two_digits = ltrim($last_two_digits, '0');
			$last_two_digits_str = $this->last_two_digits_declinated[$last_two_digits] ?? "N/A";
		}

		$year_str = $first_two_digits_str;
		if (!empty($last_two_digits_str)) {
			$year_str .= " $last_two_digits_str";
		}

		// only return string if all conversions successful
		if(!in_array("N/A", [$day_str, $month_str, $year_str])) {
			return trim("$day_str $month_str $year_str");
		} else {
			return null;
		}
	}

	private function generateDayDeclinated() {
		return [
			'01' => 'prvog',
			'02' => 'drugog',
			'03' => 'trećeg',
			'04' => 'četvrtog',
			'05' => 'petog',
			'06' => 'šestog',
			'07' => 'sedmog',
			'08' => 'osmog',
			'09' => 'devetog',
			'10' => 'desetog',
			'11' => 'jedanaestog',
			'12' => 'dvanaestog',
			'13' => 'trinaestog',
			'14' => 'četrnaestog',
			'15' => 'petnaestog',
			'16' => 'šesnaestog',
			'17' => 'sedamnaestog',
			'18' => 'osamnaestog',
			'19' => 'devetnaestog',
			'20' => 'dvadesetog',
			'21' => 'dvadeset prvog',
			'22' => 'dvadeset drugog',
			'23' => 'dvadeset trećeg',
			'24' => 'dvadeset četvrtog',
			'25' => 'dvadeset petog',
			'26' => 'dvadeset šestog',
			'27' => 'dvadeset sedmog',
			'28' => 'dvadeset osmog',
			'29' => 'dvadeset devetog',
			'30' => 'tridesetog',
			'31' => 'trideset prvog'
		];
	}

	private function generateMonthDeclinated() {
		return [
			'01' => 'siječnja',
			'02' => 'veljače',
			'03' => 'ožujka',
			'04' => 'travnja',
			'05' => 'svibnja',
			'06' => 'lipnja',
			'07' => 'srpnja',
			'08' => 'kolovoza',
			'09' => 'rujna',
			'10' => 'listopada',
			'11' => 'studenog',
			'12' => 'prosinca'
		];
	}


	private function generateLastTwoDigitsDeclinated() {
		$suffixes = [
			'0' => 'e',
			'1' => 'prve',
			'2' => 'druge',
			'3' => 'treće',
			'4' => 'četvrte',
			'5' => 'pete',
			'6' => 'šeste',
			'7' => 'sedme',
			'8' => 'osme',
			'9' => 'devete'
		];

		$prefixes = [
			'2' => 'dvadeset',
			'3' => 'trideset',
			'4' => 'četrdeset',
			'5' => 'pedeset',
			'6' => 'šezdeset',
			'7' => 'sedamdeset',
			'8' => 'osamdeset',
			'9' => 'devedeset'
		];

		$special_cases = [
			10 => 'desete',
			11 => 'jedanaeste',
			12 => 'dvanaeste',
			13 => 'trinaeste',
			14 => 'četrnaeste',
			15 => 'petnaeste',
			16 => 'šesnaeste',
			17 => 'sedamnaeste',
			18 => 'osamnaeste',
			19 => 'devetnaeste'
		];

		$last_two_digits_declinated = [];

		foreach ($suffixes as $last_digit => $suffix) {
			$last_two_digits_declinated[intval($last_digit)] = $suffix;
		}

		$last_two_digits_declinated += $special_cases;

		foreach ($prefixes as $tens => $prefix) {
			foreach ($suffixes as $last_digit => $suffix) {
				$number = intval($tens . $last_digit);
				$last_two_digits_declinated[$number] = $suffix !== "e" ? "$prefix $suffix" : "$prefix$suffix";
			}
		}

		return $last_two_digits_declinated;
	}
}
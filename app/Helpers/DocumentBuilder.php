<?php

namespace App\Helpers;

use App\Helpers\DocumentPDF as PDF;
use App\Models\Document;
use Illuminate\Support\Str;
use View;

class DocumentBuilder {

	private array $data;
	private string $layout;
	private string $data_view;
	private string $template_title;

	private int $current_article_index;
	private array $requested_inputs = [];

	private mixed $preview;  // section view on which preview is being generated
    private bool $is_initial_preview = false;

    public string $preview_layout;
    public string $template_layout;

	public array $skipped_articles = [];

	public Document $document;

	public string $render_view;

	// written in place of null values
	public static string $placeholder = '<span class="document_placeholder">_______________</span>';

	public function __construct(Document $document, mixed $preview = null) {

		$this->document       = $document;

        // eager load document parties
        $document->load('parties');

		$this->preview        = $preview;
		$this->data           = $document->values();
		$this->template_title = $document->template->public_title;

        $this->preview_layout = 'tenant.layouts.document.preview';
        $this->template_layout = 'tenant.layouts.document.template';

        if(tenancy()->initialized) {
            $tenant = tenancy()->tenant->id;
            $this->data_view   = "tenant.documents.{$tenant}.{$document->template->title}.template.data";
            $this->render_view = "tenant.documents.{$tenant}.{$document->template->title}.template.render";
        } else {
            $this->data_view   = "documents.{$document->template->title}.template.data";
            $this->render_view = "documents.{$document->template->title}.template.render";
        }

		$this->layout = $preview ? $this->preview_layout : $this->template_layout;
	}

	public function stream($watermark = "PRIMJER") {

		if ($template = $this->loadTemplate($watermark)) {

			// disable copy, print, etc.
			$template->SetProtection([], '', 'previewModeProtection');

			return $template->stream($this->template_title . '.pdf');
		}

		return null;
	}

	public function loadTemplate($watermark = null): DocumentPDF {

		return new PDF(
			$this->getContent(),
			['title' => $this->document->title],
			$watermark
		);
	}

	public function generateNewContent(): string {
		return View::make(
			$this->data_view,
			["builder" => $this]
		)->render();
	}

	public function getContent() {

		// if content exists in database, fetch it from database, otherwise generate from files
		if ($this->document->content()->exists()) {
			return $this->appendSignatures(
				$this->document->content->html
			);
		}

		return $this->appendSignatures(
			$this->generateNewContent()
		);
	}

	public function getPreviewContent(): string {
		return $this->appendSignatures(
			$this->generateNewContent()
		);
	}

	public function getParties() {
		// if ajax preview, fetch parties from user input
		if($this->isPreview() && !$this->isInitialPreview() && !empty($this->get('parties', false))) {
			return collect(
				json_decode(
					$this->get('parties', false, false),
					true)
			);
		} else {
			//otherwise, fetch parties from database
			return $this->document->parties;
		}
	}

	public function getPartyLabel($party, $default) {

		if($this->isPreview() && !$this->isInitialPreview() && !empty($this->get('parties', false))) {
			$label = !empty($party['name']) ? $party['label'] : $default;
		} else {
			$label = !empty($party['name']) ? \Crypt::decrypt($party['label']) : $default;
		}

		// replace <br> with a unique placeholder
		$label = Str::replace(['<br>', '<br/>', '<br />'], '[TEMP_BR]', $label);

		// escape the string
		$label = StringHelper::escape($label);

		// replace the placeholder back to <br>
		return Str::replace('[TEMP_BR]', '<br>', $label);
	}

	private function appendSignatures($base_content) {

		$parties = $this->getParties();

		$left_parties  = $parties->where('side', 'left')->values()->toArray();
		$right_parties = $parties->where('side', 'right')->values()->toArray();

		$is_preview_layout = $this->getLayout() === $this->preview_layout;

		if(!empty($left_parties) || !empty($right_parties)) {
			foreach ($left_parties as $_i => $_left_party) {

				$_signature = ! empty($_left_party['signature']) ?
					storage_path('app') . '/' . $_left_party['signature']
					: ($is_preview_layout ? '/img/signature_placeholder.png' : public_path('/img/signature_placeholder.png'));

				$base_content = str_replace("{L$_i}", $_signature, $base_content);
			}

			foreach ($right_parties as $_i => $_right_party) {

				$_signature = ! empty($_right_party['signature']) ?
					storage_path('app') . '/' . $_right_party['signature']
					: ($is_preview_layout ? '/img/signature_placeholder.png' : public_path('/img/signature_placeholder.png'));

				$base_content = str_replace("{R$_i}", $_signature, $base_content);
			}
		} else {
			$base_content = str_replace("{L0}", ($is_preview_layout ? '/img/signature_placeholder.png' : public_path('/img/signature_placeholder.png')), $base_content);
			$base_content = str_replace("{R0}", ($is_preview_layout ? '/img/signature_placeholder.png' : public_path('/img/signature_placeholder.png')), $base_content);
		}


		return $base_content;
	}

    public function get($name, $use_placeholder = true, $escape = true) {
        // store requested input
        if (!in_array($name, $this->requested_inputs, true)) {
            $this->requested_inputs[] = $name;
        }

        // get all data
        $all_data = array_reduce($this->data, function ($carry, $item) {
            return array_merge($carry, $item);
        }, []);

        // if exists - escape value (to prevent XSS)
        if (isset($all_data[$name])) {
            return $escape ? StringHelper::escape($all_data[$name]) : $all_data[$name];
        }

        // return placeholder or null if data is not found
        return $use_placeholder ? $this::$placeholder : null;
    }

	public function getRequestedInputs(): array {

		return $this->requested_inputs;
	}

	public function getArticleIndex($i, $is_header = false): int {

		$cnt = 0;

		foreach ($this->skipped_articles as $_i) {
			if ($_i < $i) {
				$cnt ++;
			}
		}

		if ($is_header) {
			$this->current_article_index = $i - $cnt;
		}

		return $i - $cnt;
	}

	public function getCurrentArticleIndex(): int {

		return $this->current_article_index;
	}

	public function updateSectionValues(int $section_id, array $data): static {
		$this->data[$section_id] = $data;
		return $this;
	}

	public function getLayout(): string {
		return $this->layout;
	}

	public function setLayout(string $layout): static {
		$this->layout = $layout;
		return $this;
	}

	public function isPreview(): bool {
		return !empty($this->preview);
	}

	// is non-ajax (preloaded) section preview?
    public function setInitialPreview(bool $flag = true): static {
        $this->is_initial_preview = $flag;
        return $this;
    }

	public function isInitialPreview(): bool {
		return $this->is_initial_preview;
	}

	public function partiesEmpty(): bool {
		if ($this->isPreview()) {
			if ($this->isInitialPreview()) {
				return $this->document->parties->isEmpty();
			} else if ($this->preview !== 'parties') {
				return $this->document->parties->isEmpty();
			}
		} else {
			return $this->document->parties->isEmpty();
		}

		return false;
	}

}

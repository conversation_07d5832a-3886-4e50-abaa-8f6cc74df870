<?php

/**
 * DocumentDasher Helper Class
 *
 * This class is designed to process HTML content and generate a PDF with dynamic
 * formatting features. It automates tasks such as splitting paragraphs, adding
 * dashes to content for alignment, and applying custom JavaScript-based logic
 * to the document. Leveraging <PERSON><PERSON>'s Browsershot package, it handles PDF creation
 * with options for custom dimensions, headers, and footers based on tenant data.
 *
 * Key Features:
 * - Dynamically generates headers and footers with logos and text.
 * - Adjusts page margins automatically based on the presence of a header.
 * - Dynamically adds dashes for proper paragraph alignment in generated PDFs.
 * - Supports custom JavaScript execution to manipulate HTML before rendering.
 *
 * Usage:
 * Instantiate the class with HTML content, the desired file path, and the tenant object.
 * Call the save() method to render and save the PDF with the specified configuration.
 *
 * @package App\Helpers
 */

namespace App\Helpers;

use Spa<PERSON>\Browsershot\Browsershot;
use <PERSON><PERSON>\Browsershot\Exceptions\CouldNotTakeBrowsershot;

class DocumentDasher
{
    private const PDF_DIMENSIONS = ['width' => 816, 'height' => 1000];

    private string $html;
    private string $path;
    private ?object $tenant;

    /* ---------- HEADER & FOOTER DATA ---------- */
    private ?string $headerLogo;
    private ?string $headerLogoPos;
    private ?string $headerText;
    private ?string $headerTextPos;
    private ?string $footerLogo;
    private ?string $footerLogoPos;
    private ?string $footerText;
    private ?string $footerTextPos;
    private bool $hasHeaderContent = false;
    private bool $hasFooterContent = false;

    /**
     * The constructor for the DocumentDasher.
     *
     * @param string $html The HTML content to be processed.
     * @param string $path The file path where the PDF will be saved.
     */
    public function __construct(string $html, string $path)
    {
        $this->html = $html;
        $this->path = $path;
        $this->tenant = tenant();

        $this->initializeHeaderFooterData();
    }

    /**
     * Initializes header and footer properties from the tenant object.
     */
    private function initializeHeaderFooterData(): void
    {
        if (!$this->tenant) {
            return;
        }

        /* ---------- HEADER DATA ---------- */
        $this->headerLogo = $this->tenant->headerLogoPath;
        $this->headerLogoPos = $this->tenant->headerLogoPosition ?? ($this->headerLogo ? 'left' : null);
        $this->headerText = $this->tenant->headerText;
        $this->headerTextPos = $this->tenant->headerTextPosition;

        /* ---------- FOOTER DATA ---------- */
        $this->footerLogo = $this->tenant->footerLogoPath;
        $this->footerLogoPos = $this->tenant->footerLogoPosition ?? ($this->footerLogo ? 'left' : null);
        $this->footerText = $this->tenant->footerText;
        $this->footerTextPos = $this->tenant->footerTextPosition;

        /* ---------- FLAGS ---------- */
        $this->hasHeaderContent = $this->headerLogo || $this->headerText;
        $this->hasFooterContent = $this->footerLogo || $this->footerText;
    }


    /**
     * @throws CouldNotTakeBrowsershot
     */
    public function save(): void
    {
        $js_code = $this->getJsCode();

        // clean up the HTML
        $this->cleanHtml();

        $rendered_html = Browsershot::html($this->html)
            ->windowSize(self::PDF_DIMENSIONS['width'], self::PDF_DIMENSIONS['height'])
            ->noSandbox()
            ->evaluate($js_code);

        $footer_html = $this->getFooterHtml();
        $header_html = $this->getHeaderHtml();

        // Dynamic margins based on header/footer presence, similar to blade logic
        $margins = [
            'top'    => $this->hasHeaderContent ? '30mm' : '20mm', // More space if header exists
            'bottom' => $this->hasFooterContent ? '20mm' : '15mm', // More space if footer exists
            'left'   => '20mm',
            'right'  => '20mm'
        ];

        // generate and save the PDF
        Browsershot::html($rendered_html)
            ->waitUntilNetworkIdle()
            ->windowSize(self::PDF_DIMENSIONS['width'], self::PDF_DIMENSIONS['height'])
            ->noSandbox()
            ->headerHtml($header_html)
            ->footerHtml($footer_html)
            ->setOption('displayHeaderFooter', true)
            ->setOption('margin', $margins)
            ->savePdf($this->path);
    }

    private function cleanHtml(): void
    {
        // decode HTML entities and replace multiple spaces with a single space
        $this->html = preg_replace('/[ \xa0]{2,}/u', ' ', html_entity_decode($this->html));
    }

    private function getJsCode(): string
    {
        return <<<JS
            // select appropriate paragraph selector based on whether the document is contract or statement
            let paragraphSelector = document.querySelector('.editable-segment[data-type="body"]') ?
                                    '.editable-segment[data-type="body"]' : '.article-body';

            // process all paragraphs and add dashes where necessary
            function processParagraphs(selector) {
                document.querySelectorAll(selector + ' p').forEach(splitParagraphsByBreaks);
                document.querySelectorAll(selector + ' p').forEach(addDashesToElement);
            }

            // process all list items and add dashes where necessary
            function processListItems(selector) {
                document.querySelectorAll(selector + ' li').forEach(addDashesToListItem);
            }

            // split <br> tags into separate paragraphs
            function splitParagraphsByBreaks(paragraph) {
                let content = paragraph.innerHTML.replace(/(<span[^>]+data-toggle="tooltip"[^>]*>.*?<\/span>)|(<br\s*\/?>)+/gi, function(match, tooltipSpan) {
                    return tooltipSpan || '###BREAK###';
                });

                let breakParagraphs = content.split('###BREAK###').filter(section => section.trim().length);
                if (breakParagraphs.length > 1) {
                    breakParagraphs.forEach(section => {
                        let newP = document.createElement('p');
                        newP.innerHTML = section.trim();
                        paragraph.parentNode.insertBefore(newP, paragraph);
                    });
                    paragraph.parentNode.removeChild(paragraph);
                }
            }

            // add dashes to any block-level element (paragraph or list item)
            function addDashesToElement(element) {
                let initialHeight = element.offsetHeight;
                let dashSpan = document.createElement('span');
                dashSpan.className = 'dash';
                element.appendChild(dashSpan);

                // append dashes until height changes
                while (element.offsetHeight === initialHeight) dashSpan.textContent += '-';
                if (element.offsetHeight > initialHeight) dashSpan.textContent = dashSpan.textContent.slice(0, -1);
                if (dashSpan.textContent === '') element.removeChild(dashSpan);
            }

            // add dashes to list items
            function addDashesToListItem(li) {
                let initialHeight = li.offsetHeight;

                // process each text node in reverse order to avoid issues with DOM changes
                Array.from(li.childNodes).reverse().forEach(function(childNode) {
                    if (childNode.nodeType === Node.TEXT_NODE) appendDashesToTextNode(childNode, li, initialHeight);
                });
            }

            // append dashes to text nodes inside list items
            function appendDashesToTextNode(node, li, initialHeight) {
                let dashSpan = document.createElement('span');
                dashSpan.className = 'dash';
                node.parentNode.insertBefore(dashSpan, node.nextSibling);

                while (li.offsetHeight === initialHeight) dashSpan.textContent += '-';
                if (li.offsetHeight > initialHeight) dashSpan.textContent = dashSpan.textContent.slice(0, -1);
                if (dashSpan.textContent === '') node.parentNode.removeChild(dashSpan);
            }

            // process parties section if present
            function processParties() {
                let parties = document.querySelector('.editable-segment[data-type="parties"]');
                if (parties) {
                    parties.querySelectorAll('p').forEach(splitParagraphsByBreaks);
                    parties.querySelectorAll('p').forEach(addDashesToElement);
                    parties.querySelectorAll('li').forEach(addDashesToListItem);
                }
            }

            // execute the functions to process content
            processParagraphs(paragraphSelector);
            processListItems(paragraphSelector);
            processParties();

            document.documentElement.outerHTML;
        JS;
    }

    /**
     * Builds the footer HTML based on tenant settings.
     *
     * @return string The HTML for the footer.
     */
    private function getFooterHtml(): string
    {
        $footerLogoLeft = ($this->footerLogo && $this->footerLogoPos === 'left') ?
            '<img src="' . $this->getImageAsBase64(tenant_asset($this->footerLogo)) . '" style="max-height:40px;" alt="Logo">' :
            '';

        $footerTextLeft = ($this->footerText && $this->footerTextPos === 'left') ?
            '<small>' . nl2br(e($this->footerText)) . '</small>' :
            '';

        $footerLogoCenter = ($this->footerLogo && $this->footerLogoPos === 'center') ?
            '<img src="' . $this->getImageAsBase64(tenant_asset($this->footerLogo)) . '" style="max-height:40px;" alt="Logo">' :
            '';
        $footerTextCenter = ($this->footerText && $this->footerTextPos === 'center') ?
            '<small>' . nl2br(e($this->footerText)) . '</small>' :
            '';

        return <<<HTML
            <style>
                .footer-container { width: 100%; font-family: Arial, sans-serif; color: #373737; }
                .footer-container table { width: 100%; border-spacing: 0; }
                .footer-container td { vertical-align: top; font-size: 9pt; }
            </style>
            <div class="footer-container">
                <hr style="border: 0; border-top: 1px solid #c0c0c0;"/>
                <table>
                    <tr>
                        <td style="width: 33%; text-align: left;">
                            {$footerLogoLeft}
                            {$footerTextLeft}
                        </td>
                        <td style="width: 34%; text-align: center;">
                            {$footerLogoCenter}
                            {$footerTextCenter}
                        </td>
                        <td style="width: 33%; text-align: right;">
                             <small>Stranica <span class="pageNumber"></span> od <span class="totalPages"></span></small>
                        </td>
                    </tr>
                </table>
            </div>
        HTML;
    }

    /**
     * Builds the header HTML based on tenant settings.
     *
     * @return string The HTML for the header.
     */
    private function getHeaderHtml(): string
    {
        if (!$this->hasHeaderContent) {
            return '';
        }

        $headerLogoLeft = ($this->headerLogo && $this->headerLogoPos === 'left') ?
            '<img src="' . $this->getImageAsBase64(tenant_asset($this->headerLogo)) . '" style="max-width:250px;" alt="Logo">' :
            '';
        $headerTextLeft = ($this->headerText && $this->headerTextPos === 'left') ?
            '<small>' . nl2br(e($this->headerText)) . '</small>' :
            '';

        $headerLogoCenter = ($this->headerLogo && $this->headerLogoPos === 'center') ?
            '<img src="' . $this->getImageAsBase64(tenant_asset($this->headerLogo)) . '" style="max-width:250px;" alt="Logo">' :
            '';
        $headerTextCenter = ($this->headerText && $this->headerTextPos === 'center') ?
            '<small>' . nl2br(e($this->headerText)) . '</small>' :
            '';

        $headerLogoRight = ($this->headerLogo && $this->headerLogoPos === 'right') ?
            '<img src="' . $this->getImageAsBase64(tenant_asset($this->headerLogo)) . '" style="max-width:250px;" alt="Logo">' :
            '';
        $headerTextRight = ($this->headerText && $this->headerTextPos === 'right') ?
            '<small>' . nl2br(e($this->headerText)) . '</small>' :
            '';


        return <<<HTML
            <style>
                .header-container { width: 100%; font-family: Arial, sans-serif; }
                .header-container table { width: 100%; border-spacing: 0; }
                .header-container td { vertical-align: top; font-size: 9pt; }
            </style>
            <div class="header-container">
                <table>
                    <tr>
                        <td style="width: 33%; text-align: left;">
                           {$headerLogoLeft}
                           {$headerTextLeft}
                        </td>
                        <td style="width: 34%; text-align: center;">
                           {$headerLogoCenter}
                           {$headerTextCenter}
                        </td>
                        <td style="width: 33%; text-align: right;">
                           {$headerLogoRight}
                           {$headerTextRight}
                        </td>
                    </tr>
                </table>
                <hr style="border: 0; border-top: 1px solid #c0c0c0; margin-top: 5px;"/>
            </div>
        HTML;
    }

    /**
     * Fetches an image from a URL and returns it as a Base64 encoded data URI.
     *
     * @param string|null $url The full URL of the image.
     * @return string The base64 encoded image data URI, or an empty string on failure.
     */
    private function getImageAsBase64(?string $url): string
    {
        if (empty($url)) {
            return '';
        }

        // Use error suppression to handle cases where the URL is invalid or inaccessible
        $imageData = @file_get_contents($url);
        if ($imageData === false) {
            // Optionally log the error, e.g., Log::warning("Could not fetch image: {$url}");
            return '';
        }

        $finfo = new \finfo(FILEINFO_MIME_TYPE);
        $mimeType = $finfo->buffer($imageData);

        // Fallback for SVGs or other types finfo might not get from buffer
        if (!$mimeType || $mimeType === 'text/plain') {
            $mimeType = 'image/' . pathinfo($url, PATHINFO_EXTENSION);
        }

        return 'data:' . $mimeType . ';base64,' . base64_encode($imageData);
    }


    /**
     * @throws CouldNotTakeBrowsershot
     */
    private function screenshot(): void
    {
        Browsershot::html($this->html)
            ->windowSize(self::PDF_DIMENSIONS['width'], self::PDF_DIMENSIONS['height'])
            ->save('screenshot.png');
    }
}

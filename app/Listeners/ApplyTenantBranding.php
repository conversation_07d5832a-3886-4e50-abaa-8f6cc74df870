<?php

namespace App\Listeners;

use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\URL;
use Stancl\Tenancy\Events\TenancyInitialized;

class ApplyTenantBranding
{
	public function handle(TenancyInitialized $event)
	{
        $tenant = $event->tenancy->tenant;

        /* -----------------------------------------------------------------
         | 1. Build the tenant’s “root URL” with the right scheme
         |------------------------------------------------------------------
        */

        $tenant_domain = $tenant->domains->first();

        if($tenant_domain) {
            $domain = $tenant_domain->domain;

            $secure = !app()->runningUnitTests();        // stay http during tests

            $scheme = $secure ? 'https' : 'http';
            $rootUrl = "{$scheme}://{$domain}";

            // Make the whole app believe this is the canonical URL
            Config::set('app.url', $rootUrl);
            URL::forceRootUrl($rootUrl);                  // affects route(), asset(), …
            URL::forceScheme($scheme);                    // keeps signedRoute() valid
        }


        /* -----------------------------------------------------------------
         | 2. Other tenant-specific branding
         |------------------------------------------------------------------*/

        // App name (optional)
        if (! empty($tenant->appName)) {
            Config::set('app.name', $tenant->appName);
        }

        // “From” address for queued mailables & notifications
        if (! empty($tenant->emailFromAddress)) {
            Config::set('mail.from.address', $tenant->emailFromAddress);
        }

        if (! empty($tenant->emailFromName)) {
            Config::set('mail.from.name', $tenant->emailFromName);
        }
	}
}

<?php

namespace App\Console\Commands;

use App\Models\Tenant;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class PrepareStorageForTesting extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'storage:clear';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clears storage before running tests';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
		// explicitly forbid running in production
		if(App::isProduction() || !Str::contains(env('APP_URL'), 'staging')) {
			return Command::FAILURE;
		}

	    // for every tenant
        foreach(Tenant::all() as $tenant) {
            tenancy()->initialize($_tenant->id);
            Storage::deleteDirectory('documents');
            Storage::deleteDirectory('editor');
        }


	    return Command::SUCCESS;
    }
}

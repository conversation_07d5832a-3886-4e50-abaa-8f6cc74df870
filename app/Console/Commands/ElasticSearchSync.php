<?php

namespace App\Console\Commands;

use App\Helpers\ElasticHelper;
use App\Models\Tenant;
use Illuminate\Console\Command;
use Stancl\Tenancy\Tenancy;

class ElasticSearchSync extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'es:sync {--full}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Syncs templates with Elastic Search';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     */
    public function handle() {
		$full = $this->option('full'); // sync central + tenant applications

        $this->syncAll($full);

	    return Command::SUCCESS;
    }


    private function syncAll($full = false): void {

        $es = new ElasticHelper();
        $es->restartIndices();

        $es->importTemplates();

		if($full) {
			// tenant applications
			foreach(Tenant::all() as $tenant) {
				// switch application context
				tenancy()->initialize($tenant);

				$es = new ElasticHelper();
				$es->restartIndices();

				$es->importTemplates();

				tenancy()->end();
			}
		}
    }
}

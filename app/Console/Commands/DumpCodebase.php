<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;

class DumpCodebase extends Command
{
	/**
	 * The name and signature of the console command.
	 *
	 * We add an optional "document" argument here. If it's not provided, we'll
	 * process the first subfolder inside resources/views/documents.
	 */
	protected $signature = 'codebase:dump {document? : If provided, process only this subfolder under resources/views/documents}';

	/**
	 * The console command description.
	 *
	 * @var string
	 */
	protected $description = 'Dump all important code files (controllers, migrations, views, etc.) into a single file for LLM analysis including MySQL schema';

	/**
	 * Execute the console command.
	 *
	 * @return int
	 */
	public function handle()
	{
		$document = $this->argument('document');

		$paths = [
			base_path('app'),
			base_path('routes'),
			base_path('resources/assets/js'),
			base_path('resources/views'),
			base_path('composer.json'),
			base_path('package.json'),
			base_path('code_style.md')
		];

		$output = '';

		foreach ($paths as $path) {
			if (!File::exists($path)) {
				$this->warn("Path does not exist: " . $path);
				continue;
			}

			// If it's a directory, determine if we need to handle it specially.
			if (File::isDirectory($path)) {
				// Special case: if it's resources/views, skip "documents" and handle it separately.
				if ($path === base_path('resources/views')) {
					// Process everything in resources/views EXCEPT the documents folder.
					$files = $this->getFilesExcludingSubdirectory($path, 'documents');
					foreach ($files as $file) {
						$this->line("Processing file: " . $file);
						$content = File::get($file);
						$output .= "----- File: " . $file . " -----\n" . $content . "\n\n";
					}

					// Now handle the "documents" folder logic.
					$documentsPath = $path . DIRECTORY_SEPARATOR . 'documents';
					if (File::exists($documentsPath) && File::isDirectory($documentsPath)) {
						if ($document) {
							// Process only the specified subfolder.
							$docPath = $documentsPath . DIRECTORY_SEPARATOR . $document;
							if (File::exists($docPath)) {
								if (File::isDirectory($docPath)) {
									$docFiles = $this->getFilesFromDirectory($docPath);
									foreach ($docFiles as $file) {
										$this->line("Processing file: " . $file);
										$content = File::get($file);
										$output .= "----- File: " . $file . " -----\n" . $content . "\n\n";
									}
								} else {
									// If it's a single file.
									$this->line("Processing file: " . $docPath);
									$content = File::get($docPath);
									$output .= "----- File: " . $docPath . " -----\n" . $content . "\n\n";
								}
							} else {
								$this->warn("Document subfolder does not exist: " . $docPath);
							}
						} else {
							// No document argument provided, so process the FIRST subfolder in documents.
							$subfolders = array_filter(glob($documentsPath . '/*'), 'is_dir');
							if (!empty($subfolders)) {
								// Grab the first subfolder.
								$firstSubfolder = array_shift($subfolders);
								$this->info("No document argument provided. Processing first subfolder: $firstSubfolder");
								$docFiles = $this->getFilesFromDirectory($firstSubfolder);
								foreach ($docFiles as $file) {
									$this->line("Processing file: " . $file);
									$content = File::get($file);
									$output .= "----- File: " . $file . " -----\n" . $content . "\n\n";
								}
							} else {
								$this->warn("No subfolders found in: " . $documentsPath);
							}
						}
					} else {
						$this->warn("Documents folder does not exist or is not a directory: " . $documentsPath);
					}
				} else {
					// For any other directory, process all files recursively.
					$files = $this->getFilesFromDirectory($path);
					foreach ($files as $file) {
						$this->line("Processing file: " . $file);
						$content = File::get($file);
						$output .= "----- File: " . $file . " -----\n" . $content . "\n\n";
					}
				}
			} else {
				// If it's a file, directly dump it.
				$this->line("Processing file: " . $path);
				$content = File::get($path);
				$output .= "----- File: " . $path . " -----\n" . $content . "\n\n";
			}
		}

		// Dump MySQL schema structure using Laravel's DB facade.
		$this->info("Dumping MySQL table structure...");
		try {
			$tables = DB::select('SHOW TABLES');
			foreach ($tables as $tableRow) {
				// Extract the table name – the key is dynamic, so get the first value.
				$table = array_values((array)$tableRow)[0];

				// Retrieve the CREATE statement for the table.
				$result = DB::select("SHOW CREATE TABLE `$table`");
				if (isset($result[0]->{'Create Table'})) {
					$createSql = $result[0]->{'Create Table'};
					$output .= "----- Table Structure: $table -----\n" . $createSql . "\n\n";
					$this->line("Dumped table: " . $table);
				} else {
					$this->warn("Could not dump schema for table: " . $table);
				}
			}
		} catch (\Exception $e) {
			$this->error("Error retrieving table structures: " . $e->getMessage());
		}

		// Write the complete output into codebase.txt.
		$destination = storage_path('codebase.txt');
		File::put($destination, $output);

		$this->info("Codebase dump created at: " . $destination);
		return 0;
	}

	/**
	 * Recursively retrieve all file paths from the given directory.
	 *
	 * @param string $directory
	 * @return array
	 */
	protected function getFilesFromDirectory($directory)
	{
		$files = [];
		$allFiles = File::allFiles($directory);
		foreach ($allFiles as $file) {
			$files[] = $file->getPathname();
		}
		return $files;
	}

	/**
	 * Retrieve all file paths from the given directory,
	 * excluding anything under a specified subdirectory.
	 *
	 * @param string $directory
	 * @param string $excludeSubdir
	 * @return array
	 */
	protected function getFilesExcludingSubdirectory($directory, $excludeSubdir)
	{
		$files = [];
		$excludePath = rtrim($directory, DIRECTORY_SEPARATOR) . DIRECTORY_SEPARATOR . $excludeSubdir;

		// Retrieve all files recursively.
		$allFiles = File::allFiles($directory);

		// Skip files that reside under the excluded directory.
		foreach ($allFiles as $file) {
			if (strpos($file->getPathname(), $excludePath) === false) {
				$files[] = $file->getPathname();
			}
		}

		return $files;
	}
}

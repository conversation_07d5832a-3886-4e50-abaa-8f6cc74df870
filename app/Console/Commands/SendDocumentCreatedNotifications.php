<?php

namespace App\Console\Commands;

use App\Exceptions\SkipNotificationException;
use App\Models\Document;
use App\Notifications\DocumentCreated;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Notification;

class SendDocumentCreatedNotifications extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sendDocumentCreatedNotifications';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sends notifications for newly created documents.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
	    $unsent_documents = Document::with(['user'])->whereNotNull('user_id')->where([
			'is_sent' => 0,
		    'is_visible' => 1
	    ])->get();

		foreach($unsent_documents as $_document) {
            // send notification if user still exists
            if($_document->user){
	            $notification = new DocumentCreated($_document);
	            Notification::route('mail', $_document->user->email)->notify($notification);
            }

			// set flag to sent
			$_document->is_sent = 1;
			$_document->save();
		}

	    return Command::SUCCESS;
    }
}

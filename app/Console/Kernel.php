<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use Illuminate\Support\Facades\Log;
use Propaganistas\LaravelDisposableEmail\Console\UpdateDisposableDomainsCommand;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        UpdateDisposableDomainsCommand::class
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        // skip if dusk tests are currently running
        if (config('app.dusk_test') !== true) {

            $schedule->command('queue:work --stop-when-empty')->everyMinute();

	        if(app()->environment('production')) {
		        $schedule->command('sitemap:generate')->dailyAt('01:00');
		        $schedule->command('activitylog:clean')->dailyAt('04:00');
		        $schedule->call(function(){\Artisan::call('disposable:update');})->dailyAt('05:00');
	        }

	        $schedule->command('sendDocumentCreatedNotifications')->everyMinute()->withoutOverlapping();

        } else {
            Log::info('Scheduler skipped due to Dusk testing.');
        }
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}

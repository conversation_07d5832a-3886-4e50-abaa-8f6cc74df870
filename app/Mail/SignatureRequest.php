<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Queue\ShouldQueue;

class SignatureRequest extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $document;
    public $link;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($document, $link)
    {
        $this->document = $document;
        $this->link = $link;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->view('emails.signature_request')
            ->with(['document' => $this->document, 'link' => $this->link]);
    }
}

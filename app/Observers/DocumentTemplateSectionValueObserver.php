<?php

namespace App\Observers;

use App\Models\DocumentTemplateSectionValue;
use App\Models\User;

class DocumentTemplateSectionValueObserver
{
    /**
     * Handle the DocumentTemplateSectionValue "created" event.
     *
     * @param DocumentTemplateSectionValue $documentTemplateSectionValue
     *
     * @return void
     */
    public function created(DocumentTemplateSectionValue $documentTemplateSectionValue)
    {
	    // log user activity - document updated (because it touches Document)
	    activity()->performedOn($documentTemplateSectionValue->document)->withProperties([
		    'agent' => request()->userAgent()
	    ])->log('Update');
    }

    /**
     * Handle the DocumentTemplateSectionValue "updated" event.
     *
     * @param DocumentTemplateSectionValue $documentTemplateSectionValue
     *
     * @return void
     */
    public function updated(DocumentTemplateSectionValue $documentTemplateSectionValue)
    {
		// log user activity - document updated (because it touches Document)
	    activity()->performedOn($documentTemplateSectionValue->document)->withProperties([
		    'agent' => request()->userAgent()
	    ])->log('Update');
    }

    /**
     * Handle the DocumentTemplateSectionValue "deleted" event.
     *
     * @param DocumentTemplateSectionValue  $documentTemplateSectionValue
     *
     * @return void
     */
    public function deleted(DocumentTemplateSectionValue $documentTemplateSectionValue)
    {
        //
    }

    /**
     * Handle the DocumentTemplateSectionValue "restored" event.
     *
     * @param DocumentTemplateSectionValue  $documentTemplateSectionValue
     *
     * @return void
     */
    public function restored(DocumentTemplateSectionValue $documentTemplateSectionValue)
    {
        //
    }

    /**
     * Handle the DocumentTemplateSectionValue "force deleted" event.
     *
     * @param DocumentTemplateSectionValue  $documentTemplateSectionValue
     *
     * @return void
     */
    public function forceDeleted(DocumentTemplateSectionValue $documentTemplateSectionValue)
    {
        //
    }
}

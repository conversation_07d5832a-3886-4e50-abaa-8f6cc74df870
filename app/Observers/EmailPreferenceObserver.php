<?php

namespace App\Observers;

use App\Models\EmailPreference;

class EmailPreferenceObserver
{
    /**
     * Handle the EmailPreference "created" event.
     *
     * @param EmailPreference $emailPreference
     *
     * @return void
     */
    public function created(EmailPreference $emailPreference)
    {
        activity()->performedOn($emailPreference)->withProperties(
            $emailPreference->makeHidden(['id', 'created_at', 'updated_at'])->toArray()
        )->log('Create');
    }

    /**
     * Handle the EmailPreference "updated" event.
     *
     * @param EmailPreference $emailPreference
     *
     * @return void
     */
    public function updated(EmailPreference $emailPreference)
    {
        activity()->performedOn($emailPreference)->withProperties(
            $emailPreference->makeHidden(['id', 'created_at', 'updated_at'])->toArray()
        )->log('Update');
    }

    /**
     * Handle the EmailPreference "deleted" event.
     *
     * @param EmailPreference  $emailPreference
     *
     * @return void
     */
    public function deleted(EmailPreference $emailPreference)
    {
        //
    }

    /**
     * Handle the EmailPreference "restored" event.
     *
     * @param EmailPreference  $emailPreference
     *
     * @return void
     */
    public function restored(EmailPreference $emailPreference)
    {
        //
    }

    /**
     * Handle the EmailPreference "force deleted" event.
     *
     * @param  EmailPreference  $emailPreference
     *
     * @return void
     */
    public function forceDeleted(EmailPreference $emailPreference)
    {
        //
    }
}

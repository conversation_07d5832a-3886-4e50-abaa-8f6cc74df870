<?php

namespace App\Observers;

use App\Models\SignatureRequest;

class SignatureRequestObserver
{
    /**
     * Handle the SignatureRequest "created" event.
     *
     * @param SignatureRequest $signatureRequest
     *
     * @return void
     */
    public function created(SignatureRequest $signatureRequest)
    {
	    activity()->performedOn($signatureRequest)->withProperties(
		    $signatureRequest->makeHidden(['id', 'created_at', 'updated_at', 'deleted_at'])->toArray()
	    )->log('Create');
    }

    /**
     * Handle the SignatureRequest "updated" event.
     *
     * @param SignatureRequest $signatureRequest
     *
     * @return void
     */
    public function updated(SignatureRequest $signatureRequest)
    {
	    activity()->performedOn($signatureRequest)->withProperties(
		    $signatureRequest->makeHidden(['id', 'created_at', 'updated_at', 'deleted_at'])->toArray()
	    )->log('Update');
    }

    /**
     * Handle the SignatureRequest "deleted" event.
     *
     * @param SignatureRequest  $signatureRequest
     *
     * @return void
     */
    public function deleted(SignatureRequest $signatureRequest)
    {
	    activity()->performedOn($signatureRequest)->log('Delete');
    }

    /**
     * Handle the SignatureRequest "restored" event.
     *
     * @param SignatureRequest  $signatureRequest
     *
     * @return void
     */
    public function restored(SignatureRequest $signatureRequest)
    {
        //
    }

    /**
     * Handle the SignatureRequest "force deleted" event.
     *
     * @param  SignatureRequest  $signatureRequest
     *
     * @return void
     */
    public function forceDeleted(SignatureRequest $signatureRequest)
    {
        //
    }
}

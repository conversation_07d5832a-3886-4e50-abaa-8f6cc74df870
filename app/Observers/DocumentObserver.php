<?php

namespace App\Observers;

use App\Models\Document;
use App\Models\User;

class DocumentObserver
{
    /**
     * Handle the Document "created" event.
     *
     * @param Document $document
     *
     * @return void
     */
    public function created(Document $document)
    {
	    activity()->performedOn($document)->withProperties([
		    'agent' => request()->userAgent()
	    ])->log('Create');
    }

    /**
     * Handle the Document "updated" event.
     *
     * @param Document $document
     *
     * @return void
     */
    public function updated(Document $document)
    {
		 // logging of this activity is performed in DocumentTemplateSectionValueObserver
    }

    /**
     * Handle the Document "deleted" event.
     *
     * @param Document  $document
     *
     * @return void
     */
    public function deleted(Document $document)
    {
	    activity()->performedOn($document)->log('Delete');
    }

    /**
     * Handle the Document "restored" event.
     *
     * @param  Document  $document
     *
     * @return void
     */
    public function restored(Document $document)
    {
        //
    }

    /**
     * Handle the Document "force deleted" event.
     *
     * @param  Document  $document
     *
     * @return void
     */
    public function forceDeleted(Document $document)
    {
        //
    }
}

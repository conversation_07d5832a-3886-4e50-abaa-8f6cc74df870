<?php

namespace App\Exceptions;

use App\Models\User;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Support\Facades\Route;
use Log;
use Throwable;
use Symfony\Component\ErrorHandler\ErrorRenderer\HtmlErrorRenderer;
use Symfony\Component\ErrorHandler\Exception\FlattenException;

class <PERSON>ler extends ExceptionHandler {

	/**
	 * A list of the exception types that are not reported.
	 *
	 * @var array
	 */
	protected $dontReport = [
		SkipNotificationException::class
	];

	/**
	 * A list of the inputs that are never flashed for validation exceptions.
	 *
	 * @var array
	 */
	protected $dontFlash = [
		'password',
		'password_confirmation',
	];

	/**
	 * Report or log an exception.
	 *
	 * @param \Throwable $exception
	 *
	 * @return void
	 *
	 * @throws \Exception
	 */
	public function report(Throwable $exception) {

		parent::report($exception);

		// emails.exception is the template of your email
		// it will have access to the $error that we are passing below

		if ($this->shouldReport($exception)) {
			$this->sendEmail($exception); // sends an email
		}

	}

	protected function context() {
		// get the default context from the parent class
		$context = parent::context();

		// add the client's IP address to the context
		$context['client_ip'] = request()->ip();

		// add serialized request to the context
		$context['request'] = request()->all();

		return $context;
	}

	/**
	 * Render an exception into an HTTP response.
	 *
	 * @param \Illuminate\Http\Request $request
	 * @param \Throwable $exception
	 *
	 * @return \Symfony\Component\HttpFoundation\Response
	 *
	 * @throws \Throwable
	 */
	public function render($request, Throwable $exception) {

		// redirect to "documents" route if model bindings failed to resolve for document download (old email links)
		if ($exception instanceof ModelNotFoundException) {
			if (Route::currentRouteName() === 'document.download') {
				return redirect()->route('documents');
			}
		}

		return parent::render($request, $exception);
	}

	public function sendEmail(Throwable $exception) {

		try {
			if ( ! \App::isLocal()) {
				$e       = FlattenException::create($exception);
				$handler = new HtmlErrorRenderer(true);
				$css     = $handler->getStylesheet();
				$content = $handler->getBody($e);

				\Mail::send('emails.exception', compact('css', 'content'), function ($message) {

					$message->to([env('DEVELOPER_EMAIL')])
					        ->subject('Legaldesk Exception: ' . request()->fullUrl());
				});
			}

		} catch (Throwable $exception) {
			Log::error($exception);
		}
	}


}

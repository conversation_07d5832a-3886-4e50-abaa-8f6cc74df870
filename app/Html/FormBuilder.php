<?php

namespace App\Html;

use Illuminate\Support\HtmlString;

class FormBuilder
{
	public function model($model, $attributes)
	{
		return html()->modelForm($model, $attributes['method'] ?? 'POST', $attributes['url'])->attributes($attributes)->open();
	}

	public function open($action, $method = 'POST', $attributes = [])
	{
		return html()->form($method, $action)->attributes($attributes)->open();
	}

	public function close()
	{
		return html()->closeModelForm();
	}

    public function fLabel($model, $for, $label, $tooltip = null, $tooltip_padding = null, $image_tooltip = null)
    {
        $data = compact('model', 'for', 'label', 'tooltip', 'tooltip_padding', 'image_tooltip');
        return new HtmlString(view('tenant.components.form.label', $data)->render());
    }

    public function fText($model, $name, $value = null, $label = null, $attributes = [], $addon = null, $tooltip = null, $tooltip_padding = null, $image_tooltip = null, $image_tooltip_explanation = null)
    {
        $data = compact( 'model', 'name', 'value', 'label', 'attributes', 'addon', 'tooltip', 'tooltip_padding', 'image_tooltip', 'image_tooltip_explanation');
        return new HtmlString(view('tenant.components.form.text', $data)->render());
    }

    public function fTextArea($model, $name, $value = null, $label = null, $attributes = [], $tooltip = null, $tooltip_padding = null, $image_tooltip = null, $image_tooltip_explanation = null)
    {
        $data = compact('model', 'name', 'value', 'label', 'attributes', 'tooltip', 'tooltip_padding', 'image_tooltip', 'image_tooltip_explanation');

	    $data['rows'] = $attributes['rows'] ?? 10;

        return new HtmlString(view('tenant.components.form.textArea', $data)->render());
    }

    public function fNumber($model, $name, $value = null, $label = null, $attributes = [], $addon = null, $tooltip = null, $tooltip_padding = null, $image_tooltip = null)
    {
        $data = compact('model', 'name', 'value', 'label', 'attributes', 'addon', 'tooltip', 'tooltip_padding', 'image_tooltip');
        return new HtmlString(view('tenant.components.form.number', $data)->render());
    }

    public function fCheckbox($model, $name, $value = null, $label = null, $attributes = [], $tooltip = null, $tooltip_padding = null, $image_tooltip = null)
    {
        $data = compact('model', 'name', 'value', 'label', 'attributes', 'tooltip', 'tooltip_padding', 'image_tooltip');
        return new HtmlString(view('tenant.components.form.checkbox', $data)->render());
    }

    public function fRadio($model, $name, $value = null, $label = null, $attributes = [], $tooltip = null, $tooltip_padding = null, $image_tooltip = null)
    {
        $data = compact('model', 'name', 'value', 'label', 'attributes', 'tooltip', 'tooltip_padding', 'image_tooltip');
        return new HtmlString(view('tenant.components.form.radio', $data)->render());
    }

    public function fDropdown($model, $name, $value = null, $options = [], $label = null, $attributes = [], $tooltip = null, $tooltip_padding = null, $image_tooltip = null)
    {
        $data = compact('model', 'name', 'value', 'options', 'label', 'attributes', 'tooltip', 'tooltip_padding', 'image_tooltip');

	    $data['placeholder'] = $attributes['placeholder'] ?? 'Odaberi...';

        return new HtmlString(view('tenant.components.form.dropdown', $data)->render());
    }

    public function fDropdownText($model, $label, $name, $value, $dropdown_name, $dropdown_value, $dropdown_options, $attributes = [], $tooltip = null, $tooltip_padding = null, $image_tooltip = null, $image_tooltip_explanation = null)
    {
        $data = compact('model', 'label', 'name', 'value', 'dropdown_name', 'dropdown_value', 'dropdown_options', 'attributes', 'tooltip', 'tooltip_padding', 'image_tooltip', 'image_tooltip_explanation');
        return new HtmlString(view('tenant.components.form.dropdown_text', $data)->render());
    }

	public function text($name, $value = null, $attributes = [])
	{
		return html()->text($name, $value)->attributes($attributes);
	}

	public function number($name, $value = null, $attributes = [])
	{
		// enforce only positive integers for denominator input fields
		if (str($name)->contains('denominator')) {
			$attributes = array_merge($attributes, ['oninput' => "this.value = this.value.replace(/[^0-9]/g, ''); if(parseInt(this.value) <= 0) this.value = '';"]);
		}

		return html()->number($name, $value)->attributes($attributes);
	}

	public function checkbox($name, $value, $checked, $attributes = [])
	{
		return html()->checkbox($name, $checked, $value)->attributes($attributes);
	}

	public function radio($name, $value, $checked, $attributes = [])
	{
		return html()->radio($name, $checked, $value)->attributes($attributes);
	}
}

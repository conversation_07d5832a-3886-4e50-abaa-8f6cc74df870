<?php

namespace App\Notifications;

use App\Models\EmailPreference;
use Illuminate\Auth\Notifications\ResetPassword as ResetPasswordNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;

class ResetPassword extends ResetPasswordNotification implements ShouldQueue
{
    use Queueable;

    private $resetPasswordUrl;

    /**
     * ResetPassword constructor.
     * @param $token
     */
    public function __construct($token, $resetPasswordUrl)
    {
        $this->resetPasswordUrl = $resetPasswordUrl;
        $this->token = $token;
    }

	/**
	 * Get the notification's delivery channels.
	 *
	 * @param  mixed  $notifiable
	 * @return array
	 */
	public function via(mixed $notifiable): array
	{
		return ['mail'];
	}

    public function shouldSkip(mixed $notifiable): bool
    {
        return !EmailPreference::isAllowedGeneralEmails($notifiable->email);
    }

    /**
     * Build Mail Message
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('Promjena lozinke - Legaldesk.hr')
            ->line('Za promjenu lozinke, kliknite na poveznicu ispod.')
            ->action('Promjena lozinke', $this->resetPasswordUrl)
            ->line('Vrijeme za ponovno postavljanje lozinke ističe za 60 minuta i tada poveznica više neće biti aktivna.
            Ako niste zatražili ponovno postavljanje lozinke, slobodno ignorirajte ovaj e-mail.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            //
        ];
    }
}

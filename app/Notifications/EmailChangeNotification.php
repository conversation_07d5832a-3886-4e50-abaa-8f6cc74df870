<?php

namespace App\Notifications;

use App\Exceptions\SkipNotificationException;
use App\Models\EmailPreference;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\URL;

class EmailChangeNotification extends Notification implements ShouldQueue
{
	use Queueable;

	protected int $user_id;
	protected string $email;

	/**
	 * Create a new notification instance.
	 *
	 * @param int $user_id
	 * @param string $email
	 */
	public function __construct(int $user_id, string $email)
	{
		$this->user_id = $user_id;
		$this->email = $email;
	}

	/**
	 * Get the notification's delivery channels.
	 *
	 * @param  mixed  $notifiable
	 * @return array
	 */
	public function via($notifiable): array
	{
		return ['mail'];
	}

	public function shouldSkip($notifiable)
	{
		return !EmailPreference::isAllowedGeneralEmails($this->email);
	}

	/**
	 * Build Mail Message
	 *
	 * @param  mixed  $notifiable
	 * @return \Illuminate\Notifications\Messages\MailMessage
	 */
	public function toMail($notifiable)
	{
		if($this->shouldSkip($notifiable)) {
			throw new SkipNotificationException();
		}

		$url = URL::temporarySignedRoute('tenant.email.update', Carbon::now()->addMinutes(60), [
			'user_id' => $this->user_id,
			'email' => $this->email
		]);

		$message = (new MailMessage)
			->subject(Lang::get('Potvrdi adresu e-pošte'))
			->line(Lang::get('Please click the button below to verify your email address.'))
			->line(Lang::get('The verification link expires in 60 minutes.'))
			->action(Lang::get('Verify Email Address'), $url)
			->line(Lang::get('If you did not create an account, no further action is required.'));

		$message->viewData = ['email_preferences_link' => URL::signedRoute('tenant.email.preferences.show', ['email' => $this->email])];

		return $message;
	}

}

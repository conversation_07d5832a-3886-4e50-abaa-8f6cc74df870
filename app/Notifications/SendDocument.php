<?php

namespace App\Notifications;

use App\Exceptions\SkipNotificationException;
use App\Models\Document;
use App\Models\EmailPreference;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\URL;

class SendDocument extends Notification implements ShouldQueue
{
    use Queueable;

    private Document $document;
    private string $email;

	/**
	 * SendDocument constructor.
	 *
	 * @param Document $document
	 * @param string $email
	 */
    public function __construct(Document $document, string $email)
    {
        $this->document = $document;
	    $this->email = $email;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
	public function via($notifiable): array
	{
		return ['mail'];
	}

    public function shouldSkip($notifiable)
    {
	    return !$this->document->user || !EmailPreference::isAllowedGeneralEmails($this->email);
    }

    /**
     * Build Mail Message
     *
     * @param  mixed  $notifiable
     * @return MailMessage
     */
    public function toMail($notifiable)
    {
	    if($this->shouldSkip($notifiable)) {
		    throw new SkipNotificationException();
	    }

        $content = $this->document->user->name. " Vam šalje dokument preko aplikacije Legaldesk.hr: ".$this->document->template->public_title.".";

	    $message = (new MailMessage)
            ->subject( $this->document->user->name. " Vam šalje dokument: ".$this->document->template->public_title)
		    ->replyTo($this->document->user->email)
            ->greeting('Pozdrav,')
            ->line($content)
            ->action('Preuzmi dokument', URL::signedRoute('tenant.document.download', $this->document));

	    $message->viewData = ['email_preferences_link' => URL::signedRoute('tenant.email.preferences.show', ['email' => $this->email])];

	    return $message;
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            //
        ];
    }
}

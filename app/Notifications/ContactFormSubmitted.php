<?php

namespace App\Notifications;

use App\Exceptions\SkipNotificationException;
use App\Models\Contact;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\App;

class ContactFormSubmitted extends Notification implements ShouldQueue
{
    use Queueable;

    private Contact $contact;
	private array $files;

	/**
	 * ContactFormSubmitted constructor.
	 *
	 * @param Contact $contact
	 * @param array $files
	 */
    public function __construct(Contact $contact, array $files = [])
    {
        $this->contact = $contact;
		$this->files = $files;
    }

	/**
	 * Get the notification's delivery channels.
	 *
	 * @param  mixed  $notifiable
	 * @return array
	 */
	public function via($notifiable): array
	{
		return ['mail'];
	}

    /**
     * Build Mail Message
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $user = User::where('email', $this->contact->email)->get();

        $mail_message =  (new MailMessage)
	        ->from($this->contact->email)
            ->subject("Novi upit - " . Contact::$topics[$this->contact->topic_id])
            ->line("<strong>Tip upita:</strong> " . Contact::$topics[$this->contact->topic_id])
            ->line("<strong>Legaldesk korisnik:</strong> " . ($user->count() ? "Da" : "Ne"));


        if($user->count()){
            $mail_message->line("<strong>Ime:</strong> ". $user->first()->name);
        }

        $mail_message->line("<strong>Email:</strong> ".$this->contact->email);

        if($this->contact->phone){
            $mail_message->line("<strong>Kontakt:</strong> ". $this->contact->phone);
        }

	    $mail_message->line("<strong>Poruka:</strong><br/> ".nl2br($this->contact->message));

	    // append uploads
	    if(count($this->files) > 0) {
		    $mail_message->line("<strong>Priložene datoteke:</strong> <ul><li>" . implode("</li><li>", $this->files) . "</li></ul>");
	    }

		// remove salutation
	    $mail_message->salutation('none');

        return $mail_message;
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            //
        ];
    }
}

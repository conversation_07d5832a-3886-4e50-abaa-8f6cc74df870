<?php

namespace App\Notifications;

use App\Exceptions\SkipNotificationException;
use App\Models\DocumentParty;
use App\Models\EmailPreference;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\URL;

class PartySignedDocument extends Notification implements ShouldQueue
{
    use Queueable;

    private DocumentParty $party;
    private string $email;

	/**
	 * PartySignedDocument constructor.
	 *
	 * @param DocumentParty $party
	 * @param string $email
	 */
    public function __construct(DocumentParty $party, string $email)
    {
        $this->party = $party;
	    $this->email = $email;
    }

	/**
	 * Get the notification's delivery channels.
	 *
	 * @param  mixed  $notifiable
	 * @return array
	 */
	public function via($notifiable): array
	{
		return ['mail'];
	}

    public function shouldSkip($notifiable)
    {
	    return !$this->party->document || !$this->party->document->user  || !EmailPreference::isAllowedGeneralEmails($this->email);
    }

    /**
     * Build Mail Message
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
	    if($this->shouldSkip($notifiable)) {
		    throw new SkipNotificationException();
	    }

		$content = "Stranka ". $this->party->name . " je potpisala dokument: ".$this->party->document->template->public_title.".";

        $message = (new MailMessage)
            ->subject("Stranka ". $this->party->name . " je potpisala dokument: ".$this->party->document->template->public_title)
            ->greeting('Pozdrav '.$this->party->document->user->name.",")
            ->line($content)
            ->action('Pregledaj potpise', route('tenant.signature.index', $this->party->document));

	    $message->viewData = ['email_preferences_link' => URL::signedRoute('tenant.email.preferences.show', ['email' => $this->email])];

	    return $message;
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            //
        ];
    }
}

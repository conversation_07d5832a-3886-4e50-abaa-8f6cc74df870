<?php

namespace App\Notifications;

use App\Exceptions\SkipNotificationException;
use App\Models\EmailPreference;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\URL;

class EmailChangedNotification extends Notification implements ShouldQueue
{
	use Queueable;

	protected string $email;
	protected string $new_email;

	/**
	 * Create a new notification instance.
	 *
	 * @param string $email
	 * @param string $new_email
	 */
	public function __construct(string $email, string $new_email)
	{
		$this->email = $email;
		$this->new_email = $new_email;
	}

	/**
	 * Get the notification's delivery channels.
	 *
	 * @param  mixed  $notifiable
	 * @return array
	 */
	public function via($notifiable): array
	{
		return ['mail'];
	}

	public function shouldSkip($notifiable)
	{
		return !EmailPreference::isAllowedGeneralEmails($this->email);
	}

	/**
	 * Build Mail Message
	 *
	 * @param  mixed  $notifiable
	 * @return \Illuminate\Notifications\Messages\MailMessage
	 */
	public function toMail($notifiable)
	{
		if($this->shouldSkip($notifiable)) {
			throw new SkipNotificationException();
		}

		$message = (new MailMessage)
			->subject('Uredili ste adresu e-pošte')
			->line('Uspješno ste uredili adresu e-pošte za Vaš račun na Legaldesku.')
			->line('Nova adresa je: '.$this->new_email)
			->line('Ako niste uređivali adresu e-pošte, ili smatrate da se radi o pogrešci, molimo da nas hitno kontaktirate.')
			->action('Kontaktiraj Legaldesk', URL::route('contact'));

		$message->viewData = ['email_preferences_link' => URL::signedRoute('tenant.email.preferences.show', ['email' => $this->email])];

		return $message;

	}

}

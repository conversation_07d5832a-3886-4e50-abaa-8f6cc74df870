<?php

namespace App\Notifications;

use App\Exceptions\SkipNotificationException;
use App\Models\DocumentDraft;
use App\Models\EmailPreference;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\URL;

class SendDocumentDraft extends Notification implements ShouldQueue
{
    use Queueable;

    private DocumentDraft $draft;
    private string $email;

	/**
	 * SendDocument constructor.
	 *
	 * @param DocumentDraft $draft
	 * @param string $email
	 */
    public function __construct(DocumentDraft $draft, string $email)
    {
        $this->draft = $draft;
	    $this->email = $email;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
	public function via($notifiable): array
	{
		return ['mail'];
	}

    public function shouldSkip($notifiable)
    {
	    return !$this->draft->user || !EmailPreference::isAllowedGeneralEmails($this->email);
    }

    /**
     * Build Mail Message
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
	    if($this->shouldSkip($notifiable)) {
		    throw new SkipNotificationException();
	    }

        $content = $this->draft->user->name. " Vam šalje dokument preko aplikacije Legaldesk.hr: ".$this->draft->title.".";

	    $message = (new MailMessage)
            ->subject($this->draft->user->name. " Vam šalje dokument: ".$this->draft->title)
		    ->replyTo($this->draft->user->email)
            ->greeting('Pozdrav,')
            ->line($content)
            ->action('Preuzmi dokument', URL::signedRoute('tenant.editor.draft.download', $this->draft));

	    $message->viewData = ['email_preferences_link' => URL::signedRoute('tenant.email.preferences.show', ['email' => $this->email])];

	    return $message;
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            //
        ];
    }
}

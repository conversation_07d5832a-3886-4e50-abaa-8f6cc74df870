<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;

class AuthWithMessage {

	/**
	 * If user not authenticated, store a user message into session and redirect to login page
	 */
	public function handle(Request $request, Closure $next, $message)
	{
		if ( ! Auth::check()) {
			if ( ! empty($message)) {
				Session::put('auth_message', $message);
			}

			// Store the previous url that's being requested.
			$request->session()->put('url.intended', url()->previous());

			return redirect()->route('tenant.login');
		}

		return $next($request);
	}

}

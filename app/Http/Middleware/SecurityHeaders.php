<?php

namespace App\Http\Middleware;

use Closure;

class SecurityHeaders
{
    /**
     * Handle an incoming request.
     */
    public function handle($request, Closure $next)
    {
        $response = $next($request);

		// add security headers on non-local environments
		if(!app()->environment('local')) {

			// set strict-transport-security header
			$response->headers->set('Strict-Transport-Security', 'max-age=31536000;');

			// set x-frame-options header
			$response->headers->set('X-Frame-Options', 'SAMEORIGIN');

			// set x-content-type-options header
			$response->headers->set('X-Content-Type-Options', 'nosniff');

			// set referrer-policy header
			$response->headers->set('Referrer-Policy', 'no-referrer-when-downgrade');

			// set permissions-policy header
			$response->headers->set('Permissions-Policy', 'geolocation=(self), microphone=()');
		}

        return $response;
    }
}

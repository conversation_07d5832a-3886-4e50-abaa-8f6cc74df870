<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\App;

class Local
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \Closure $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if (App::environment() != 'production') {
            return $next($request);
        } else {
            return response('Invalid environment.', 401);
        }
    }
}

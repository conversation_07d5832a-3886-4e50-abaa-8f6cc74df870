<?php

namespace App\Http\Requests\Tenant;

use App\Rules\ValidDocumentTemplate;
use Illuminate\Foundation\Http\FormRequest;

class SelectDocumentTemplateRequest extends FormRequest {

	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize() {

		return true;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules() {

		return [
			'template_id' => ['required', new ValidDocumentTemplate()]
		];
	}

	public function messages() {

		return [
			'template_id.required' => 'Potrebno je odabrati obrazac za dokument',
		];
	}
}

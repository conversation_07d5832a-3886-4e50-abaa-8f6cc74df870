<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreContactRequest;
use App\Models\Contact;
use App\Notifications\ContactFormSubmitted;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\URL;

class ContactController extends Controller {

	public function index() {

		return view('contact.index');
	}

	public function store(StoreContactRequest $request, Contact $contact)
	{

		$contact->fill($request->validated());

		if ($contact->save()) {

			$files = [];

			// store the files
			if($request->hasFile('slike')) {
				foreach ($request->file('slike') as $_file) {
					$_sanitized_file_name = preg_replace('/[^a-zA-Z0-9\s\.\-_]/', '', $_file->getClientOriginalName());
					$_sanitized_file_name = str_replace(' ', '_', $_sanitized_file_name);

					$_path = 'contact/' . $contact->id;
					$_file->storeAs($_path, $_sanitized_file_name);

					// generate the signed URL for the stored file
					$files[] = URL::signedRoute('contact.file', [
						'id' => $contact->id,
						'filename' => $_sanitized_file_name
					]);
				}
			}

			// send contact form notification to admin email
			$notification = new ContactFormSubmitted($contact, $files);
			$recipient = App::isProduction() ? '<EMAIL>' : env('DEVELOPER_EMAIL');
			Notification::route('mail', $recipient)->notify($notification);

            activity()->withProperties([
                'topic_id' => $request->get('topic_id'),
                'email' => $request->get('email'),
                'message' => $request->get('message'),
                'slike' => $request->get('slike')
            ])->log('Contact');

			return redirect(route('contact'))
				->with('success', 'Zahvaljujemo, zaprimili smo Vašu poruku i odgovorit ćemo Vam u najkraćem mogućem roku.');
		}

		return redirect(route('contact'))
			->with('error', 'Ups, negdje je došlo do pogreške. Pokušajte ponovo.');
	}

	public function getFile($id, $filename) {
		$file_path = storage_path("app/contact/$id/$filename");
		return response()->file($file_path);
	}
}

<?php

namespace App\Http\Controllers\Tenant;

use <PERSON><PERSON><PERSON><PERSON><PERSON>ov\EmailSmtpValidation\Rules\EmailSmtpVerified;
use App\Http\Controllers\Controller;
use App\Models\EmailPreference;
use App\Models\User;
use App\Notifications\EmailChangedNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Str;

class EmailChangeController extends Controller
{
	public function update(Request $request)
	{
		$validate_params = [
			'user_id' => 'required',
			'email' => [
				'required',
				'string',
				'email:rfc,dns',
				'max:255',
				'unique:users',
				'indisposable'
			],
		];

		// perform smtp validation on gmail address
		if(Str::contains($request->get('email'), '@gmail.com')) {
			$validate_params['email'][] = app(EmailSmtpVerified::class);
		}

		$request->validate($validate_params);

		$user = User::find($request->get('user_id'));

		$old_email = $user->email;

		EmailPreference::where('email', $old_email)->delete();

		$user->update([
			'email' => $request->get('email')
		]);

		// send email changed notification to old user email
		$notification = new EmailChangedNotification($old_email, $request->get('email'));
		Notification::route('mail', $old_email)->notify($notification);

		activity()->withProperties([
			'old_email' => $old_email,
			'new_email' => $request->get('email')
		])->log('Email change');

		return redirect(route('tenant.dashboard'))->with('success', 'Uspješno ste promijenili adresu e-pošte.');
	}

}

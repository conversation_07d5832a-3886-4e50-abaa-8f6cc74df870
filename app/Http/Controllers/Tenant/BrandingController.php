<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;

class BrandingController extends Controller
{
    public function edit()
    {
        return view('tenant.branding.edit', [
            'tenant' => tenant(),
        ]);
    }

    public function update(Request $request)
    {
        $validated = $request->validate([
            'appName'          => 'required|string|max:255',
            'emailFromAddress' => 'required|email|max:255',
            'emailFromName'    => 'required|string|max:255',

            'headerLogo'        => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:512',
            'headerLogoPath'    => 'nullable|string|max:255',
            'headerLogoPosition' => [
                'nullable',
                Rule::in(['left', 'center', 'right']),
                Rule::requiredIf(fn () => $request->hasFile('headerLogo')
                    || $request->filled('headerLogoPath')),
            ],

            'headerText'        => 'nullable|string|max:500',
            'headerTextPosition' => [
                'nullable',
                Rule::in(['left', 'center', 'right']),
                Rule::requiredIf(fn () => trim($request->input('headerText')) !== ''),
            ],

            'footerLogo'        => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:512',
            'footerLogoPath'    => 'nullable|string|max:255',
            'footerLogoPosition' => [
                'nullable',
                Rule::in(['left', 'center']),
                Rule::requiredIf(fn () => $request->hasFile('footerLogo')
                    || $request->filled('footerLogoPath')),
            ],

            'footerText'        => 'nullable|string|max:500',
            'footerTextPosition' => [
                'nullable',
                Rule::in(['left', 'center']),
                Rule::requiredIf(fn () => trim($request->input('footerText')) !== ''),
            ],
        ]);

        $tenant = tenant();

        $tenant->appName          = $validated['appName'];
        $tenant->emailFromAddress = $validated['emailFromAddress'];
        $tenant->emailFromName    = $validated['emailFromName'];

        $tenant->headerText         = $request->input('headerText');
        $tenant->headerTextPosition = $request->input('headerTextPosition');
        $tenant->headerLogoPosition = $request->input('headerLogoPosition');

        if ($request->hasFile('headerLogo')) {
            if ($tenant->headerLogoPath) {
                Storage::disk('public')->delete($tenant->headerLogoPath);
            }
            $tenant->headerLogoPath = $request->file('headerLogo')->store('branding/header', 'public');
        } elseif ($tenant->headerLogoPath && !$request->filled('headerLogoPath')) {
            Storage::disk('public')->delete($tenant->headerLogoPath);
            $tenant->headerLogoPath = null;
        }

        $tenant->footerText         = $request->input('footerText');
        $tenant->footerTextPosition = $request->input('footerTextPosition');
        $tenant->footerLogoPosition = $request->input('footerLogoPosition');

        if ($request->hasFile('footerLogo')) {
            if ($tenant->footerLogoPath) {
                Storage::disk('public')->delete($tenant->footerLogoPath);
            }
            $tenant->footerLogoPath = $request->file('footerLogo')->store('branding/footer', 'public');
        } elseif ($tenant->footerLogoPath && !$request->filled('footerLogoPath')) {
            Storage::disk('public')->delete($tenant->footerLogoPath);
            $tenant->footerLogoPath = null;
        }

        $tenant->save();

        return redirect()
            ->route('tenant.branding', ['tab' => $request->input('tab', 'general')])
            ->with('success', 'Postavke brenda su uspješno ažurirane.');
    }
}

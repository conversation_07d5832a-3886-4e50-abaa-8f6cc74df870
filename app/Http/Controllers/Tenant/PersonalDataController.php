<?php

namespace App\Http\Controllers\Tenant;

use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\EmailSmtpValidation\Rules\EmailSmtpVerified;
use App\Http\Controllers\Controller;
use App\Notifications\EmailChangeNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Str;

class PersonalDataController extends Controller
{
    public function show(Request $request) {
    	return view('tenant.account.personal_data');
    }

    public function update(Request $request) {

		$validate_params = [
			'email' => [
				'required',
				'string',
				'email:rfc,dns',
				'max:255',
			],
			'name' => 'required'
		];

	    // perform smtp validation on gmail address
	    if(Str::contains($request->get('email'), '@gmail.com')) {
		    $validate_params['email'][] = app(EmailSmtpVerified::class);
	    }

    	$request->validate($validate_params);

    	$user = \Auth::user();
    	$user->name = $request->get('name');
	    $user->save();

    	if($request->get('email') !== $user->email) {

		    $request->validate([
			    'email' => 'unique:users'
		    ]);

		    // send verification email to the user
		    $notification = new EmailChangeNotification(Auth::user()->id, $request->get('email'));
		    Notification::route('mail', $request->get('email'))->notify($notification);

		    return back()->with('success', 'Za potvrdu promjene adrese e-pošte, kliknite na poveznicu koju smo vam poslali na: '.$request->get('email'));
	    }

    	return back()->with('success', 'Uspješno ste uredili svoje podatke.');
    }
}

<?php

namespace App\Http\Controllers\Tenant;

use App\Helpers\DocumentBuilder;
use App\Helpers\StringHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Tenant\SelectDocumentTemplateRequest;
use App\Models\Document;
use App\Models\DocumentTemplate;
use App\Models\DocumentTemplateSection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Component\Process\Process;
use Yajra\DataTables\DataTables;
use TijsVerkoyen\CssToInlineStyles\CssToInlineStyles;

class DocumentController extends Controller {

	public function index() {

		$device = new \Detection\MobileDetect;

		return view('tenant.documents.index', [
			'is_desktop' => !$device->isMobile() && !$device->isTablet()
		]);
	}

	public function create(SelectDocumentTemplateRequest $request) {

		// validate chosen template
		$request->validated();

		if ($document = Document::create($request->get('template_id'), Auth::check() ? Auth::id() : null)) {
			return redirect(route('tenant.section.show', [$document, $document->template->firstSection()]));
		}

		return back()->with('error', 'Hmm, nešto je pošlo po zlu. Pokušajte ponovo ili nas obavijestite o pogrešci');

	}

	public function previewPdf(Document $document) {
		return view('tenant.documents.preview', [
			'document' => $document
		]);
	}

	public function previewHtml(Request $request) {

		$request->validate([
			'document_id' => 'required',
			'section_id' => 'required'
		]);

		$id = $request->get('document_id');
		$id = \Hashids::decode($id)[0];

		$section_id = $request->get('section_id');
		$section_id = \Hashids::decode($section_id)[0];

		if(($document = Document::find($id)) && ($section = DocumentTemplateSection::find($section_id))) {

			// get all user input
			$section_data = $request->except(['_token', 'document_id', 'section_id']);

			$document_builder = new DocumentBuilder($document, $section->view);

			return $document_builder
				->updateSectionValues($section->id, $section_data)
				->getPreviewContent();
		}

		abort(400);
	}

	public function stream(Document $document) {

		$builder = new DocumentBuilder($document);

		if ($document->isDownloadable()) {
			return $builder->stream(null);
		}

		return $builder->stream();

	}

    public function downloadWord(Document $document) {
        $cb = new DocumentBuilder($document);
        $html = $cb->generateNewContent();

        // Step 0: Clean the HTML
        $html = $this->cleanHtmlForWord($html);

        // Step 1: Create temp file for HTML
        $tempDir = sys_get_temp_dir();
        $htmlFile = tempnam($tempDir, 'html_') . '.html';
        file_put_contents($htmlFile, $html);

        // Step 2: Convert HTML to ODT using LibreOffice CLI
        $odtDir = $tempDir;
        $process1 = new Process([
            env('LIBREOFFICE_PATH'), '--headless', '--convert-to', 'odt', '--outdir', $odtDir, $htmlFile
        ]);
        $process1->run();

        if (!$process1->isSuccessful()) {
            unlink($htmlFile);
            abort(500, 'Error converting HTML to ODT: ' . $process1->getErrorOutput());
        }

        $odtFile = preg_replace('/\.html$/', '.odt', $htmlFile);

        // Step 3: Convert ODT to DOCX using LibreOffice CLI
        $docxDir = $tempDir;
        $process2 = new Process([
            env('LIBREOFFICE_PATH'), '--headless', '--convert-to', 'docx', '--outdir', $docxDir, $odtFile
        ]);
        $process2->run();

        if (!$process2->isSuccessful()) {
            unlink($htmlFile);
            unlink($odtFile);
            abort(500, 'Error converting ODT to DOCX: ' . $process2->getErrorOutput());
        }

        $docxFile = preg_replace('/\.odt$/', '.docx', $odtFile);

        // Step 4: Return DOCX as a download response
        $filename = Str::slug($document->title) . '.docx';
        $content = file_get_contents($docxFile);

        // Clean up temp files
        unlink($htmlFile);
        unlink($odtFile);
        unlink($docxFile);

        return \Response::make($content, 200, [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'Content-Disposition' => "attachment; filename=\"$filename\"",
            'Content-Length' => strlen($content),
        ]);
    }

    private function cleanHtmlForWord(string $html): string
    {
        // Inline CSS styles
        $inliner = new CssToInlineStyles();
        $html = $inliner->convert($html);

        // Remove <style> tags in <head>
        $html = preg_replace('/<style\b[^>]*>.*?<\/style>/is', '', $html);

        // Remove <htmlpageheader> and <htmlpagefooter> tags
        $html = preg_replace('/<htmlpageheader\b[^>]*>.*?<\/htmlpageheader>/is', '', $html);
        $html = preg_replace('/<htmlpagefooter\b[^>]*>.*?<\/htmlpagefooter>/is', '', $html);

        // Process only the table inside #signatures-segment
        $html = preg_replace_callback(
            '/<div[^>]*id="signatures-segment"[^>]*>.*?<table[^>]*>.*?<\/table>.*?<\/div>/is',
            function ($matches) {
                $segment = $matches[0];

                // Remove style from <td> inside this segment
                $segment = preg_replace('/<td\s+[^>]*style="[^"]*"[^>]*>/i', '<td>', $segment);

                // Set border: none on the <table>
                $segment = preg_replace_callback(
                    '/<table([^>]*)style="([^"]*)"/i',
                    function ($m) {
                        $before = $m[1];
                        $style = preg_replace('/\bborder\s*:[^;"]*;?/i', '', $m[2]);
                        $style = trim($style);
                        $newStyle = $style ? "$style; border: none;" : 'border: none;';
                        return "<table{$before}style=\"{$newStyle}\"";
                    },
                    $segment
                );

                return $segment;
            },
            $html
        );

        // Shorten long underscores in signature lines
        $html = preg_replace_callback(
            '/<span[^>]*class="signature-line"[^>]*>[_\s]{10,}<\/span>/i',
            fn($m) => '<br/><br/><br/><br/><br/><br/><br/><br/><span class="signature-line">___________________________</span>',
            $html
        );

        // Remove <img> elements with alt="signature"
        $html = preg_replace('/<img\b[^>]*alt\s*=\s*["\']signature["\'][^>]*>/i', '', $html);

        //echo $html; exit;

        return $html;
    }

	public function download(Document $document) {

		$pdf_path = 'documents/' . $document->id . '/document.pdf';

		// precautionary check
		if (Storage::missing($pdf_path)) {
			$document->savePdf();
		}

		// read image file to avoid copy-pasting text from PDF
		if($document->shouldProtectCopyPasting()) {

			$image_pdf_path = 'documents/' . $document->id . '/document_image.pdf';

            if (Storage::missing($image_pdf_path)) {
                $document->saveImagePdf();
            }

			$pdf_path = $image_pdf_path;
        }

		// attach a (httponly=false) cookie indicating to frontend that download has started (for loader toggle)
		Cookie::queue(cookie('download_started', 1, 1, null, null, false, false));

        activity()->performedOn($document)->log('Download');

		return response()->download(
			storage_path("app/$pdf_path"),
			StringHelper::sanatizeStringForDownload($document->template->public_title) . ".pdf",
			[
				'Cache-Control' => 'public, must-revalidate, max-age=0',
				'Expires' => 'Sat, 26 Jul 1997 05:00:00 GMT'
			]
		);
	}

	public function delete(Document $document) {

		if ($document->delete()) {
			return back()->with('success', 'Uspješno ste izbrisali dokument.');
		} else {
			return back()->with('error', 'Došlo je do pogreške prilikom brisanja dokumenta.');
		}
	}

	public function duplicate(Document $document) {

		if ( ! $document->isOutdated()) {

			$cloned_document = $document->replicate();
			$cloned_document->is_sent = 1; // avoid sending emails for duplicated documents
			$cloned_document->save();

			foreach ($document->parties as $_party) {
				$_party->replicate()->fill([
					'document_id' => $cloned_document->id,
					'signed_at'   => null,
					'signature'   => null
				])->saveQuietly();
			}

			foreach ($document->sectionValues as $_section_values) {
				$_section_values->replicate()->fill([
					'document_id' => $cloned_document->id
				])->saveQuietly();
			}

			$cloned_document->createStorageDirectory();
			$cloned_document->savePdf();

            activity()->performedOn($document)->log('Duplicate');

			return back()->with('success', 'Uspješno ste duplicirali dokument.');
		} else {
			return back()->with('error', __('Document is outdated.'));
		}

	}

	public function duplicateAdmin(Document $document) {

		if ( ! $document->isOutdated()) {

			$cloned_document = $document->replicate();
			$cloned_document->is_sent = 1; // avoid sending emails for duplicated documents
			$cloned_document->user_id = Auth::id(); // overwrite ownership
			$cloned_document->save();

			foreach ($document->parties as $_party) {
				$_party->replicate()->fill([
					'document_id' => $cloned_document->id,
					'signed_at'   => null,
					'signature'   => null
				])->save();
			}

			foreach ($document->sectionValues as $_section_values) {
				$_section_values->replicate()->fill([
					'document_id' => $cloned_document->id
				])->save();
			}

			$cloned_document->createStorageDirectory();
			$cloned_document->savePdf();

            activity()->performedOn($document)->log('Duplicate (admin)');

			return back()->with('success', 'Uspješno ste duplicirali dokument.');
		} else {
			return back()->with('error', __('Document is outdated.'));
		}

	}

	public function exportToEditor(Document $document) {
		try{
			$draft = $document->createDraft();
			return redirect(route('tenant.editor.draft.edit', $draft));
		} catch(\Exception $e) {
			report($e);
			return back()->with('error', 'Ups, negdje je došlo do pogreške. Pokušajte ponovo.');
		}
	}

	public function fork(Document $document) {

		if ($document->template->isPrecontract()) {
			if ( ! $document->isOutdated()) {

				$document_template = DocumentTemplate::find($document->template->precontract_for_id);

				$cloned_document                       = $document->replicate();
				$cloned_document->template_id          = $document->template->precontract_for_id;
				$cloned_document->title                = $document_template->public_title;
				$cloned_document->save();

				foreach ($document->parties as $_party) {
					$_party->replicate()->fill([
						'document_id' => $cloned_document->id,
						'signed_at'   => null,
						'signature'   => null
					])->save();
				}

				foreach ($document->sectionValues as $_section_values) {
					$cloned_document_section = DocumentTemplateSection::where([
						'template_id' => $cloned_document->template_id,
						'title'                => $_section_values->section->title
					])->first();

					if ($cloned_document_section) {
						$_section_values->replicate()->fill([
							'document_id'                  => $cloned_document->id,
							'document_template_section_id' => $cloned_document_section->id
						])->save();
					}
				}

				$cloned_document->createStorageDirectory();
				$cloned_document->savePdf();

				activity()->performedOn($document)->log('Fork');

				return back()->with('success', 'Uspješno ste generirali ' . lcfirst($document_template->public_title) . '.');
			} else {
				return back()->with('error', __('Document is outdated.'));
			}
		} else {
			return back()->with('error', 'Potrebno je odabrati predugovor.');
		}
	}

	public function dataTables() {

		$columns = ['id', 'title', 'comment', 'user_id', 'created_at', 'updated_at', 'template_id'];

		$documents = Auth::user()->isAdmin() ?
			Document::where('is_visible', true)->select($columns)
			: Auth::user()->documents()->select($columns);

		return Datatables::of($documents->with(['template', 'template.sections', 'signedParties', 'parties', 'user']))
		                 ->editColumn('id', function ($document) {

			                 return $document->hashid();
		                 })
		                 ->editColumn('created_at', function ($document) {

			                 return date('d.m.Y.', strtotime($document->created_at));
		                 })
		                 ->editColumn('title', function ($document) {

							 $parties = $document->getPartiesString();

							 $title = !empty($parties) ?
								 $document->title . " (" . $document->getPartiesString() . ")" :
								 $document->title;

			                 if(Auth::user()->isAdmin() && $document->user) {
				                 $title.= " <div class='text-muted'><small>" . $document->user->email . "</small></div>";
			                 }

							 return $title;
		                 })
		                 ->editColumn('comment', function ($document) {

			                 return nl2br($document->comment ?: '');
		                 })
		                 ->addColumn('links', function ($document) {

			                 return [
				                 'edit'           => [
					                 'url'      => $document->signedParties->count() ? 'javascript:void(0)' : route('tenant.section.show', [
						                 $document,
						                 $document->template->firstSection()
					                 ]),
					                 'disabled' => $document->signedParties->count() ? 'disabled' : ''
				                 ],
				                 'download'       => [
					                 'url' => URL::signedRoute('tenant.document.download', $document),
				                 ],
                                 'downloadWord'       => [
                                     'url' => URL::signedRoute('tenant.document.download.word', $document),
                                 ],
				                 'duplicate'      => [
					                 'url' => Auth::user()->isAdmin() ? route('tenant.document.duplicate.admin', $document) : route('tenant.document.duplicate', $document),
				                 ],
				                 'fork'           => [
					                 'is_precontract' => $document->template->isPrecontract(),
					                 'url'            => route('tenant.document.fork', $document),
				                 ],
				                 'exportToEditor' => [
					                 'url' => route('tenant.document.exportToEditor', $document),
				                 ],
				                 'send'           => [
					                 'url' => route('tenant.email.document', $document),
				                 ],
				                 'signatures'     => [
					                 'url' => route('tenant.signature.index', $document),
				                 ],
				                 'comment'        => [
					                 'id'      => $document->hashid(),  // because we're using ajax to update
					                 'comment' => $document->comment
				                 ],
				                 'delete'         => [
					                 'url'      => route('tenant.document.delete', $document),
					                 'disabled' => ''
				                 ],
			                 ];
		                 })
		                 ->rawColumns(['comment', 'title'])
		                 ->filter(function ($query) {

			                 // perform search by document title, comment and parties names (and email if admin)

			                 if ( ! empty(request('search')['value'])) {
				                 $term = request('search')['value'];

				                 // admin searching by email
				                 if (Auth::user()->isAdmin() && filter_var($term, FILTER_VALIDATE_EMAIL)) {
					                 $query->whereHas('user', function($q) use ($term) {
						                 $q->where('email', 'like', "%$term%");
					                 });
				                 } else {
					                 $matching_parties_document_ids = [];

					                 if (\App::environment(['production']) && Auth::user()->isAdmin()) {
						                 $parties = DB::table('document_parties')->select('document_id', 'name', 'label')->get();
					                 } else {
						                 $parties = DB::table('document_parties')
						                              ->join('documents', 'document_parties.document_id', '=', 'documents.id')
						                              ->where('documents.user_id', '=', Auth::id())
						                              ->select('document_parties.document_id', 'document_parties.name', 'document_parties.label')
						                              ->get();
					                 }

					                 // because of encryption, we need to manually check if query term matches any of the document parties
					                 foreach ($parties as $_party) {
						                 $_party_label = Crypt::decrypt($_party->label);
						                 $_party_name = $_party_label;

						                 // if authorized person, append company name
						                 if ($_parenthesis_text = StringHelper::extractTextFromParenthesis($_party_label)) {
							                 $_party_name .= " $_parenthesis_text";
						                 }

						                 if (StringHelper::stringContainsAnotherStringWords($term, $_party_name)) {
							                 $matching_parties_document_ids[] = $_party->document_id;
						                 }
					                 }

					                 $query->where(function ($query) use ($term, $matching_parties_document_ids) {
						                 $query->where('title', 'like', "%$term%");
						                 $query->orWhere('comment', 'like', "%$term%");
						                 $query->orWhereIn('id', $matching_parties_document_ids);
					                 });
				                 }
			                 }

		                 })
						 // hide sensitive columns
		                 ->removeColumn('user_id')
		                 ->removeColumn('template_id')
		                 ->removeColumn('signed_parties')
		                 ->removeColumn('template')
		                 ->removeColumn('user')
		                 ->make();
	}

	public function comment(Document $document) {
		$document->comment = request()->get('comment');
		$document->timestamps = false;  // do not update timestamps
		return $document->save();
	}
}

<?php

namespace App\Http\Controllers\Tenant;

use App\Helpers\DocumentParser;
use App\Helpers\DocumentPDF;
use App\Helpers\ElasticHelper;
use App\Helpers\Post;
use App\Helpers\SpellChecker;
use App\Helpers\StringHelper;
use App\Http\Controllers\Controller;
use App\Models\DocumentDraft;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\URL;
use Soundasleep\Html2Text;
use View;
use Yajra\DataTables\DataTables;

class EditorController extends Controller {

	public function edit(DocumentDraft $draft) {

		if ($draft->isOutdated()) {
			return back()->with('error', __('Draft is outdated.'));
		}

		$parser = new DocumentParser($draft->html, $draft->type_id);

		return view('tenant.editor.edit', [
			'draft'  => $draft,
			'parser' => $parser
		]);
	}

	public function save(DocumentDraft $draft) {

		if ($draft->isOutdated()) {
			return redirect(route('tenant.dashboard'))->with('error', __('Draft is outdated.'));
		}

		request()->validate([
			'html' => 'required|string'
		]);

		$render_type_id = request()->get('solemnization') === 'on' ?
			DocumentDraft::$render_types['solemnization'] :
			DocumentDraft::$render_types['normal'];

		$view = $render_type_id === DocumentDraft::$render_types['solemnization'] ?
			'tenant.editor.show_dashed' :
			'tenant.editor.show';

		$html = View::make($view, ["content" => request()->get('html')])->render();

		$draft->update([
			'html' => $html,
			'render_type_id' => $render_type_id,
			'is_visible' => true
		]);

		return redirect(route('tenant.dashboard', ['tab' => 'editor']))
			->with('success', 'Uspješno ste spremili dokument.')
			->withCookie(cookie('documentsTab', 'editor',60*24*360, '/', null, null, false));
	}

    public function downloadAdmin(DocumentDraft $draft) {

	    // log activity
	    activity()->performedOn($draft)->log('Download (admin)');

	    // if not solemnization, always rebuild pdf (important for highlighting changes via CSS rules)
	    if($draft->render_type_id !== DocumentDraft::$render_types['solemnization']) {

			$parser = new DocumentParser(
			    $draft->html,
			    $draft->type_id
		    );

		    $content = View::make('tenant.editor.show', [
				"content" => $parser->getContent()
		    ])->render();

		    $template = new DocumentPDF($content);

		    // attach a (httponly=false) cookie indicating to frontend that download has started (for loader toggle)
		    Cookie::queue(cookie('download_started', 1, 1, null, null, false, false));

		    return response()->streamDownload(function () use ($template) {
			    echo $template->output();
		    }, StringHelper::sanatizeStringForDownload($draft->title) . ".pdf", [
			    'Cache-Control' => 'public, must-revalidate, max-age=0',
			    'Expires'       => 'Sat, 26 Jul 1997 05:00:00 GMT'
		    ]);
	    } else {
			// if solemnization, just use regular download
		    return $this->download($draft);
	    }
    }

	public function download(DocumentDraft $draft) {

		$pdf_path = 'editor/drafts/' . $draft->id . '/document.pdf';

		// first time downloading
		if (Storage::missing($pdf_path)) {
			$draft->savePdf();
		}

		// read image file to avoid copy-pasting text from PDF
		if ($draft->shouldProtectCopyPasting()) {

			$image_pdf_path = 'editor/drafts/' . $draft->id . '/document_image.pdf';

			if (Storage::missing($image_pdf_path)) {
				$draft->saveImagePdf();
			}

			$pdf_path = $image_pdf_path;
		}

		// attach a (httponly=false) cookie indicating to frontend that download has started (for loader toggle)
		Cookie::queue(cookie('download_started', 1, 1, null, null, false, false));

		activity()->performedOn($draft)->log('Download');

		return response()->download(
			storage_path("app/$pdf_path"),
			StringHelper::sanatizeStringForDownload($draft->title) . ".pdf",
			[
				'Cache-Control' => 'public, must-revalidate, max-age=0',
				'Expires'       => 'Sat, 26 Jul 1997 05:00:00 GMT'
			]
		);
	}


	public function delete(DocumentDraft $draft) {

		if ($draft->delete()) {
			return back()->with('success', 'Uspješno ste izbrisali dokument.');
		} else {
			return back()->with('error', 'Došlo je do pogreške prilikom brisanja dokumenta.');
		}
	}

	public function comment(DocumentDraft $draft) {
		$draft->comment = request()->get('comment');
		$draft->timestamps = false;  // do not update timestamps
		return $draft->save();
	}

	public function title(DocumentDraft $draft) {
		$draft->title = request()->get('title');
		$draft->timestamps = false;  // do not update timestamps
		return $draft->save();
	}

	public function stream(DocumentDraft $draft) {
		$document_directory = storage_path("app/editor/drafts/{$draft->id}");
		$pdf_path =  "$document_directory/document.pdf";

		// create pdf if it doesn't already exist
		if(!File::exists($pdf_path)) {
			$draft->savePdf();
		}

		return response()->stream(function () use ($pdf_path, $draft) {
			$file = File::get($pdf_path);
			echo $file;
		});
	}

	public function duplicate(DocumentDraft $draft) {

		if ( ! $draft->isOutdated()) {
			$cloned_draft = $draft->replicate();
			$cloned_draft->save();

			$cloned_draft->createStorageDirectory();
			$cloned_draft->savePdf();

			activity()->performedOn($draft)->log('Duplicate');

			return back()->with('success', 'Uspješno ste duplicirali uređeni dokument.');
		} else {
			return back()->with('error', __('Draft is outdated.'));
		}
	}

	public function duplicateAdmin(DocumentDraft $draft) {

		if ( ! $draft->isOutdated()) {
			$cloned_draft = $draft->replicate();
			$cloned_draft->user_id = Auth::id(); // overwrite ownership
			$cloned_draft->save();

			$cloned_draft->createStorageDirectory();
			$cloned_draft->savePdf();

			activity()->performedOn($draft)->log('Duplicate (admin)');

			return back()->with('success', 'Uspješno ste duplicirali uređeni dokument.');
		} else {
			return back()->with('error', __('Draft is outdated.'));
		}
	}

	public function draftsDataTables() {

		$columns = ['id', 'user_id', 'title', 'comment', 'is_dirty', 'created_at', 'updated_at'];

		$drafts = Auth::user()->isAdmin() ?
			DocumentDraft::where('is_visible', true)->with(['user'])->select($columns)
			: Auth::user()->documentDrafts()->with(['user'])->select($columns);

		return Datatables::of($drafts)
		                 ->editColumn('id', function ($document) {

			                 return $document->hashid();
		                 })
						 ->editColumn('is_dirty', function ($document) {

							 // disable dirty flag for non-admins
							 return auth()->user()->isAdmin() ? $document->is_dirty : false;
						 })
		                 ->editColumn('created_at', function ($draft) {

			                 return date('d.m.Y.', strtotime($draft->created_at));
		                 })
						 ->editColumn('title', function ($draft) {

							$title = $draft->title;

							if(Auth::user()->isAdmin() && $draft->user) {
								$title.= " <div class='text-muted'><small>" . $draft->user->email . "</small></div>";
							}

							return $title;
						 })
		                 ->editColumn('comment', function ($draft) {

			                 return nl2br($draft->comment ?: '');
		                 })
		                 ->addColumn('links', function ($draft) {

			                 return [
				                 'edit'      => [
					                 'url' => route('tenant.editor.draft.edit', $draft),
				                 ],
				                 'download'  => [
					                 'url' => URL::signedRoute(Auth::user()->isAdmin() ?
						                 'tenant.editor.draft.download.admin' :
						                 'tenant.editor.draft.download',
						                 [$draft]
					                 ),
				                 ],
				                 'duplicate' => [
					                 'url' => Auth::user()->isAdmin() ? route('tenant.editor.draft.duplicate.admin', $draft) : route('tenant.editor.draft.duplicate', $draft),
				                 ],
				                 'send'      => [
					                 'url' => route('tenant.editor.draft.email', $draft),
				                 ],
				                 'title'     => [
					                 'id'    => $draft->hashid(),  // because we're using ajax to update
					                 'title' => $draft->title
				                 ],
				                 'comment'   => [
					                 'id'      => $draft->hashid(),  // because we're using ajax to update
					                 'comment' => $draft->comment
				                 ],
				                 'delete'    => [
					                 'url'      => route('tenant.editor.draft.delete', $draft),
					                 'disabled' => ''
				                 ],
			                 ];
		                 })
		                 ->rawColumns(['comment', 'title'])
		                 ->filter(function ($query) {

			                 // perform search by draft title and comment
			                 if ( ! empty(request('search')['value'])) {
				                 $term = request('search')['value'];

				                 // admin searching by email
				                 if (Auth::user()->isAdmin() && filter_var($term, FILTER_VALIDATE_EMAIL)) {
					                 $query->whereHas('user', function($q) use ($term) {
						                 $q->where('email', 'like', "%$term%");
					                 });
				                 } else {
					                 $query->where(function ($query) use ($term) {

						                 $query->where('title', 'like', "%$term%");
						                 $query->orWhere('comment', 'like', "%$term%");
					                 });
				                 }
			                 }
		                 })
						 // hide sensitive columns
			             ->removeColumn('user_id')
		                 ->removeColumn('user')
		                 ->make();
	}

	public function articlesDataTables(Post $post) {

		request()->validate([
			'draw' => 'present',
			'start' => 'present',
			'length' => 'present',
			'search.value' => 'present'
		]);

		$draw = request('draw');
		$start = request('start');
		$length = request('length');
		$query = request('search.value') ?? "";

		$cache_key = 'search.article.'.App::environment().':'. $post->ID . ':' . $draw . ':' . $start . ':' . $length . ':' . $query;

		return Cache::tags(['article-search'])->rememberForever($cache_key, function () use ($post, $draw, $start, $length, $query) {

			try {
				// fetch relevant example ids
				$example_ids = $post->getRelatedExamples();

				$post_related_examples = $post->acf->related_examples;

				if($post_related_examples && count($post_related_examples->toArray())) {
					$example_ids+= $post_related_examples->pluck('ID')->toArray();
				}

				if(!empty($example_ids)) {

					$filters = ['post_ids' => $example_ids];

					$es = new ElasticHelper();

					$articles = !empty($query)
						? $es->search('article', $query, $length, $start, $filters)
						: $es->get('article', $length, $start, $filters);

					// format the response in a way that DataTables can use
					$data = [];
					foreach ($articles['hits']['hits'] as $_hit) {
						$_hit_data = Arr::only($_hit['_source'], [
							'title', 'body', 'description', 'post_title'
						]);

						// annonymize article references
						$_hit_data['body'] = $this->anonymizeArticleReferences($_hit_data['body']);

						// prepare the body for editor (enumerate, clean, turn paragraphs into lists...)
						$_hit_data['content'] = DocumentParser::formatArticleBody($_hit_data['body']);
						$_hit_data['content'] = $this->anonymizeArticleReferences($_hit_data['content']);

						// save modified hit data
						$data[] = $_hit_data;
					}

					$total_articles_count = ElasticHelper::getCount('article');
					$total_filtered_records_count = $articles['hits']['total']['value'];

					// return data to DataTable
					return [
						'draw' => $draw,
						'recordsTotal' => $total_articles_count,
						'recordsFiltered' => $total_filtered_records_count,
						'data' => $data,
					];
				} else {
					// this document has no related examples, therefore no articles should be returned
					if(empty($query) && empty($filters)) {
						return [
							'draw' => $draw,
							'recordsTotal' => 0,
							'recordsFiltered' => 0,
							'data' => [],
						];
					}
				}
			} catch(\Exception  $e) {

				// report exception but don't throw it to the user
				report($e);

				return [
					'draw' => $draw,
					'recordsTotal' => 0,
					'recordsFiltered' => 0,
					'data' => [],
				];
			}

		});
	}

	public function spellCheck() {

		request()->validate([
			'id' => 'required',
			'content' => 'required'
		]);

		$hashed_id = request()->get('id');
		$ids = \Hashids::decode($hashed_id);

		if (!empty($ids) && $draft = DocumentDraft::find($ids[0])) {
			activity('spellcheck')->performedOn($draft)->log('Spellcheck');
		}

		$content = request()->get('content');

		if($text = Html2Text::convert($content, ['ignore_errors' => true])) {

			// cache the results for 30 days
			$cache_key = 'spell_check_' . md5($text);

			return Cache::remember($cache_key, now()->addMonth(), function () use ($text, $content) {
				$result = SpellChecker::analyze($text);
				return Spellchecker::fix($content, $text, $result);
			});
		} else {
			return ['adjustments' => false, 'errors' => false, 'content' => $content];
		}
	}

	/**
	 * Helper function that anonymizes the article and paragraph references in the body because user needs to input them manually
	 *
	 * @param string $text
	 * @return array|string|null
	 */
	private function anonymizeArticleReferences(string $text): array|string|null {
		// regex pattern to match both article and paragraph references
		$pattern = '/(članak|članku|članka|stavak|stavka|stavku) (\d+(\.\d+)?)(\.?) ovog/';
		$replacement = '$1 [X]$4 ovog';
		return preg_replace($pattern, $replacement, $text);
	}
}

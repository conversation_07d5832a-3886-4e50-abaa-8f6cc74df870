<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\EmailPreference;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\URL;

class EmailPreferencesController extends Controller
{
    public function show(Request $request) {
    	$request->validate([
    		'email' => 'required',
	    ]);

	    $preferences = EmailPreference::firstOrCreate([
		    'email' => $request->get('email')
	    ])->refresh();

	    return view('tenant.account.email_preferences', [
	    	'preferences' => $preferences,
		    'guest' => !User::where('email', $request->get('email'))->exists()
	    ]);


    }

	public function store(Request $request) {
		$request->validate([
			'email' => 'required'
		]);

		$guest = !User::where('email', $request->get('email'))->exists();

		if($guest) {
			// store general preferences
			if($request->get('general')) {
				EmailPreference::subscribeToGeneral($request->get('email'));
			}
			else {
				EmailPreference::unsubsubscribeFromGeneral($request->get('email'));
			}
		}

		// store newsletter preferences
		if($request->get('newsletter')) {
			EmailPreference::subscribeToNewsletter($request->get('email'));
		}
		else {
			EmailPreference::unsubsubscribeFromNewsletter($request->get('email'));
		}

		return redirect(URL::signedRoute('tenant.email.preferences.show', ['email' => $request->get('email')]))
			->with('success', 'Uspješno ste spremili postavke.');


	}
}

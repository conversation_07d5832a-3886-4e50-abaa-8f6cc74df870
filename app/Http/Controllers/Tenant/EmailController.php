<?php

namespace App\Http\Controllers\Tenant;

use <PERSON><PERSON><PERSON><PERSON><PERSON>ov\EmailSmtpValidation\Rules\EmailSmtpVerified;
use App\Http\Controllers\Controller;
use App\Models\Document;
use App\Models\DocumentDraft;
use App\Models\Translation;
use App\Models\TranslationDocument;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Str;

class EmailController extends Controller {

	public function draft(DocumentDraft $draft) {
		return view('tenant.editor.email', ['draft' => $draft]);
	}

	public function document(Document $document) {
		return view('tenant.documents.email', ['document' => $document]);
	}

	protected function validateEmails() {
		$validate_params = [
			'email.*' => [
				'required',
				'string',
				'email:rfc,dns',
				'max:255',
			],
		];

		foreach (request()->get('email') as $_i => $_email) {
			if (Str::contains($_email, '@gmail.com')) {
				$validate_params["email.$_i"] = app(EmailSmtpVerified::class);
			}
		}

		return \Validator::make(request()->all(), $validate_params);
	}

	protected function sendNotifications($model, $recipients, $notificationClass) {
		foreach ($recipients as $_recipient) {
			$notification = new $notificationClass($model, $_recipient);
			Notification::route('mail', $_recipient)->notify($notification);
		}

		activity()->performedOn($model)->withProperties(['recipients' => $recipients])->log('Send');
	}

	public function sendDocument(Document $document) {
		$validator = $this->validateEmails();

		if ($validator->fails()) {
			return back()
				->withErrors($validator->errors()->getMessages() + ['override' => 'Crveno označene adrese e-pošte su neispravne.'])
				->withInput();
		}

		$recipients = array_filter(request('email'));

		if (!empty($recipients)) {
			$this->sendNotifications($document, $recipients, \App\Notifications\SendDocument::class);
			return back()->with('success', 'Uspješno ste poslali dokument na upisane adrese e-pošte.');
		} else {
			return back()->with('error', 'Potrebno je upisati barem jednu adresu e-pošte.');
		}
	}

	public function sendDraft(DocumentDraft $draft) {
		$validator = $this->validateEmails();

		if ($validator->fails()) {
			return back()
				->withErrors($validator->errors()->getMessages() + ['override' => 'Crveno označene adrese e-pošte su neispravne.'])
				->withInput();
		}

		$recipients = array_filter(request('email'));

		if (!empty($recipients)) {
			$this->sendNotifications($draft, $recipients, \App\Notifications\SendDocumentDraft::class);
			return back()->with('success', 'Uspješno ste poslali dokument na upisane adrese e-pošte.');
		} else {
			return back()->with('error', 'Potrebno je upisati barem jednu adresu e-pošte.');
		}
	}
}

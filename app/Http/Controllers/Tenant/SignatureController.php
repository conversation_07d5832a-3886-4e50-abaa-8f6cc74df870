<?php

namespace App\Http\Controllers\Tenant;

use <PERSON><PERSON><PERSON><PERSON><PERSON>ov\EmailSmtpValidation\Rules\EmailSmtpVerified;
use App\Http\Controllers\Controller;
use App\Models\Document;
use App\Models\DocumentParty;
use App\Models\SignatureRequest;
use App\Notifications\AllPartiesSignedDocument;
use App\Notifications\PartySignedDocument;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Str;

class SignatureController extends Controller {

	public function index(Document $document) {

		if( ! $document->isOutdated()) {
			if ($document->signeeParties()->where('name', '!=', '')->count()) {
				return view('tenant.signatures.index', [
					'document' => $document->load(['signeeParties', 'signeeParties.signatureRequest']),
				]);
			} else {
				return back()->with('error', 'Nisu uneseni podaci o strankama.');
			}
		} else {
			return back()->with('error', __('Document is outdated.'));
		}
	}

	public function show(DocumentParty $party) {

		$signature_request = SignatureRequest::where('link', request()->fullUrl())->first();

		if ( ! $party->document) {
			return view('tenant.signatures.document_deleted');
		}

		// if not document owner, and has revoked or expired link, deny entry
		if ($party->document->user != Auth::user()) {

			if ( ! $signature_request) {
				return view('tenant.signatures.revoked');
			} elseif ($signature_request->isExpired()) {
				return view('tenant.signatures.expired');
			}
		}

		return view('tenant.signatures.show', [
			'party'                => $party,
			'signature_request_id' => $signature_request ? $signature_request->id : null
		]);

	}

	public function save(DocumentParty $party) {

		request()->validate([
			'party_signature'      => 'required',
			'signature_request_id' => 'present',
		]);

		// upload signature image
		$path = "documents/" . $party->document->id . "/signatures/" . $party->id . "/signature.png";

		$signature = \Image::make(request('party_signature'))->resize(300, 150);

		$binary = (string) $signature->encode();

		$result = Storage::put($path, $binary);

		// save signature path in database
		if ($result) {
			$party->signature = $path;
			$party->signed_at = Carbon::now();
			$party->save();

			// save pdf
			$party->document->savePdf();

			// if via request link, update is_signed flag, send notification
			if (request('signature_request_id')) {
				if ($request = SignatureRequest::find(request('signature_request_id'))) {
					$request->is_signed = true;
					$request->save();
				}

				// send notification
				if ($party->document->allPartiesSigned()) {

					foreach ($party->document->signedParties as $_party) {
						// send notification
						if ($_party->signatureRequest) {
							$notification = new AllPartiesSignedDocument($party->document, $_party->signatureRequest->email);
							Notification::route('mail', $_party->signatureRequest->email)->notify($notification);
						}
					}

					// also send to document owner
					$notification = new AllPartiesSignedDocument($party->document, $party->document->user->email);
					Notification::route('mail', $party->document->user->email)->notify($notification);

				} else {
					$notification = new PartySignedDocument($party, $party->document->user->email);
					Notification::route('mail', $party->document->user->email)->notify($notification);
				}
			}

            activity()->performedOn($party)->log('Sign');

			if ($party->document->user == Auth::user()) {
				return redirect(route('tenant.signature.index', $party->document))->with('success', 'Uspješno ste potpisali dokument.');
			} else {
				return back()->with('success', 'Uspješno ste potpisali dokument.');
			}

		} else {
			return back()->with('error', 'Došlo je do pogreške prilikom spremanja e-Potpisa');
		}

	}

	public function delete(DocumentParty $party) {

		Storage::deleteDirectory('documents/' . $party->document->id . '/signatures/' . $party->id);

		$party->signature = null;
		$party->signed_at = null;

		if ($party->save()) {
            activity()->performedOn($party)->log('Delete signature');
			return back()->with('success', 'e-Potpis je uklonjen.');
		} else {
			return back()->with('error', 'Došlo je do pogreške prilikom brisanja e-Potpisa');
		}
	}

	public function request(Document $document, DocumentParty $party) {

		return view('tenant.signatures.request', ['document' => $document, 'party' => $party]);
	}

	public function requestSend(Document $document, DocumentParty $party) {

		$validate_params = [
			'email' => [
				'required',
				'string',
				'email:rfc,dns',
				'max:255',
			],
		];

		// perform smtp validation on gmail address
		if(Str::contains(request()->get('email'), '@gmail.com')) {
			$validate_params['email'][] = app(EmailSmtpVerified::class);
		}

		request()->validate($validate_params);

		// delete all previous requests
		SignatureRequest::where([
			'document_id' => $document->id,
			'party_id'    => $party->id,
		])->delete();

		// create new signature request
		if ($request = SignatureRequest::create([
			'document_id' => $document->id,
			'party_id'    => $party->id,
			'email'       => request('email'),
			'link'        => URL::signedRoute('tenant.signature.show', [$party])
		])) {
			// send notification
			$notification = new \App\Notifications\SignatureRequest($request, $request->email);
			Notification::route('mail', $request->email)->notify($notification);

            activity()->performedOn($request)->log('Send');

			return redirect(route('tenant.signature.index', [$document]))->with('success', 'Zahtjev za e-Potpisom je poslan na adresu e-pošte: ' . request('email'));
		} else {
			return redirect(route('tenant.signature.index', [$document]))->with('error', 'Ups, nešto je pošlo po zlu. Pokušajte ponovo ili nam prijavite grešku.');
		}

	}

	public function requestRevoke(Document $document, SignatureRequest $request) {

		if ($request->delete()) {
			return back()->with('success', 'Uspješno ste otkazali zahtjev za e-Potpisom.');
		} else {
			return back()->with('error', 'Ups, nešto je pošlo po zlu. Pokušajte ponovo ili nam prijavite grešku.');

		}
	}

	public function image(DocumentParty $party) {

		header('Content-Type: image/png');

		readfile(
			storage_path('app/documents/' . $party->document->id . '/signatures/' . $party->id . '/signature.png')
		);
	}
}

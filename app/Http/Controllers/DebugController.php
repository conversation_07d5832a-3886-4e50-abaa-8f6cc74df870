<?php

namespace App\Http\Controllers;

use App\Helpers\DateToText;
use App\Helpers\DocumentBuilder;
use App\Helpers\DocumentDasher;
use App\Helpers\DocumentHelperAI;
use App\Helpers\DocumentPDF;
use App\Helpers\ElasticHelper;
use App\Helpers\Post;
use App\Helpers\SpellChecker;
use App\Helpers\StringHelper;
use App\Models\Document;
use App\Models\DocumentTemplate;
use App\Models\Tenant;
use App\Models\Translation;
use App\Models\User;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use RecursiveDirectoryIterator;
use RecursiveIteratorIterator;
use SMTPValidateEmail\Validator as SmtpEmailValidator;
use Spatie\Activitylog\Models\Activity;
use Spatie\Browsershot\Browsershot;
use Spatie\Browsershot\Exceptions\CouldNotTakeBrowsershot;
use Stancl\Tenancy\Tenancy;

class DebugController extends Controller {

	public function index()
	{
		/*$this->deleteTenant(Tenant::first());
		$this->createTenant();*/
        
	}

	private function createTenant() {
		return Tenant::createTenant([
			'id'              => 'bozovic',
			'tenancy_db_name' => 'legaldesk_tenant_bozovic',
			'appName'        => 'Legaldesk.hr',
			'emailFromAddress' => '<EMAIL>',
			'emailFromName'    => 'Božović',
		], 'local.client.legaldesk.hr');
	}

	private function deleteTenant($tenant) {
		if($tenant){
			Tenant::deleteTenant($tenant);
		}
	}

	private function getFacebookReviewsCount($url): ?int {
		try {
			$pageContent = Browsershot::url($url)
			                          ->userAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.93 Safari/537.36')
			                          ->waitUntilNetworkIdle()
			                          ->bodyHtml();

			$pattern = '/(\d+)% recommend \((\d+) Reviews\)/';
			if (preg_match($pattern, $pageContent, $matches)) {
				return (int) $matches[2];
			} else {
				return null;
			}
		} catch (\Exception $e) {
			report($e);
			return null;
		}
	}


	private function formatName(string $name) : string
	{
		// split the name by hyphen if present
		$parts = explode('-', $name);

		// capitalize the first letter of each part and then join them back with a hyphen
		$formattedParts = array_map(function ($part) {
			// capitalize the first letter of each word in a part, ensuring multibyte support
			return mb_convert_case(mb_strtolower($part, 'UTF-8'), MB_CASE_TITLE, "UTF-8");
		}, $parts);

		return implode('-', $formattedParts);
	}

	public function spellCheck()
	{
		$text = 'PUNOMOĆ ZA DAVANJE NASLJEDNIČKE IZJAVE

kojom ja, Doroteja Kirhmajer-Vujčić, Dobri dol 54, 10000 Zagreb, Hrvatska, OIB: 80170463065, kao opunomoćitelj (u daljnjem tekstu: Opunomoćitelj) dajem ovlaštenje da me Slavko Kirhmajer, Vajdin vijenac 18, 10010 Zagreb, Hrvatska, OIB: 24469578513, brat Opunomoćitelja, kao opunomoćenik (u daljnjem tekstu: Opunomoćenik) zastupa vezano za prodaju nekretnine u mojem vlasništvu i to: suvlasničkog dijela 1/2 nekretnine upisane u zemljišnoj knjizi koju vodi Općinski sud u Novom Zagrebu, Zemljišnoknjižni odjel Novi Zagreb, katastarska općina 335495 ZAPRUDSKI OTOK, u zemljišnoknjižni uložak 20161, k. č. br. 1391/6, površine 1715 m2, oznake zemljišta Stambena zgrada, ZAGREB, Vajdin vijenac 16, 18, 20 i k. č. br. 1391/6, površine 1715 m2, oznake zemljišta Stambena zgrada, ZAGREB, Vajdin vijenac 16, 18, 20, 120. Suvlasnički dio s neodređenim omjerom ETAŽNO VLASNIŠTVO (E-120) stan broj 19 u VII (sedmom) katu koji se sastoji od dvije sobe, kuhinje, kupaonice sa WC-om, hodnika i degažmana u površini od 63,30 m2, Vajdin vijenac 18, koji je neodvojivo povezan sa odgovarajućim suvlasničkim dijelom cijele nekretnine koji je jednako velik kao i ostali suvlasnički dijelovi. 120.3. Suvlasnički dio etaže: 1/2 KIRHMAJER VUJČIĆ DOROTEJA, OIB: 80170463065, DOBRI DOL 54, 10000 ZAGREB 120.4. Suvlasnički dio etaže: 1/2 KIRHMAJER SLAVKO, OIB: 24469578513, VAJDIN VIJENAC 18, 10000 ZAGREB tako da može u moje ime i za moj račun poduzeti sljedeće pravne radnje: oglašavati predmetnu nekretninu i na druge načine tražiti kupca pokazivati predmetnu nekretninu potencijalnim kupcima samostalno odrediti kupoprodajnu cijenu predmetne nekretnine pregovarati s potencijalnim kupcima o sklapanju predugovora o kupoprodaji predmetne nekretnine, ugovora o kupoprodaji predmetne nekretnine i eventualnih dodataka (aneksa) tim ugovorima samostalno odabrati kupca pribaviti svu dokumentaciju potrebnu za sklapanje predugovora o kupoprodaji predmetne nekretnine, ugovora o kupoprodaji predmetne nekretnine i eventualnih dodataka tim ugovorima, kao što je zemljišnoknjižni izvadak, energetski certifikat, izvod iz matične knjige, potvrde komunalnih društava za priključke koji postoje na predmetnoj nekretnini, itd. poduzeti sve potrebne radnje za provedbu usklađivanja stanja u katastru nekretnina sa zemljišnoknjižnim stanjem za predmetnu nekretninu s odabranim kupcem potpisati predugovor o kupoprodaji predmetne nekretnine, ugovor o kupoprodaji predmetne nekretnine i eventualne dodatke (anekse) tim ugovorima i na njima ovjeriti potpis kod javnog bilježnika primiti iznos kapare u gotovini ili na vlastiti bankovni račun primiti isplatu kupoprodajne cijene u gotovini ili na vlastiti bankovni račun izdati kupcu tabularnu izjavu tj. dopuštenje kupcu za uknjižbu prava vlasništva i na njoj ovjeriti potpis kod javnog bilježnika predati predmetnu nekretninu u posjed kupcu i potpisati primopredajni zapisnik ishoditi promjenu korisnika javnih i drugih usluga koje se odnose na predmetnu nekretninu koja je predmet ugovora o kupoprodaji kod svih komunalnih društava, energetskih distributera i drugih nadležnih službi, kao što su struja, voda, odvoz smeća, zajednička pričuva, čišćenje stubišta, itd. poduzeti sve druge pravne radnje vezane za realizaciju prodaje predmetne nekretnine. Ova Punomoć vrijedi do opoziva. Zagreb, dana 17. 2. 2024. (slovima: sedamnaestog veljače dvije tisuće dvadeset četvrte) godine.';

		$clean_text = strip_tags($text);

		$result = SpellChecker::analyze($clean_text);

		dd($result);

		if($result['success']) {
			$response = $result['data']['response'];

			$error_count = $response['errors'];

			if($error_count) {
				$errors = $response['error'];

				foreach($errors as $_error) {
					$_error_suspicious = $_error['suspicious'];
					$_error_class = $_error['class'];

					$_error_header = $_error['header'];
					$_error_message = $_error['message'];
					$_error_suggestions = $_error['suggestions'];


					$replacement = '<span class="spelling-error" data-class="'.htmlspecialchars($_error_class).'" data-header="'.htmlspecialchars($_error_header).'" data-message="'.htmlspecialchars($_error_message).'" data-suggestions="'.htmlspecialchars(json_encode($_error_suggestions)).'">' . htmlspecialchars($_error_suspicious). '</span>';

					$text = str_replace($_error_suspicious, $replacement, $text);
				}

				echo $text; exit;
			}
		} else {
			// handle error
		}
	}

	public function anonymizeArticleNumbers($text)
	{
		$pattern = '/(članak|članku|članka) (\d+)(\.?) ovog/i';
		$replacement = '$1 [X]$3 ovog Ugovora';
		return preg_replace($pattern, $replacement, $text);
	}



	private function downloadDashedDocument() {
		// Retrieve the latest Document
		$document = Document::latest()->first();

		$db = new DocumentBuilder($document, true);

		$html = $db->generateNewContent();

		$dasher = new DocumentDasher($html);
		return $dasher->download();
	}

	private function downloadDashedDraft() {
		// Retrieve the latest Draft
		$document = DocumentDraft::latest()->first();

		// Get HTML content from the draft
		$html = $document->html;

		$dasher = new DocumentDasher($html);
		return $dasher->download();
	}

	public function screenshot()
	{
		$screenshot = new Screenshot();
		return $screenshot->snap('https://google.com', public_path('google'));
	}

	// acf field  related_document | related_examples
	private function relatedACFPosts($post_id, $acf_field): array
	{
		return DB::connection('wordpress') // Replace with your WordPress DB connection
		         ->table('postmeta')
		         ->join('posts', 'posts.ID', '=', 'postmeta.post_id')
		         ->where('postmeta.meta_key', '=', $acf_field)
		         ->where('postmeta.meta_value', 'LIKE', '%"'.$post_id.'"%')
		         ->where('posts.post_status', '=', 'publish') // only published posts
		         ->where('posts.post_type', '=', 'example') // only posts of post type "example"
		         ->pluck('posts.ID')
		         ->toArray();
	}

	private function pdf2img() {
		shell_exec('pdf2image/pdf2image -o pdf2image/test_out -d 150 pdf2image/test/document.pdf');
		$images = collect(Storage::disk('pdf2image')->files('test_out'))->map(function($image){ return public_path("/pdf2image/$image");})->toArray();
		$result = shell_exec('img2pdf --pagesize A4 '. implode(' ', $images) . ' -o document.pdf');
		dd($result);
	}

	private function validateSMTP($email) {
		/**
		 * Simple example
		 */
		$sender    = '<EMAIL>';
		$validator = new SmtpEmailValidator($email, $sender);

		// If debug mode is turned on, logged data is printed as it happens:
		$validator->debug = true;
		$results   = $validator->validate();

		var_dump($results);
	}

	private function curl()
	{
		// create curl resource
		$ch = curl_init();

		// set url
		curl_setopt($ch, CURLOPT_URL, "http://elasticsearch:9200/templates-local/_search?q=motor");

		//return the transfer as a string
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);

		// $output contains the output string
		$output = curl_exec($ch);

		// close curl resource to free up system resources
		curl_close($ch);

		return $output;
	}


	private function impersonate(User $user) {

		auth()->login($user);
	}

	private function getSectionInputs($doc, $template) {

		$section_inputs      = [];
		$ignored_input_types = ['hidden', 'submit']; // _token, submit, etc.
		$ignored_input_names = ['{ID}'];

		foreach ($template->sections as $_section) {
			$sectionFormModel = new SectionFormModel($doc, $_section);
			$sectionFormModel->setupView();

			try {
				$html = view($sectionFormModel->view)->with(
					[
						'model' => $sectionFormModel,
						'js'    => $sectionFormModel->js,
						'route' => route('section.store', [$doc, $_section])
					]
				)->render();

				$document = new \DOMDocument();
				libxml_use_internal_errors(true);   // suppress invalid html element errors (html5 incompatibility)
				$document->loadHTML($html);

				// only traverse form-container scope
				$document = $document->getElementById('form-container');

				// get all inputs
				$inputs = $document->getElementsByTagName("input");

				foreach ($inputs as $input) {
					$input_name = $input->getAttribute("name");

					// only take the part before array markup (if multi field)
					$input_name = trim(explode('[', $input_name)[0]);

					if (
						! empty($input_name) // not empty
						&& ! in_array($input->getAttribute("type"), $ignored_input_types)   // input type valid
						&& ! in_array($input_name, $ignored_input_names)   // input name valid
						&& ! in_array($input_name, $section_inputs)  // not already saved

					) {
						$section_inputs[] = $input_name;
					}
				}

				// get all textareas
				$textareas = $document->getElementsByTagName("textarea");

				foreach ($textareas as $input) {
					$input_name = $input->getAttribute("name");

					// only take the part before array markup (if multi field)
					$input_name = trim(explode('[', $input_name)[0]);

					if (
						! empty($input_name) // not empty
						&& ! in_array($input_name, $ignored_input_names)   // input name valid
						&& ! in_array($input_name, $section_inputs)  // not already saved

					) {
						$section_inputs[] = $input_name;
					}
				}

			} catch (\Throwable $e) {
				dd($e);
			}
		}

		return $section_inputs;
	}


}

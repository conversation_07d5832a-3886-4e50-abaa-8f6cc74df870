<?php

namespace App\Rules;


use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use TimeHunter\LaravelGoogleReCaptchaV3\Facades\GoogleReCaptchaV3;
use TimeHunter\LaravelGoogleReCaptchaV3\Validations\GoogleReCaptchaV3ValidationRule;

class GoogleReCaptchaValidationRule extends GoogleReCaptchaV3ValidationRule
{

	/**
	 * @param  string  $attribute
	 * @param  mixed  $value
	 * @return bool
	 */
	public function passes($attribute, $value)
	{
		// auto pass for dusk testing
		if(config('app.dusk_test') === true) {
			return true;
		}

		$response = GoogleReCaptchaV3::setAction($this->action)->verifyResponse($value, app('request')->getClientIp());
		$this->message = $response->getMessage();

		if(!$result = $response->isSuccess()) {
			Log::info('GoogleReCaptchaV3 failed on action: ' . $this->action . ', user: ' . (Auth::user() ? Auth::user()->email : 'guest'));
		}

		return $result;
	}
}

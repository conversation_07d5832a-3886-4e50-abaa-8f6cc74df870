<?php

namespace App\Policies;

use App\Models\Document;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class DocumentPolicy
{
    use HandlesAuthorization;

    /**
     * Create a new policy instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Before each method, perform custom checks
     * @param $user
     * @param $ability
     */
    public function before($user, $ability)
    {

    }

	/**
	 * Determine if document can be updated by current user
	 *
	 * @param User|null $user
	 * @param Document $document
	 *
	 * @return bool
	 */
    public function update(?User $user, Document $document): bool {
        if($user && $document->user_id === $user->id){
            return true;
        }

        return false;
    }
}

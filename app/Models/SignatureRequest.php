<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

class SignatureRequest extends Model {

	use SoftDeletes;

	private const EXPIRATION_DAYS = 5;

	protected $fillable = ['document_id', 'party_id', 'link', 'email', 'is_signed', 'is_active'];

	public function document() {

		return $this->belongsTo('App\Models\Document', 'document_id');
	}

	public function party() {

		return $this->belongsTo('App\Models\DocumentParty', 'party_id');
	}

	public function belongsToUser($user) {

		return $user->email === $this->email;
	}

	public static function getExpirationDays() {

		return self::EXPIRATION_DAYS;
	}

	public function isExpired() {

		$to   = Carbon::now();
		$from = Carbon::createFromFormat('Y-m-d H:s:i', $this->created_at);

		return $to->diffInDays($from) >= $this->expiration_days;
	}
}

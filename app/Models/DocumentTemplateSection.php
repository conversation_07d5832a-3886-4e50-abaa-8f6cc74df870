<?php

namespace App\Models;

use GeneaLabs\LaravelModelCaching\Traits\Cachable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Mtvs\EloquentHashids\HasHashid;
use Mtvs\EloquentHashids\HashidRouting;

class DocumentTemplateSection extends Model {

	use SoftDeletes;
	use Cachable;

	use HasHashid, HashidRouting;

	public function template() {

		return $this->belongsTo('App\Models\DocumentTemplate', 'template_id', 'id');
	}

	public function values($document_id, $exists = false) {

		$values = $this->hasOne('App\Models\DocumentTemplateSectionValue')
		            ->where('document_id', $document_id);

		return $exists ? $values->exists() : $values->first();
	}

	public function showMissingDataWarning($values, $is_edit, $current_section_order_index): bool {

		// show warning if we're editing and section has never been saved or there's missing data
		if($is_edit) {
			return empty($values) || !empty($values->missing_data);
		} else {
			// or if we're creating document and section has missing data, or has a lower order_index than current section
			if(!empty($values)) {
				return !empty($values->missing_data);
			} else {
				return $this->order_index < $current_section_order_index;
			}
		}

	}

}

<?php

namespace App\Models;

use GeneaLabs\LaravelModelCaching\Traits\Cachable;
use Illuminate\Database\Eloquent\Model;

class DocumentCategory extends Model {

	use Cachable;

	// important for laravel scout
	protected $touches = ['templates'];

	public function templates() {
		return $this->belongsToMany('App\Models\DocumentTemplate', 'document_categories_templates',
			'category_id', 'template_id')->where('is_visible', true)->orderByPivot('order_index');
	}

	/**
	 * @param $title
	 * @param null $order_index
	 *
	 * @return DocumentCategory|null
	 */
	public static function createNew($title, $order_index = null) {

		$model              = new self();
		$model->title       = $title;
		$model->order_index = $order_index;
		if ($model->save()) {
			return $model;
		}

		return null;
	}

}

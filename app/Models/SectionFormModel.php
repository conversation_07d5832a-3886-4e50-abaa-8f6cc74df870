<?php
/**
 * Created by PhpStorm.
 * User: dinocagalj
 * Date: 19/09/2018
 * Time: 00:17
 */

namespace App\Models;

use App\Helpers\DocumentBuilder;
use Illuminate\Support\Str;

class SectionFormModel {

	public $js;
	public $view;
	public $is_edit;
	public $section;
	public $document;
	public $section_id;
	public $document_id;
	public $missing_data;
	public $preview_html;
	public $next_section;
	public $next_section_url;

	public function __construct($document, $section) {

		$this->section     = $section;
		$this->document    = $document;

		$this->section_id  = $section->hashid();
		$this->document_id = $document->hashid();

		if ($next_section = $this->document->template->nextSection($this->section->order_index)) {
			$this->next_section     = $next_section;
            $this->next_section_url = route('tenant.section.show', [$this->document, $next_section]);
		}
	}

	public function setupView(): void {

		$this->populateSectionValues();
		$this->loadPreviewHtml();

        if(tenancy()->initialized) {
            $tenant = tenancy()->tenant->id;
            $this->view = "tenant.documents.{$tenant}.{$this->document->template->title}.{$this->section->view}";
            $this->js   = "/js/documents/{$tenant}/{$this->document->template->title}/{$this->section->view}.js";

        } else {
            $this->view = "tenant.documents.{$this->document->template->title}.{$this->section->view}";
            $this->js   = "/js/documents/{$this->document->template->title}/{$this->section->view}.js";
        }


	}

	private function loadPreviewHtml(): void {
		$document_builder = new DocumentBuilder($this->document, $this->section->view);
        $document_builder->setInitialPreview();

		$this->preview_html = $document_builder
			->setLayout($document_builder->preview_layout)  // set preview layout
			->getPreviewContent();
	}

	private function populateSectionValues(): void {

		if ($section_values = $this->document->sectionValues->where('document_template_section_id', $this->section->id)->first()) {

			// section already has saved data
			$this->is_edit = true;

			$section_data = json_decode($section_values->data, true);
			// instantiate and populate attributes
			foreach ($section_data as $_field_name => $_field_values) {
				$this->{$_field_name} = $_field_values;
			}

			// populate missing data
			$this->missing_data = json_decode($section_values->missing_data, true);

		} else {

			// section is new
			$this->is_edit = false;

			// if document is already visible, but section values are not yet created
			if($this->document->is_visible) {
				$this->missing_data = '*';
			}
		}
	}

	public function save($data, $is_preview) {

		$section_value = DocumentTemplateSectionValue::firstOrNew([
			'document_template_section_id' => $this->section->id,
			'document_id'                  => $this->document->id,
		]);

		// handle document parties (if present)
		if (isset($data['parties'])) {
			$parties = json_decode($data['parties'], true);
			DocumentParty::whereDocumentId($this->document->id)->delete();

			foreach ($parties as $_party) {
				if ( ! empty($_party['name'])) {
					DocumentParty::create([
						'document_id' => $this->document->id,
						'name'        => $_party['name'],
						'label'       => $_party['label'],
						'side'        => $_party['side'],
						'is_signee'        => $_party['isSignee'] ?? true,
					]);
				}
				else {
                    DocumentParty::create([
                        'document_id' => $this->document->id,
                        'name'        => "",
                        'label'       => trim(Str::replace(['() <br>  -', '()'], "", $_party['label'])) ,
                        'side'        => $_party['side'],
                        'is_signee'        => $_party['isSignee'] ?? true,
                    ]);
                }
			}

			unset($data['parties']);
		}

		// set missing data
		if(isset($data['missing_data'])) {
			$section_value->missing_data = json_encode($data['missing_data']);
			unset($data['missing_data']);
		} else {
			$section_value->missing_data = null;
		}

		// set data
		$section_value->data = json_encode($data);
		$result = $section_value->save();


		// if visible (saved) document OR about to become visible
		if ($this->document->is_visible || (!$this->next_section && !$is_preview)) {
			// create document storage directory
			$this->document->createStorageDirectory();
			// save html
			$this->document->saveHtml();
			// save pdf
			$this->document->savePdf();
		}


		return $result;
	}

    public function get($name) {
        // convert the bracket notation to dot notation
	    $name = preg_replace('/\[(.*?)\]/', '.$1', $name);
	    return data_get($this, $name);
    }

	public function getSavedSectionValue($section_index, $field) {

		$section = $this->document->template->sections[$section_index];

		if ($section && $values = $section->values($this->document->id)) {

			$values = json_decode($values->data, true);

			return $values[$field] ?? null;
		}

		return null;
	}

	public function isChecked($name, $value, $attributes = []) {
		if(isset($attributes['checked'])) {
			return $attributes['checked'];
		}

		$value = trim($value);
		$name = rtrim($name, '[]');

		if($this->get($name) !== null) {
			$saved_value = $this->get($name);
			if(is_array($saved_value)) {
				return in_array($value, $saved_value);
			} else {
				return $value == $saved_value;
			}
		}

		return false;
	}

}

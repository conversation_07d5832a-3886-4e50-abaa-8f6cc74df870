<?php

namespace App\Models;

use App\Notifications\ResetPassword;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\Activitylog\Models\Activity;

class User extends Authenticatable {

	use Notifiable;
	use SoftDeletes;

	/**
	 * The attributes that are mass assignable.
	 *
	 * @var array
	 */
	protected $fillable = [
		'name',
		'email',
		'password',
        'auth_id',
        'auth_type',
		'otp_code',
		'otp_expires_at',
	];

	/**
	 * The attributes that should be hidden for arrays.
	 *
	 * @var array
	 */
	protected $hidden = [
		'password',
		'remember_token',
		'otp_code',
	];

	/**
	 * The attributes that should be cast.
	 *
	 * @var array<string, string>
	 */
	protected $casts = [
		'otp_expires_at' => 'datetime',
		'password' => 'hashed',
	];

	public function activities()
	{
		return $this->morphMany(Activity::class, 'subject');
	}

	public function sendPasswordResetNotification($token) {
		try{
            $resetPasswordUrl = route('tenant.password.reset', ['token' => $token, 'email' => $this->email]);
			$this->notify(new ResetPassword($token, $resetPasswordUrl));
		} catch(\Exception $e) {
			report($e);
		}
	}

	public function options() {
		return $this->hasOne('App\Models\UserOptions');
	}

	public function documents() {
		return $this->hasMany('App\Models\Document')->where('is_visible', '=', true);
	}

	public function documentDrafts() {
		return $this->hasMany('App\Models\DocumentDraft')->where('is_visible', '=', true);
	}

    public function emailPreference() {
        return $this->hasOne('App\Models\EmailPreference', 'email', 'email');
    }

	public function isDocumentOwner(Document $document) {
		return $this->id === $document->user_id;
	}

	public function isAdmin() {
		return $this->options && $this->options->is_admin;
	}

	public static function getAdminEmails() {
		return User::whereRelation('options', 'is_admin', '=', 1)->select('email')->get()->map(function($user) {
			return $user->email;
		})->toArray();
	}

	public function shouldShowEditorTutorial() {
		return $this->options && !$this->options->is_editor_tutorial_shown;
	}

	public static function shouldShowWizardTutorial() {
		if (auth()->check()) {
			$user_options = auth()->user()->options;
			return $user_options && !$user_options->is_wizard_tutorial_shown && !\Cookie::has('wizardTutorialShown');
		} else {
			return !\Cookie::has('wizardTutorialShown');
		}
	}

}

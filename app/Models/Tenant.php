<?php

namespace App\Models;

use Stancl\Tenancy\Database\Models\Tenant as BaseTenant;
use Stancl\Tenancy\Contracts\TenantWithDatabase;
use Stancl\Tenancy\Database\Concerns\HasDatabase;
use Stancl\Tenancy\Database\Concerns\HasDomains;

class Tenant extends BaseTenant implements TenantWithDatabase
{
	use HasDatabase, HasDomains;

	public static function createTenant($data, $domain) {

		$tenant = Tenant::create($data);
		$tenant->domains()->create(['domain' => $domain]);

		return $tenant;

	}

	public static function deleteTenant($tenant) {
		$tenant->domains()->delete();
		$tenant->delete();
	}
}
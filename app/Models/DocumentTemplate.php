<?php

namespace App\Models;

use App\Helpers\ElasticHelper;
use Corcel\Model\Post;
use Illuminate\Database\Eloquent\Concerns\HasEvents;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;

class DocumentTemplate extends Model
{

    use SoftDeletes;
    use HasEvents;

	public static $types = [
		'contract' => 1,
		'statement' => 2
	];

	public function getType() {
		return array_flip(self::$types)[$this->type_id];
	}

    public static function search($query, $from = 0, $size = null)
    {
        $query = ElasticHelper::prepareInput($query);

        if ($query) {
            $es = new ElasticHelper();

            $results = $es->search('template', $query, $size, $from);

            if (!empty($results['hits']['hits'])) {
                $ids = [];
                foreach ($results['hits']['hits'] as $_hit) {
                    $ids[] = $_hit['_source']['id'];
                }

	            $order = sprintf('FIELD(id, %s)', implode(',', $ids));

	            return self::whereIn('id', $ids)->with(['category', 'categoryTemplate'])->orderByRaw($order)->get();

            }

            return [];
        }

        return self::where('is_visible', 1)->with(['category', 'categoryTemplate'])->get();
    }


    public function category()
    {

        return $this->hasOneThrough(
            'App\Models\DocumentCategory',
            'App\Models\DocumentCategoryTemplate',
            'template_id',
            'id',
            'id',
            'category_id'
        );
    }

    public function categoryTemplate()
    {

        return $this->hasOne(
            'App\Models\DocumentCategoryTemplate',
            'template_id'
        );
    }

    public function sections()
    {

        return $this->hasMany('App\Models\DocumentTemplateSection', 'template_id', 'id')->orderBy('document_template_sections.order_index');
    }

    public function documents()
    {

        return $this->hasMany('App\Models\Document');
    }

    public function firstSection()
    {

        return $this->sections[0];
    }

    public function isPrecontract()
    {

        return !empty($this->precontract_for_id);
    }

    public function nextSection($order_index)
    {

        foreach ($this->sections as $_section) {
            if ($_section->order_index > $order_index) {
                return $_section;
            }
        }

        return null;
    }

    // data for widget on document blog/pages
    public static function getDocumentWidgetContent($post, $slug)
    {

        if ($post) {
            if ($model = self::where('slug', '=', $slug)->first()) {
                return [
                    'template_id' => $model->id,
                    'document_template_public_title' => $model->public_title,
                    'updated_at' => $model->updated_at,
                    'time_to_fill' => $model->time_to_fill,
                    'format' => 'PDF',
                    'author' => 'Jasmina Mutabžija',
                    'author_page' => url('/jasmina-mutabzija'),
                ];
            }
        }

        return null;
    }

    /**
     * @param $title
     * @param $public_title
     * @param $tags
     * @param $slug
     * @param $time_to_fill
     *
     * @return DocumentTemplate|bool
     */
    public static function createNew($title, $public_title, $tags, $slug, $time_to_fill)
    {

        $template = new self();
        $template->title = $title;
        $template->public_title = $public_title;
        $template->tags = $tags;
        $template->slug = $slug;
        $template->time_to_fill = $time_to_fill;

        if ($template->save()) {
            return $template;
        } else {
            return null;
        }

    }

    public function assignCategory($category, $order_index = null): bool {

        if ($category) {
            if (DocumentCategoryTemplate::createNew(
                $this->id,
                $category->id,
                $order_index
            )) {
                return true;
            }
        }

        return false;
    }

	public function isContract() {
		return $this->type_id === self::$types['contract'];
	}

	public function isStatement() {
		return $this->type_id === self::$types['statement'];
	}

	public function landingPage() {
		return Post::type('document')->slug($this->slug);
	}
}

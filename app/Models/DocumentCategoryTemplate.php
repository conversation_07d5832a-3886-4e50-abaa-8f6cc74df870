<?php

namespace App\Models;

use GeneaLabs\LaravelModelCaching\Traits\Cachable;
use Illuminate\Database\Eloquent\Model;

class DocumentCategoryTemplate extends Model {

	use Cachable;

	protected $table = 'document_categories_templates';

	// important for laravel scout
	protected $touches = ['template'];

	public function template() {

		return $this->belongsTo('App\Models\DocumentTemplate', 'template_id');
	}

	public static function createNew($template_id, $category_id, $order_index = null) {

		$model              = new self();
		$model->template_id = $template_id;
		$model->category_id = $category_id;
		$model->order_index = $order_index;

		if ($model->save()) {
			return $model;
		}

		return null;

	}
}

<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

use App\Http\Controllers\ContactController;
use App\Http\Controllers\DebugController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\Tenant\UserController;
use Illuminate\Support\Facades\Route;

// Controller Imports

// only allow in local environment
Route::group(['middleware' => ['local']], function () {
	Route::any('/debug', [DebugController::class, 'index']);
});

// home route
Route::get('/', [HomeController::class, 'index'])->name('home');

// signed routes
Route::group(['middleware' => ['signed']], function () {
    // contact files
    Route::get('/contact/{id}/file/{filename}', [ContactController::class, 'getFile'])->name('contact.file');
});

// throttled routes
Route::group(['middleware' => ['throttle']], function () {

    // contact routes
    Route::get('/kontakt', [ContactController::class, 'index'])->name('contact');
    Route::post('/kontakt', [ContactController::class, 'store'])->name('contact.store')->middleware(\Spatie\Honeypot\ProtectAgainstSpam::class);

});

// save cookie preferences
Route::post('/cookiePreferences', [UserController::class, 'setCookiePreferences'])->name('cookie.preferences');
